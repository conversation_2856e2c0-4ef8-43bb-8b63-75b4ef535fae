package com.ottogroup.appkit.tracking

import com.ottogroup.appkit.tracking.event.View
import com.ottogroup.appkit.tracking.event.View.Screen
import com.ottogroup.appkit.tracking.uri.UrlMatcher

public data class OGTrackingConfig(
    /**
     * General Feature toggle to configure if tracking is enabled or not.
     */
    val trackingEnabled: Boolean,
    /**
     *
     */
    val logLevel: LogLevel,
    /**
     * Mapping of View.Screen Events to their respective URL Matchers.
     * This is used to determine which events to track based on the current URL.
     * In order to have automatic tracking of screen views, the loaded url need to be passed to [OGTracking].onDidLoad(url: String).
     * @see [View.Screen]
     * @see [UrlMatcher]
     * @see OGTracking.onDidLoad
     */
    val viewEventMapping: Map<Screen, List<UrlMatcher>>,

    /**
     * If set to true, the SDK will observe overlays and retrigger the screen events after overlays are dismissed.
     * This works out of the box for android. For iOS, the app needs to call [OverlayHandler.onShowOverlay] and [OverlayHandler.onDismissOverlay] when overlays are shown and dismissed.
     */
    val observeOverlaysForScreenEvents: Boolean = false,
)

public enum class LogLevel {
    Verbose,
    Debug,
    Info,
    Warn,
    Error,
    Assert
}
