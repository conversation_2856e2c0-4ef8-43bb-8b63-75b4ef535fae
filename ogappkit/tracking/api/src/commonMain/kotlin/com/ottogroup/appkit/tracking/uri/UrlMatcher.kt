package com.ottogroup.appkit.tracking.uri

import io.ktor.http.Url
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder

/**
 * This class is used to match URLs based on a provided pattern.
 * The pattern can be a regular expression, a host, or a path.
 *
 * The pattern can be defined in several ways:
 * 1. **Regular Expression**: If the pattern starts with "regex:", the rest of the string is used as a regular expression for matching URLs.
 * 2. **Host**: If the pattern starts with "host:", the rest of the string is used to match the host part of the URL. If the host pattern starts with "*.", it matches any subdomain.
 * 3. **URL**: If the pattern is a valid URL, it is used directly for matching.
 * 4. **Path**: If the pattern does not fall into any of the above categories, it is used to match the path part of the URL.
 *
 * @property pattern The pattern used for matching URLs.
 */
@OptIn(ExperimentalSerializationApi::class)
@Serializable(with = UrlMatcherSerializer::class)
public data class UrlMatcher(
    // public for serialization
    val pattern: String
) {
    private val matcher: (Url) -> Boolean

    init {
        when {
            pattern.startsWith("regex:") -> {
                matcher = { url ->
                    url.toString().matches(Regex(pattern.removePrefix("regex:")))
                }
            }

            pattern.startsWith("host:") -> {
                val hostExpression = pattern.removePrefix("host:")
                val regex = if (hostExpression.startsWith("*.")) {
                    Regex("(.+\\.)?" + Regex.escape(hostExpression.removePrefix("*.")))
                } else {
                    Regex(Regex.escape(hostExpression))
                }
                matcher = { url ->
                    // also allow subdomains of this host
                    url.host.matches(regex)
                }
            }

            pattern.toUrl()?.protocol?.name != null -> {
                matcher = { url ->
                    url.toString().matches(Regex(pattern))
                }
            }

            else -> {
                matcher = { url ->
                    url.encodedPath.removePrefix("/").matches(Regex(pattern))
                }
            }
        }
    }

    internal fun matchesUrl(url: Url): Boolean {
        return matcher(url)
    }
}

internal fun UrlMatcher.matchesUrl(url: Url?) = url?.let { matchesUrl(it) } == true

@ExperimentalSerializationApi
public class UrlMatcherSerializer : KSerializer<UrlMatcher> {
    override val descriptor: SerialDescriptor = PrimitiveSerialDescriptor(
        "com.ottogroup.ogkit.tracking.uri.UrlMatcherSerializer",
        PrimitiveKind.STRING
    )

    override fun deserialize(decoder: Decoder): UrlMatcher {
        return UrlMatcher(decoder.decodeString())
    }

    override fun serialize(encoder: Encoder, value: UrlMatcher) {
        encoder.encodeString("regex:" + value.pattern)
    }
}
