package com.ottogroup.appkit.tracking.event

import kotlinx.serialization.Serializable

/**
 * The Interaction event represents an interaction (e.g. click) on an interactive element.
 * This event is used for all types of clicks and interactions.
 * The base interaction may include the following parameters:
 * - category: the category of the interaction
 * - scenario: the scenario in which the interaction occurred
 * - detail: the detail of the interaction
 * - label: the label of the interaction
 * All detailed Interaction events inherit the parameters from this event.
 */
@Serializable
public sealed class Interaction(
    public val category: String,
    public val scenario: String?,
    public val detail: String,
    public val label: String?
) : OGEvent {

    override fun describe(): Map<String, Any?> = super.describe() + mapOf(
        "category" to category,
        "scenario" to scenario,
        "detail" to detail,
        "label" to label
    )

    /**
     * selected menu entry on Account screen
     * trigger: user interaction
     * @param entryLabel: the entry label
     */
    @Serializable
    public data class AccountMenuEntry(
        public val entryLabel: String
    ) : Interaction(
        category = "Account",
        scenario = null,
        label = entryLabel,
        detail = "MenuEntry",
    )

    /**
     * tapped “login” button on Account Screen
     * trigger: user interaction
     */
    @Serializable
    public data object AccountLogin : Interaction(
        category = "Account",
        scenario = null,
        detail = "Login",
        label = null
    )

    /**
     * selected category on Assortment Overview
     * trigger: user interaction
     * @param categoryLabel: the category label
     */
    @Serializable
    public data class CategoryMenuEntry(
        public val categoryLabel: String
    ) : Interaction(
        category = "CategoryOverview",
        scenario = null,
        label = categoryLabel,
        detail = "Category",
    )

    /**
     * selected banner, displayed in category menu
     * trigger: user interaction
     * @param categoryLabel: the category label
     *
     */
    @Serializable
    public data class CategoryBannerEntry(
        public val categoryLabel: String
    ) : Interaction(
        category = "CategoryOverview",
        scenario = null,
        label = categoryLabel,
        detail = "Banner",
    )

    /**
     * selected secondary category menu entry
     * trigger: user interaction
     * @param categoryLabel: the category label
     */
    @Serializable
    public data class CategorySubMenuEntry(
        public val categoryLabel: String
    ) : Interaction(
        category = "CategoryOverview",
        scenario = null,
        label = categoryLabel,
        detail = "MenuEntry",
    )

    /**
     * selected Home bottom navigation entry
     * trigger: user interaction
     */
    @Serializable
    public data object HomeBottomNavigationEntry : Interaction(
        category = "Navigation",
        scenario = null,
        detail = "SelectTab",
        label = "Home"
    )

    /**
     * selected Assortment bottom navigation entry
     * trigger: user interaction
     */
    @Serializable
    public data object AssortmentBottomNavigationEntry : Interaction(
        category = "Navigation",
        scenario = null,
        detail = "SelectTab",
        label = "Assortment"
    )

    /**
     * selected Wishlist bottom navigation entry
     * trigger: user interaction
     */
    @Serializable
    public data object WishlistBottomNavigationEntry : Interaction(
        category = "Navigation",
        scenario = null,
        detail = "SelectTab",
        label = "Wishlist"
    )

    /**
     * selected Cart bottom navigation entry
     * trigger: user interaction
     */
    @Serializable
    public data object CartBottomNavigationEntry : Interaction(
        category = "Navigation",
        scenario = null,
        detail = "SelectTab",
        label = "Cart"
    )

    /**
     * selected Account bottom navigation entry
     * trigger: user interaction
     */
    @Serializable
    public data object AccountBottomNavigationEntry : Interaction(
        category = "Navigation",
        scenario = null,
        detail = "SelectTab",
        label = "Account"
    )

    /**
     * selected back action
     * trigger: user interaction
     */
    @Serializable
    public data object Back : Interaction(
        category = "Navigation",
        scenario = null,
        detail = "Back",
        label = null
    )

    /**
     * tapped “okay” button on Push Promotion Layer displayed in Onboarding scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PPLConfirmOnboarding : Interaction(
        category = "PushInfoLayer",
        scenario = "Onboarding",
        detail = "Okay",
        label = null
    )

    /**
     * tapped “okay” button on Push Promotion Layer displayed in ScreenViews scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PPLConfirmScreenViews : Interaction(
        category = "PushInfoLayer",
        scenario = "ScreenViews",
        detail = "Okay",
        label = null
    )

    /**
     * tapped “okay” button on Push Promotion Layer displayed in OrderConfirmation scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PPLConfirmOrderConfirmation : Interaction(
        category = "PushInfoLayer",
        scenario = "OrderConfirmation",
        detail = "Okay",
        label = null
    )

    /**
     * tapped “okay” button on Push Promotion Layer displayed in WebBridge scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PPLConfirmWebBridge : Interaction(
        category = "PushInfoLayer",
        scenario = "PromotionWebBridge",
        detail = "Okay",
        label = null
    )

    /**
     * tapped “Close” button on Push Promotion Layer displayed in Onboarding scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PPLCloseOnboarding : Interaction(
        category = "PushInfoLayer",
        scenario = "Onboarding",
        detail = "Close",
        label = null
    )

    /**
     * tapped “Close” button on Push Promotion Layer displayed in ScreenViews scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PPLCloseScreenViews : Interaction(
        category = "PushInfoLayer",
        scenario = "ScreenViews",
        detail = "Close",
        label = null
    )

    /**
     * tapped “Close” button on Push Promotion Layer displayed in OrderConfirmation scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PPLCloseOrderConfirmation : Interaction(
        category = "PushInfoLayer",
        scenario = "OrderConfirmation",
        detail = "Close",
        label = null
    )

    /**
     * tapped “Close” button on Push Promotion Layer displayed in WebBridge scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PPLCloseWebBridge : Interaction(
        category = "PushInfoLayer",
        scenario = "PromotionWebBridge",
        detail = "Close",
        label = null
    )

    /**
     * tapped “Yes” button on Push Promotion Layer displayed in Inbox scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PPLConfirmInbox : Interaction(
        category = "PushInfoLayer",
        scenario = "Inbox",
        detail = "Okay",
        label = null
    )

    /**
     * tapped “Close” button on Push Promotion Layer displayed in Inbox scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PPLCloseInbox : Interaction(
        category = "PushInfoLayer",
        scenario = "Inbox",
        detail = "Close",
        label = null
    )

    /**
     * tapped “confirm” button on Push System Dialog displayed in Inbox scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PushSystemConfirmInbox : Interaction(
        category = "PushOptIn",
        scenario = "Inbox",
        detail = "Okay",
        label = null
    )

    /**
     * tapped “decline” button on Push System Dialog displayed in Inbox scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PushSystemCloseInbox : Interaction(
        category = "PushOptIn",
        scenario = "Inbox",
        detail = "Close",
        label = null
    )

    /**
     * tapped “confirm” button on Push System Dialog displayed in Onboarding scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PushSystemConfirmOnboarding : Interaction(
        category = "PushOptIn",
        scenario = "Onboarding",
        detail = "Confirm",
        label = null
    )

    /**
     * tapped “confirm” button on Push System Dialog displayed in ScreenViews scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PushSystemConfirmScreenViews : Interaction(
        category = "PushOptIn",
        scenario = "ScreenViews",
        detail = "Confirm",
        label = null
    )

    /**
     * tapped “confirm” button on Push System Dialog displayed in OrderConfirmation scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PushSystemConfirmOrderConfirmation : Interaction(
        category = "PushOptIn",
        scenario = "OrderConfirmation",
        detail = "Confirm",
        label = null
    )

    /**
     * tapped “confirm” button on Push System Dialog displayed in WebBridge scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PushSystemConfirmWebBridge : Interaction(
        category = "PushOptIn",
        scenario = "PromotionWebBridge",
        detail = "Confirm",
        label = null
    )

    /**
     * tapped “confirm” button on Push System Dialog displayed in Account scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PushSystemConfirmAccount : Interaction(
        category = "PushOptIn",
        scenario = "Account",
        detail = "Confirm",
        label = null
    )

    /**
     * tapped “Decline” button on Push System Dialog displayed in Onboarding scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PushSystemCloseOnboarding : Interaction(
        category = "PushOptIn",
        scenario = "Onboarding",
        detail = "Decline",
        label = null
    )

    /**
     * tapped “Decline” button on Push System Dialog displayed in ScreenViews scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PushSystemCloseScreenViews : Interaction(
        category = "PushOptIn",
        scenario = "ScreenViews",
        detail = "Decline",
        label = null
    )

    /**
     * tapped “Decline” button on Push System Dialog displayed in OrderConfirmation scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PushSystemCloseOrderConfirmation : Interaction(
        category = "PushOptIn",
        scenario = "OrderConfirmation",
        detail = "Decline",
        label = null
    )

    /**
     * tapped “Decline” button on Push System Dialog displayed in Account scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PushSystemCloseAccount : Interaction(
        category = "PushOptIn",
        scenario = "Account",
        detail = "Decline",
        label = null
    )

    /**
     * tapped “Decline” button on Push System Dialog displayed in WebBridge scenario
     * trigger: user interaction
     */
    @Serializable
    public data object PushSystemCloseWebBridge : Interaction(
        category = "PushOptIn",
        scenario = "PromotionWebBridge",
        detail = "Decline",
        label = null
    )

    /**
     * tapped “logout” on Account screen
     * trigger: user interaction
     */
    @Serializable
    public data object AccountLogout : Interaction(
        category = "Account",
        scenario = null,
        detail = "Logout",
        label = null
    )

    /**
     * changed country
     * trigger: user interaction
     */
    @Serializable
    public data object AccountCountryChange : Interaction(
        category = "Account",
        scenario = null,
        detail = "CountryChange",
        label = null
    )

    /**
     * tapped push toggle to activate
     * trigger: user interaction
     */
    @Serializable
    public data object AccountPushEnable : Interaction(
        category = "Account",
        scenario = null,
        detail = "PushToggle",
        label = "Confirm"
    )

    /**
     * tapped push toggle to deactivate
     * trigger: user interaction
     */
    @Serializable
    public data object AccountPushDisable : Interaction(
        category = "Account",
        scenario = null,
        detail = "PushToggle",
        label = "Decline"
    )

    /**
     * tapped “confirm” on Att dialog
     * trigger: user interaction
     */
    @Serializable
    public data object AttSystemDialogConfirm : Interaction(
        category = "AttOptIn",
        scenario = "Onboarding",
        detail = "Confirm",
        label = null
    )

    /**
     * tapped “confirm” on Att dialog
     * trigger: user interaction
     */
    @Serializable
    public data object AttSystemDialogDecline : Interaction(
        category = "AttOptIn",
        scenario = "Onboarding",
        detail = "Decline",
        label = null
    )

    /**
     * tapped login button on Home
     * trigger: user interaction
     */
    @Serializable
    public data object HomeLogin : Interaction(
        category = "Homepage",
        scenario = null,
        detail = "Login",
        label = null
    )

    /**
     * tapped login button on Wishlist
     * trigger: user interaction
     */
    @Serializable
    public data object WishlistLogin : Interaction(
        category = "Wishlist",
        scenario = null,
        detail = "Login",
        label = null
    )

    /**
     * tapped login button on Basket
     * trigger: user interaction
     */
    @Serializable
    public data object BasketLogin : Interaction(
        category = "Cart",
        scenario = null,
        detail = "Login",
        label = null
    )

    /**
     * tapped search bar button
     * trigger: user interaction
     */
    @Serializable
    public data object OpenSearchBar : Interaction(
        category = "Search",
        scenario = null,
        detail = "Bar",
        label = null
    )

    /**
     * tapped search icon
     * trigger: user interaction
     */
    @Serializable
    public data object OpenSearchIcon : Interaction(
        category = "Search",
        scenario = null,
        detail = "Icon",
        label = null
    )

    /**
     * tapped inbox icon on Home
     * trigger: user interaction
     */
    @Serializable
    public data object OpenInboxHome : Interaction(
        category = "Inbox",
        scenario = "Homepage",
        detail = "Icon",
        label = null
    )

    /**
     * tapped inbox message
     * trigger: user interaction
     */
    @Serializable
    public data object OpenInboxMessage : Interaction(
        category = "Inbox",
        scenario = null,
        detail = "SelectMessage",
        label = null
    )

    /**
     * deleted inbox message
     * trigger: user interaction
     */
    @Serializable
    public data object DeleteInboxMessage : Interaction(
        category = "Inbox",
        scenario = null,
        detail = "DeleteMessage",
        label = null
    )

    @Serializable
    public sealed class Search(
        public val suggestionType: String?
    ) : OGEvent {

        override fun describe(): Map<String, Any?> = super.describe() + mapOf(
            "suggestionType" to suggestionType,
        )

        /**
         * sent search query
         * trigger: user interaction
         * @param searchTerm: the search term entered
         */
        @Serializable
        public data class QuerySubmit(public val searchTerm: String) : Search(null) {
            override fun describe(): Map<String, Any?> = super.describe() + mapOf(
                "searchTerm" to searchTerm,
            )
        }

        /**
         * selected search suggestion of type Term
         * trigger: user interaction
         */
        @Serializable
        public data object SuggestionTermSelected : Search("Term")

        /**
         * selected search suggestion of type Product
         * trigger: user interaction
         */
        @Serializable
        public data object SuggestionProductSelected : Search("Product")

        /**
         * selected search suggestion of type Category
         * trigger: user interaction
         */
        @Serializable
        public data object SuggestionCategorySelected : Search("Category")

        /**
         * selected search history entry
         * trigger: user interaction
         */
        @Serializable
        public data object HistorySelected : Search("History")
    }

    /**
     * selected account shortcut
     * trigger: user interaction
     * @param shortcut: name of the shortcut
     */
    @Serializable
    public data class SearchShortcut(public val shortcut: String) : Interaction(
        category = "Search",
        scenario = null,
        detail = "AccountShortcut",
        label = shortcut
    )

    /**
     * tapped “cancel” on search screen
     * trigger: user interaction
     */
    @Serializable
    public data object SearchCancel : Interaction(
        category = "Search",
        scenario = null,
        detail = "Cancel",
        label = null
    )

    /**
     * cleared search field
     * trigger: user interaction
     */
    @Serializable
    public data object SearchClear : Interaction(
        category = "Search",
        scenario = null,
        detail = "ClearBar",
        label = null
    )

    /**
     * deleted search history
     * trigger: user interaction
     */
    @Serializable
    public data object SearchDeleteHistory : Interaction(
        category = "Search",
        scenario = null,
        detail = "ClearHistory",
        label = null
    )

    /**
     * tapped catalog scanner button custom action button on search screens
     * trigger: user interaction
     */
    @Serializable
    public data object SearchCustomActionButtonCatalogScanner : Interaction(
        category = "Search",
        scenario = null,
        detail = "CatalogScanner",
        label = null
    )

    /**
     * changed country selected on Onboarding/ Welcome screen
     * trigger: user interaction
     */
    @Serializable
    public data object WelcomeChangeCountry : Interaction(
        category = "Onboarding",
        scenario = null,
        detail = "CountryChange",
        label = null
    )

    /**
     * tapped “login” button on Onboarding/ Welcome screen
     * trigger: user interaction
     */
    @Serializable
    public data object WelcomeLogin : Interaction(
        category = "Onboarding",
        scenario = null,
        detail = "Login",
        label = null
    )

    /**
     * tapped “register” button on Onboarding/ Welcome screen
     * trigger: user interaction
     */
    @Serializable
    public data object WelcomeRegister : Interaction(
        category = "Onboarding",
        scenario = null,
        detail = "Register",
        label = null
    )

    /**
     * tapped “skip” on Onboarding/ Welcome screen
     * trigger: user interaction
     */
    @Serializable
    public data object WelcomeSkip : Interaction(
        category = "Onboarding",
        scenario = null,
        detail = "SkipToShop",
        label = null
    )

    /**
     * tapped “continue”
     * trigger: user interaction
     *  @param currentStep: the current step (start at 1)
     */
    @Serializable
    public data class BraFittingGuideContinue(public val currentStep: Int) : Interaction(
        category = "BraFittingGuide",
        scenario = "Step $currentStep",
        detail = "Continue",
        label = null
    )

    /**
     * tapped “size measurement help” button
     * trigger: user interaction
     */
    @Serializable
    public data object BraFittingGuideHelpSize : Interaction(
        category = "BraFittingGuide",
        scenario = null,
        detail = "SizeHelp",
        label = null
    )

    /**
     * tapped CTA on final step leading to result with matching products
     * trigger: user interaction
     */
    @Serializable
    public data object BraFittingGuideFinishProducts : Interaction(
        category = "BraFittingGuide",
        scenario = null,
        detail = "Finish",
        label = "MatchingProducts"
    )

    /**
     * tapped CTA on final step leading to storefinder
     * trigger: user interaction
     */
    @Serializable
    public data object BraFittingGuideFinishStorefinder : Interaction(
        category = "BraFittingGuide",
        scenario = null,
        detail = "Finish",
        label = "Storefinder"
    )

    /**
     * tapped CTA on final step leading to Inspiration
     * trigger: user interaction
     */
    @Serializable
    public data object BraFittingGuideFinishInspiration : Interaction(
        category = "BraFittingGuide",
        scenario = null,
        detail = "Finish",
        label = "Inspiration"
    )

    /**
     * tapped info icon
     * trigger: user interaction
     * @param currentStep: the current step (start at 1)
     *
     */
    @Serializable
    public data class BraFittingGuideInfo(public val currentStep: Int) : Interaction(
        category = "BraFittingGuide",
        scenario = "Step $currentStep",
        detail = "Info",
        label = null
    )

    /**
     * tapped close icon
     * trigger: user interaction
     * @param currentStep: the current step (start at 1)
     */
    @Serializable
    public data class BraFittingGuideClose(public val currentStep: Int) : Interaction(
        category = "BraFittingGuide",
        scenario = "Step $currentStep",
        detail = "Close",
        label = null
    )

    /**
     * tapped “scan”
     * trigger: user interaction
     */
    @Serializable
    public data object CatalogScannerScan : Interaction(
        category = "CatalogScanner",
        scenario = null,
        detail = "Scan",
        label = null
    )

    /**
     * tapped “order catalog”
     * trigger: user interaction
     */
    @Serializable
    public data object CatalogScannerOrderCatalog : Interaction(
        category = "CatalogScanner",
        scenario = null,
        detail = "OrderCatalog",
        label = null
    )

    /**
     * selected product from scan overview
     * trigger: user interaction
     */
    @Serializable
    public data object CatalogScannerSelectProduct : Interaction(
        category = "CatalogScanner",
        scenario = null,
        detail = "SelectProduct",
        label = null
    )

    /**
     * tapped Home bottom navigation entry to reset tab state
     * trigger: user interaction
     *
     */
    @Serializable
    public data object ResetHomeTab : Interaction(
        category = "Navigation",
        scenario = null,
        detail = "ResetTab",
        label = "Home"
    )

    /**
     * tapped Assortment bottom navigation entry to reset tab state
     * trigger: user interaction
     */
    @Serializable
    public data object ResetAssortmentTab : Interaction(
        category = "Navigation",
        scenario = null,
        detail = "ResetTab",
        label = "Assortment"
    )

    /**
     * tapped Wishlist bottom navigation entry to reset tab state
     * trigger: user interaction
     */
    @Serializable
    public data object ResetWishlistTab : Interaction(
        category = "Navigation",
        scenario = null,
        detail = "ResetTab",
        label = "Wishlist"
    )

    /**
     * tapped Cart bottom navigation entry to reset tab state
     * trigger: user interaction
     */
    @Serializable
    public data object ResetCartTab : Interaction(
        category = "Navigation",
        scenario = null,
        detail = "ResetTab",
        label = "Cart"
    )

    /**
     * tapped Account bottom navigation entry to reset tab state
     * trigger: user interaction
     */
    @Serializable
    public data object ResetAccountTab : Interaction(
        category = "Navigation",
        scenario = null,
        detail = "ResetTab",
        label = "Account"
    )

    /**
     * selected store
     * trigger: user interaction
     */
    @Serializable
    public data object StoreLocatorSelectStore : Interaction(
        category = "StoreLocator",
        scenario = null,
        detail = "SelectStore",
        label = null
    )

    /**
     * unlocked the deal(s) via shake
     * trigger: user interaction
     */
    @Serializable
    public data object DealsUnlockShake : Interaction(
        category = "Deals",
        scenario = null,
        detail = "Unlock",
        label = "Shake"
    )

    /**
     * unlocked the deal(s) via tap
     * trigger: user interaction
     */
    @Serializable
    public data object DealsUnlockTap : Interaction(
        category = "Deals",
        scenario = null,
        detail = "Unlock",
        label = "Tap"
    )

    /**
     * unlocked the deal(s) via scratch
     * trigger: user interaction
     */
    @Serializable
    public data object DealsUnlockScratch : Interaction(
        category = "Deals",
        scenario = null,
        detail = "Unlock",
        label = "Scratch"
    )

    /**
     * selected a deal of type product
     * trigger: user interaction
     */
    @Serializable
    public data object DealsSelectProduct : Interaction(
        category = "Deals",
        scenario = null,
        detail = "SelectDeal",
        label = "Product"
    )

    /**
     * selected a deal of type promo
     * trigger: user interaction
     */
    @Serializable
    public data object DealsSelectPromo : Interaction(
        category = "Deals",
        scenario = null,
        detail = "SelectDeal",
        label = "Promotion"
    )

    /**
     * selected a deal of type voucher
     * trigger: user interaction
     */
    @Serializable
    public data object DealsSelectVoucher : Interaction(
        category = "Deals",
        scenario = null,
        detail = "SelectDeal",
        label = "Voucher"
    )

    /**
     * selected a deal of type code
     * trigger: user interaction
     */
    @Serializable
    public data object DealsSelectCode : Interaction(
        category = "Deals",
        scenario = null,
        detail = "SelectDeal",
        label = "Code"
    )

    /**
     * copied the code of a deal on overview screen
     * trigger: user interaction
     */
    @Serializable
    public data object DealsCopyCodeOverview : Interaction(
        category = "Deals",
        scenario = "Overview",
        detail = "CopyCode",
        label = null
    )

    /**
     * copied the code of a deal on cart screen
     * trigger: user interaction
     */
    @Serializable
    public data object DealsCopyCodeCart : Interaction(
        category = "Deals",
        scenario = "Cart",
        detail = "CopyCode",
        label = null
    )

    /**
     * swiped to next or previous deal
     * trigger: user interaction
     */
    @Serializable
    public data object DealsSwipe : Interaction(
        category = "Deals",
        scenario = null,
        detail = "Swipe",
        label = null
    )

    /**
     * tapped share icon
     * trigger: user interaction
     */
    @Serializable
    public data object ShareProduct : OGEvent

    /**
     * user has successfully completed login
     * trigger: login web bridge (if value changes to true)
     */
    @Serializable
    public data object LoggedIn : OGEvent

    /**
     * user has been successfully logged out
     * trigger: login web bridge (if value changes to false during session)
     */
    @Serializable
    public data object LoggedOut : OGEvent

    /**
     * completed purchase / checkout
     * trigger: purchase completed web bridge
     */
    @Serializable
    public data class Purchase(
        public val currency: String,
        public val value: Float,
        public val additionalParams: Map<String, String> = emptyMap(),
    ) : OGEvent {

        override fun describe(): Map<String, Any?> = super.describe() + mapOf(
            "currency" to currency,
            "value" to value,
        ) + additionalParams
    }

    /**
     * add item to wishlist
     * trigger: wishlist count web bridge (if count increases)
     */
    @Serializable
    public data object AddToWishlist : OGEvent

    /**
     * add item to wishlist
     * trigger: item successfully added to wishlist via API
     */
    @Serializable
    public data class AddItemToWishlist(
        val item: ECommerceItem,
        val additionalParameters: Map<String, CustomParameter> = emptyMap(),
    ) : OGEvent {
        override fun describe(): Map<String, Any?> = super.describe() +
            mapOf("item" to item) +
            additionalParameters.mapValues { it.value.value }
    }

    /**
     * add item to basket
     * trigger: basket count web bridge (if count increases)
     */
    @Serializable
    public data object AddToCart : OGEvent

    /**
     * add item to basket
     * trigger: item successfully added to basket via API
     */
    @Serializable
    public data class AddItemToCart(
        val item: ECommerceItem,
        val additionalParameters: Map<String, CustomParameter> = emptyMap(),
    ) : OGEvent {
        override fun describe(): Map<String, Any?> = super.describe() +
            mapOf("item" to item) +
            additionalParameters.mapValues { it.value.value }
    }

    public sealed class ProductDetailSelectVariant : OGEvent {
        public abstract val listId: String
        public abstract val item: ECommerceItem

        override fun describe(): Map<String, Any?> = super.describe() + mapOf(
            "listId" to listId,
            "item" to item,
        )

        /**
         * user changed variant by selecting a color
         * trigger: user interaction
         */
        @Serializable
        public data class ByColor(
            override val item: ECommerceItem
        ) : ProductDetailSelectVariant() {
            public constructor(
                itemId: String,
                itemName: String,
            ) : this(ECommerceItem(id = itemId, name = itemName))

            override val listId: String = "colors"
        }

        /**
         * user changed variant by selecting a size
         * trigger: user interaction
         */
        @Serializable
        public data class BySize(
            override val item: ECommerceItem
        ) : ProductDetailSelectVariant() {
            public constructor(
                itemId: String,
                itemName: String,
            ) : this(ECommerceItem(id = itemId, name = itemName))

            override val listId: String = "sizes"
        }

        /**
         * user changed variant by selecting a recommended product.
         * trigger: user interaction
         */
        @Serializable
        public data class ByRecommendations(
            val recommendationsId: String,
            override val item: ECommerceItem
        ) : ProductDetailSelectVariant() {
            public constructor(
                recommendationsId: String,
                itemId: String,
                itemName: String,
            ) : this(recommendationsId, ECommerceItem(id = itemId, name = itemName))

            override val listId: String = "reco_${recommendationsId.replace(" ", "_")}"
        }
    }

    /**
     * user interacted with media gallery (swipe, tap)
     * trigger: user interaction
     */
    @Serializable
    public data object ProductDetailUseMediaGallery : Interaction(
        category = "ProductDetail",
        scenario = null,
        detail = "ViewGallery",
        label = null
    )

    /**
     * user tapped size (or other dimension) to view the variants
     * trigger: user interaction
     */
    @Serializable
    public data class ProductDetailViewVariants(val dimensionName: String) : Interaction(
        category = "ProductDetail",
        scenario = null,
        detail = "ViewVariants",
        label = dimensionName
    )

    /**
     * user tapped product description accordion to view content
     * trigger: user interaction
     */
    @Serializable
    public data class ProductDetailViewDescription(val sectionTitle: String) : Interaction(
        category = "ProductDetail",
        scenario = null,
        detail = "ViewDescription",
        label = sectionTitle
    )

    /**
     * user scrolled through product recommendations (once per screen view per reco slider)
     * trigger: user interaction
     */
    @Serializable
    public data class ProductDetailViewRecommendation(
        val eventScenario: Scenario,
        val recommendationsId: String
    ) : Interaction(
        category = "ProductDetail",
        scenario = eventScenario.name,
        detail = "ViewRecommendation",
        label = recommendationsId.replace(" ", "_")
    ) {
        public enum class Scenario {
            /** The main PDP screen. */
            MainContent,

            /** The add to basket confirmation sheet. */
            Confirmation
        }
    }

    /**
     * user scrolled through review section (once per screen view)
     * trigger: user interaction
     */
    @Serializable
    public data object ProductDetailViewReviews : Interaction(
        category = "ProductDetail",
        scenario = null,
        detail = "ViewReviews",
        label = null,
    )

    /**
     * user tapped on promotion banner
     * trigger: user interaction
     */
    @Serializable
    public data class ProductDetailSelectPromotion(
        val item: ECommerceItem,
        val creativeName: String? = null,
        val creativeSlot: String? = null,
        val promotionId: String? = null,
        val promotionName: String? = null,
        val additionalParameters: Map<String, CustomParameter> = emptyMap(),
    ) : OGEvent {
        public constructor(
            itemId: String,
            itemName: String,
            coupon: String? = null,
            creativeName: String? = null,
            creativeSlot: String? = null,
            promotionId: String? = null,
            promotionName: String? = null,
            additionalParameters: Map<String, CustomParameter> = emptyMap(),
        ) : this(
            item = ECommerceItem(
                id = itemId,
                name = itemName,
                coupon = coupon,
            ),
            creativeName = creativeName,
            creativeSlot = creativeSlot,
            promotionId = promotionId,
            promotionName = promotionName,
            additionalParameters = additionalParameters,
        )

        override fun describe(): Map<String, Any?> = super.describe() +
            mapOf("item" to item) +
            additionalParameters.mapValues { it.value.value }
    }
}
