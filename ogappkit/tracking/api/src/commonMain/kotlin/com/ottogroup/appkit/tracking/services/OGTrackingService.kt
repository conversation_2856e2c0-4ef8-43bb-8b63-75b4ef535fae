package com.ottogroup.appkit.tracking.services

import co.touchlab.stately.collections.ConcurrentMutableMap
import com.ottogroup.appkit.tracking.OGTrackingDealer
import com.ottogroup.appkit.tracking.collectEvents
import com.ottogroup.appkit.tracking.collectUserProperty
import com.ottogroup.appkit.tracking.event.OGEvent
import com.ottogroup.appkit.tracking.userproperty.UserProperty
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.stateIn
import kotlinx.serialization.json.JsonObject

internal abstract class OGTrackingService<Config : ServiceConfig, Event : ServiceEvent?> protected constructor(
    trackingDealer: OGTrackingDealer,
    isInitiallyEnabled: Boolean = false,
    configFlow: Flow<Config>,
    internal val serviceId: OGTrackingServiceId,
    coroutineScope: CoroutineScope
) {
    internal abstract val config: StateFlow<Config>

    internal val isEnabledFlow: StateFlow<Boolean> = combine(
        trackingDealer.consent.consentForService(serviceId),
        configFlow
    ) { consent, config ->
        consent to config
    }.onStart {
        /* It is possible that this callback is triggered immediately at TrackingService construction,
         * before the subclass constructor has run. That would make it possible for an overridden
         * evaluateEnabledState function to access uninitialized code and crash.
         * Therefore, we add a small delay here that will not affect any real-world use but ensures
         * enough time for full subclass initialization. */
        delay(500)
    }
        .distinctUntilChanged()
        .map { (consent, config) ->
            val isEnabled = evaluateEnabledState(consent, config)
            performSetUserProperty(isEnabled)
            isEnabled
        }
        .distinctUntilChanged()
        .onEach { onIsEnabledChanged(it) }
        .stateIn(coroutineScope, SharingStarted.Eagerly, isInitiallyEnabled)

    internal val isEnabled: Boolean get() = isEnabledFlow.value
    private val cachedUserProperties = ConcurrentMutableMap<String, String?>()

    init {
        trackingDealer.collectEvents { event ->
            mapEvent(event)?.let { trackEvent(it) }
        }

        trackingDealer.collectUserProperty {
            cacheUserProperty(it)
            performSetUserProperty(isEnabled)
        }

        configFlow
            .onStart {
                // This is a best guess fix
                // It is possible that this callback is triggered immediately at TrackingService construction
                // which causes to access uninitialized code and crash on ios.
                delay(500)
            }
            .onEach { setDefaultParameters(it.globalContext) }
            .launchIn(coroutineScope)
    }

    abstract fun mapEvent(trackingEvent: OGEvent): Event?

    private fun trackEvent(trackingEvent: Event) {
        if (!isEnabled) return
        performTrackEvent(trackingEvent)
    }

    private fun cacheUserProperty(userProperty: UserProperty) {
        cachedUserProperties[userProperty.key] = userProperty.value
    }

    private fun performSetUserProperty(isEnabled: Boolean) {
        if (!isEnabled) return
        for (cachedUserProperty in cachedUserProperties) {
            setUserProperty(cachedUserProperty.key, cachedUserProperty.value)
        }
    }

    /**
     * Returns the service's enabled state after a consent or config change.
     *
     * Override this to do custom handling. The default is to enable the
     * service iff consent is given and the config is enabled.
     *
     * @param consent The new consent state for the service.
     * @param config The new config for the service.
     * @return the updated enabled state of the service
     */
    protected open fun evaluateEnabledState(consent: Boolean, config: Config): Boolean {
        return consent && config.isEnabled
    }

    /**
     * Override this to get notified about changes in the enabled state for
     * this [OGTrackingService], e.g. to enable/disable a third-party SDK.
     *
     * @param enabled new enabled state
     */
    protected open fun onIsEnabledChanged(enabled: Boolean) {}
    protected open fun performTrackEvent(trackingEvent: Event) {}

    /**
     * Override this to receive the default parameters for this
     * [OGTrackingService] which should be added to each subsequent event.
     */
    protected open fun setDefaultParameters(parameters: List<JsonObject>) {}

    /**
     * Override this to receive user properties for this
     *  @param key the name of the user property
     *  @param value nullable value of the user property
     */
    protected open fun setUserProperty(key: String, value: String?) {}
}
