plugins {
    id("ogAppKit.publishedSdk")
}

@Suppress("PropertyName")
val FRAMEWORK_NAME: String by properties

kotlin {
    /**
     * These are the modules that make up the SDK. They are added as [api] dependencies in Kotlin and bundled into the
     * generated XCFramework.
     */
    val sdkModules = listOf(
        projects.ogappkit.base.api,
        projects.ogappkit.nativeui.api,
        projects.ogappkit.tracking.api,
        projects.ogappkit.xadeal.api,
        projects.ogappkit.l10n.api
    )

    listOf(
        iosX64(),
        iosArm64(),
        iosSimulatorArm64()
    ).forEach {
        it.binaries.framework {
            baseName = FRAMEWORK_NAME
            isStatic = true
            sdkModules.forEach(::export)
        }
    }

    sourceSets {
        androidMain.dependencies {
            implementation(libs.firebase.analytics)
            implementation(libs.adjust.android)
            implementation(libs.snowplow.android.tracker)
            implementation(libs.androidx.startup.runtime)
        }
        iosMain.dependencies {
        }
        commonMain.dependencies {
            sdkModules.forEach(::api)
            implementation(projects.ogappkit.xadeal.internal)
            implementation(projects.ogappkit.nativeui.internal)
            implementation(projects.ogappkit.l10n.internal)
            implementation(libs.square.okio)
            implementation(libs.androidx.datastore.core)
            implementation(libs.androidx.datastore.core.okio)
        }
        commonTest.dependencies {
        }
    }
}
