package com.ottogroup.appkit.xadeal.data.datasource

import com.ottogroup.appkit.xadeal.model.Campaigns
import com.ottogroup.appkit.xadeal.model.LocalCampaignsHolder
import com.ottogroup.appkit.xadeal.test.LocalDataStore
import com.ottogroup.appkit.xadeal.test.TestClock
import com.ottogroup.appkit.xadeal.test.Campaign
import com.ottogroup.appkit.xadeal.test.Campaign2
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Instant

class XaDealCampaignsLocalDataSourceTest {

    private val testClock = TestClock().apply { this.nextInstant = Instant.fromEpochMilliseconds(12345) }

    private val dataStore = LocalDataStore(
        MutableStateFlow(
            LocalCampaignsHolder(
                Instant.fromEpochMilliseconds(0),
                emptyMap()
            )
        )
    )
    private val dataSource = XaDealCampaignsLocalDataSource(dataStore, testClock)

    @Test
    fun `storeCampaigns should store the campaigns in dataStore`() = runTest {
        val dummyCampaigns = Campaigns(listOf(Campaign, Campaign2))
        dataSource.storeCampaigns(dummyCampaigns)

        val campaigns = dataSource.campaigns.first()
        assertEquals(dummyCampaigns, campaigns)
        val timestamp = dataSource.campaignsRequestTimestamp.first()
        assertEquals(12345, timestamp.toEpochMilliseconds())
    }

    @Test
    fun `clearCampaigns should remove the campaigns from the dataStore`() = runTest {
        val dummyCampaigns = Campaigns(listOf(Campaign, Campaign2))
        dataSource.storeCampaigns(dummyCampaigns)
        dataSource.clearCampaigns()
        assertEquals(
            dataStore.data.first(),
            LocalCampaignsHolder(
                Instant.fromEpochMilliseconds(0),
                emptyMap()
            )
        )
    }
}
