package com.ottogroup.appkit.xadeal.test

import com.ottogroup.appkit.xadeal.model.Campaign

internal val Campaign = Campaign(
    id = "1",
    bannerId = "test",
    onboardingText = "test",
    offboardingText = "test",
    buttonText = "test",
    startDate = "test",
    endDate = "test",
    gameMechanic = Campaign.GameMechanic.ShakeADeal(10)
)

internal val Campaign2 = Campaign(
    id = "2",
    bannerId = "test2",
    onboardingText = "test2",
    offboardingText = "test2",
    buttonText = "test2",
    startDate = "test2",
    endDate = "test2",
    gameMechanic = Campaign.GameMechanic.ScratchADeal(50.0)
)
