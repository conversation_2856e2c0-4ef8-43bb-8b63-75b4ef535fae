package com.ottogroup.appkit.xadeal.data.datasource

import com.ottogroup.appkit.xadeal.model.DealProgressHolder
import com.ottogroup.appkit.xadeal.test.LocalDataStore
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest

class XaDealProgressLocalDataSourceTest {

    private val dataStore =
        LocalDataStore(MutableStateFlow(DealProgressHolder(true, -1L)))
    private val dataSource = XaDealProgressLocalDataSource(dataStore)

    @Test
    fun `storeProgress should store the progress in dataStore`() = runTest {
        val dummyProgress = DealProgressHolder(false, 3L)
        dataSource.storeProgress(false, 3L)

        val progress = dataSource.dealProgress.first()
        assertEquals(dummyProgress, progress)
    }

    @Test
    fun `clearProgress updates the data in the dataStore`() = runTest {
        val dummyProgress = DealProgressHolder(true, -1L)
        dataSource.storeProgress(false, 3L)
        dataSource.clearProgress()

        val progress = dataSource.dealProgress.first()
        assertEquals(dummyProgress, progress)
    }
}
