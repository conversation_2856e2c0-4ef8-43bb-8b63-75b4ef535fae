package com.ottogroup.appkit.xadeal.test

import androidx.datastore.core.DataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow

class LocalDataStore<T>(private val stateFlow: MutableStateFlow<T>) : DataStore<T> {
    override val data: Flow<T>
        get() = stateFlow.asSharedFlow()

    override suspend fun updateData(transform: suspend (t: T) -> T): T {
        stateFlow.value = transform(stateFlow.value)
        return stateFlow.value
    }
}

internal fun <T> mockDataStore(default: T): DataStore<T> {
    return LocalDataStore(MutableStateFlow(default))
}

internal fun <T> mockDataStore(mutableStateFlow: MutableStateFlow<T>): DataStore<T> {
    return LocalDataStore(mutableStateFlow)
}
