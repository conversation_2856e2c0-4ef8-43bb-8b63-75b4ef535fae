package com.ottogroup.appkit.xadeal.data.datasource

import com.ottogroup.appkit.xadeal.model.LocalOffersHolder
import com.ottogroup.appkit.xadeal.model.Offers
import com.ottogroup.appkit.xadeal.test.LocalDataStore
import com.ottogroup.appkit.xadeal.test.TestClock
import com.ottogroup.appkit.xadeal.test.Campaign
import com.ottogroup.appkit.xadeal.test.offer
import com.ottogroup.appkit.xadeal.test.offer2
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant

class XaDealOffersLocalDataSourceTest {

    private val testClock = TestClock().apply { this.nextInstant = Instant.fromEpochMilliseconds(12345) }

    private val dataStore =
        LocalDataStore(MutableStateFlow(LocalOffersHolder(Instant.fromEpochMilliseconds(0), emptyMap())))
    private val dataSource = XaDealOffersLocalDataSource(dataStore, testClock)

    @Test
    fun `storeOffers should store the offers in dataStore for a new campaign`() = runTest {
        val dummyOffers = Offers(listOf(offer, offer2))
        dataSource.storeOffers(Campaign.id, dummyOffers)

        val offers = dataSource.offers.first()
        assertEquals(mapOf(Campaign.id to dummyOffers), offers)
    }

    @Test
    fun `storeOffers should update the offers in dataStore for an existing campaign`() = runTest {
        dataSource.storeOffers(Campaign.id, Offers(listOf(offer)))
        dataSource.storeOffers(Campaign.id, Offers(listOf(offer, offer2)))

        val offers = dataSource.offers.first()
        assertEquals(mapOf(Campaign.id to Offers(listOf(offer, offer2))), offers)
    }

    @Test
    fun `consume should remove the offer from the dataStore for an existing campaign`() = runTest {
        dataSource.storeOffers(Campaign.id, Offers(listOf(offer, offer2)))

        dataSource.consumeOffer(Campaign.id, offer2)
        val offers = dataSource.offers.first()
        assertEquals(mapOf(Campaign.id to Offers(listOf(offer))), offers)
    }

    @Test
    fun `removeCampaign should remove the offers in dataStore for the specified campaign`() = runTest {
        val dummyOffers = Offers(listOf(offer, offer2))
        dataSource.storeOffers(Campaign.id, dummyOffers)
        dataSource.removeCampaign(Campaign.id)

        val offers = dataSource.offers.first()
        assertEquals(emptyMap(), offers)
    }

    @Test
    fun `clearOffers should remove the campaigns from the dataStore`() = runTest {
        val dummyOffers = Offers(listOf(offer, offer2))
        dataSource.storeOffers(Campaign.id, dummyOffers)
        dataSource.clearOffers()
        assertEquals(
            dataStore.data.first(),
            LocalOffersHolder(Instant.fromEpochMilliseconds(0), emptyMap())
        )
    }

    @Test
    fun `canShowDeals is true when the deals list is not empty`() = runTest {
        dataSource.storeOffers(Campaign.id, Offers(listOf(offer)))

        assertEquals(true, dataSource.canShowDeals().first()[Campaign.id])
    }

    @Test
    fun `canShowDeals is false when the list is empty and the timestamp is from today`() = runTest {
        testClock.nextInstant = Clock.System.now()
        dataSource.storeOffers(Campaign.id, Offers(listOf(offer)))
        dataSource.consumeOffer(Campaign.id, offer)

        assertEquals(false, dataSource.canShowDeals().first()[Campaign.id])
    }
}
