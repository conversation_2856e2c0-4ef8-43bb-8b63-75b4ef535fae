package com.ottogroup.appkit.xadeal.test

import com.ottogroup.appkit.xadeal.model.Offer
import com.ottogroup.appkit.xadeal.model.Price

internal val offer = Offer.Product(
    id = "1",
    title = "test",
    url = "www.test.com",
    imageUrl = "www.test.com/image",
    legalText = "test",
    price = Price(
        value = 39,
        oldValue = null,
        currency = "EUR",
        priceRange = false,
        discounted = false
    ),
    label = "test"
)

internal val offer2 = Offer.Voucher(
    id = "2",
    title = "test2",
    url = "www.test2.com",
    imageUrl = "www.test2.com/image",
    legalText = "test2"
)
