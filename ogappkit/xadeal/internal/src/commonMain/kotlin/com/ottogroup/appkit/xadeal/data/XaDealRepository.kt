package com.ottogroup.appkit.xadeal.data

import co.touchlab.kermit.Logger
import com.ottogroup.appkit.xadeal.data.datasource.XaDealCampaignsLocalDataSource
import com.ottogroup.appkit.xadeal.data.datasource.XaDealNetworkDataSource
import com.ottogroup.appkit.xadeal.data.datasource.XaDealOffersLocalDataSource
import com.ottogroup.appkit.xadeal.data.datasource.XaDealProgressLocalDataSource
import com.ottogroup.appkit.xadeal.model.Campaigns
import com.ottogroup.appkit.xadeal.model.DealProgressHolder
import com.ottogroup.appkit.xadeal.model.Offer
import com.ottogroup.appkit.xadeal.model.Offers
import kotlinx.coroutines.flow.Flow
import kotlinx.datetime.Instant

internal class XaDealRepository(
    private val xaDealCampaignsLocalDataSource: XaDealCampaignsLocalDataSource,
    private val xaDealOffersLocalDataSource: XaDealOffersLocalDataSource,
    private val xaDealNetworkDataSource: XaDealNetworkDataSource,
    private val xaDealProgressLocalDataSource: XaDealProgressLocalDataSource
) {
    suspend fun fetchAndStoreCampaigns() {
        xaDealNetworkDataSource.getCampaigns().fold(
            onSuccess = { networkCampaigns ->
                clearCampaigns()
                xaDealCampaignsLocalDataSource.storeCampaigns(networkCampaigns)
            },
            onFailure = {
                Logger.e("Error fetching campaigns: ${it.message}")
                clearCampaigns()
            }
        )
    }

    suspend fun clearCampaigns() {
        xaDealCampaignsLocalDataSource.clearCampaigns()
    }

    fun getLocalCampaigns(): Flow<Campaigns> {
        return xaDealCampaignsLocalDataSource.campaigns
    }

    fun getCampaignsRequestTimestamp(): Flow<Instant> {
        return xaDealCampaignsLocalDataSource.campaignsRequestTimestamp
    }

    suspend fun fetchAndStoreOffers(campaignId: String) {
        xaDealNetworkDataSource.getOffers(campaignId).fold(
            onSuccess = { networkOffers ->
                xaDealOffersLocalDataSource.storeOffers(campaignId, networkOffers)
            },
            onFailure = {
                Logger.e("Error fetching offers: ${it.message}")
                throw it
            }
        )
    }

    fun getLocalOffers(): Flow<Map<String, Offers>> {
        return xaDealOffersLocalDataSource.offers
    }

    suspend fun removeCampaignOffers(campaignId: String) {
        xaDealOffersLocalDataSource.removeCampaign(campaignId)
    }

    suspend fun consumeOffer(campaignId: String, offer: Offer) {
        xaDealOffersLocalDataSource.consumeOffer(campaignId, offer)
    }

    fun getOffersRequestTimestamp(): Flow<Instant> {
        return xaDealOffersLocalDataSource.offersRequestTimestamp
    }

    fun getDealProgress(): Flow<DealProgressHolder> {
        return xaDealProgressLocalDataSource.dealProgress
    }

    suspend fun updateDealProgress(showOverlay: Boolean, timeLeft: Long) {
        xaDealProgressLocalDataSource.storeProgress(showOverlay, timeLeft)
    }

    suspend fun clearDealProgress() {
        xaDealProgressLocalDataSource.clearProgress()
    }

    fun canShowDeals(): Flow<Map<String, Boolean>> {
        return xaDealOffersLocalDataSource.canShowDeals()
    }

    suspend fun clearOffers() {
        xaDealOffersLocalDataSource.clearOffers()
    }
}
