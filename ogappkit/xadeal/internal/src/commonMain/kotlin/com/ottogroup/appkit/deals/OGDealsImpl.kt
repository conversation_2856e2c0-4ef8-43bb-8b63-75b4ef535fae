package com.ottogroup.appkit.deals

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.deals.config.DealsConfig
import com.ottogroup.appkit.deals.config.DealsConfigProvider
import com.ottogroup.appkit.deals.data.DealsRepository
import com.ottogroup.appkit.deals.model.Campaign
import com.ottogroup.appkit.deals.model.Campaigns
import kotlinx.coroutines.flow.Flow

internal class OGDealsImpl(
    private val configProvider: DealsConfigProvider,
    private val dealsRepository: DealsRepository,
) : OGDeals {

    override fun configure(config: DealsConfig) {
        configProvider.update(config)
    }

    override fun getCampaigns(): Flow<Result<Campaigns>> {
        return dealsRepository.getCampaigns()
    }

    override fun getCampaign(campaignId: String): Flow<Result<Campaign>> {
        return dealsRepository.getCampaign(campaignId)
    }
}
