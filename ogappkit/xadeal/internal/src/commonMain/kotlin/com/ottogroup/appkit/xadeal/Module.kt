package com.ottogroup.appkit.xadeal

import com.ottogroup.appkit.base.datastore.jsonDataStore
import com.ottogroup.appkit.base.di.getCoroutineScope
import com.ottogroup.appkit.xadeal.config.XaDealConfigProvider
import com.ottogroup.appkit.xadeal.data.XaDealRepository
import com.ottogroup.appkit.xadeal.data.datasource.XaDealCampaignsLocalDataSource
import com.ottogroup.appkit.xadeal.data.datasource.XaDealNetworkDataSource
import com.ottogroup.appkit.xadeal.data.datasource.XaDealOffersLocalDataSource
import com.ottogroup.appkit.xadeal.data.datasource.XaDealProgressLocalDataSource
import com.ottogroup.appkit.xadeal.model.DealProgressHolder
import com.ottogroup.appkit.xadeal.model.LocalCampaignsHolder
import com.ottogroup.appkit.xadeal.model.LocalOffersHolder
import kotlinx.datetime.Instant
import org.koin.core.module.Module
import org.koin.dsl.module

public val xaDealModule: Module = module {
    single<OGXaDeal> { OGXaDealImpl(get(), get(), getCoroutineScope()) }
    single { XaDealConfigProvider() }
    single { XaDealRepository(get(), get(), get(), get()) }
    single { XaDealNetworkDataSource(get()) }
    single<XaDealCampaignsLocalDataSource> {
        XaDealCampaignsLocalDataSource(
            jsonDataStore(
                default = LocalCampaignsHolder(Instant.fromEpochMilliseconds(0), emptyMap()),
                fileName = "campaigns.json",
                fileSystem = get()
            )
        )
    }
    single<XaDealOffersLocalDataSource> {
        XaDealOffersLocalDataSource(
            jsonDataStore(
                default = LocalOffersHolder(Instant.fromEpochMilliseconds(0), emptyMap()),
                fileName = "offers.json",
                fileSystem = get()
            )
        )
    }

    single<XaDealProgressLocalDataSource> {
        XaDealProgressLocalDataSource(
            jsonDataStore(
                default = DealProgressHolder(),
                fileName = "dealProgress.json",
                fileSystem = get()
            )
        )
    }
}
