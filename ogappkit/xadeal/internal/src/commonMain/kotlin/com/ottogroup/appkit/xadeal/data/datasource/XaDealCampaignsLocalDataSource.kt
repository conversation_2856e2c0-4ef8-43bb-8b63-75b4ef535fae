package com.ottogroup.appkit.xadeal.data.datasource

import androidx.datastore.core.DataStore
import com.ottogroup.appkit.xadeal.model.Campaigns
import com.ottogroup.appkit.xadeal.model.LocalCampaignsHolder
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant

internal class XaDealCampaignsLocalDataSource(
    private val dataStore: DataStore<LocalCampaignsHolder>,
    private val clock: Clock = Clock.System
) {
    suspend fun storeCampaigns(campaigns: Campaigns) {
        dataStore.updateData {
            LocalCampaignsHolder(
                campaigns = campaigns.campaigns.associateBy { it.id },
                timestamp = clock.now()
            )
        }
    }

    suspend fun clearCampaigns() {
        dataStore.updateData {
            LocalCampaignsHolder(Instant.fromEpochMilliseconds(0), emptyMap())
        }
    }

    val campaigns: Flow<Campaigns> = dataStore.data.map { Campaigns(it.campaigns.values.toList()) }

    val campaignsRequestTimestamp: Flow<Instant> = dataStore.data.map { it.timestamp }
}
