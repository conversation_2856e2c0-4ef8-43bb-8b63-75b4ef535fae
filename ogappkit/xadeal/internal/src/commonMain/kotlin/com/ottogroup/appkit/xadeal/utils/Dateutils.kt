package com.ottogroup.appkit.xadeal.utils

import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime

internal fun isFromToday(requestTimestamp: Instant): Boolean {
    val currentDate = Clock.System.now().toLocalDateTime(TimeZone.currentSystemDefault()).date
    val requestDate = requestTimestamp.toLocalDateTime(TimeZone.currentSystemDefault()).date
    return currentDate == requestDate
}
