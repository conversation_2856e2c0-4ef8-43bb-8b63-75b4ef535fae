package com.ottogroup.appkit.xadeal.data.datasource

import androidx.datastore.core.DataStore
import com.ottogroup.appkit.xadeal.model.LocalOffersHolder
import com.ottogroup.appkit.xadeal.model.Offer
import com.ottogroup.appkit.xadeal.model.Offers
import com.ottogroup.appkit.xadeal.utils.isFromToday
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant

internal class XaDealOffersLocalDataSource(
    private val dataStore: DataStore<LocalOffersHolder>,
    private val clock: Clock = Clock.System
) {
    suspend fun storeOffers(campaignId: String, offers: Offers) {
        dataStore.updateData { currentData ->
            LocalOffersHolder(
                offers = currentData.offers.plus(campaignId to offers),
                timestamp = clock.now()
            )
        }
    }

    suspend fun removeCampaign(campaignId: String) {
        dataStore.updateData { currentData ->
            currentData.copy(
                offers = if (currentData.offers.containsKey(campaignId)) {
                    currentData.offers.minus(campaignId)
                } else {
                    currentData.offers
                }
            )
        }
    }

    suspend fun consumeOffer(campaignId: String, offer: Offer) {
        dataStore.updateData { currentData ->
            val updatedOffers = currentData.offers.mapValues { entry ->
                if (entry.key == campaignId) entry.value.copy(offers = entry.value.offers.minus(offer)) else entry.value
            }
            currentData.copy(offers = updatedOffers)
        }
    }

    suspend fun clearOffers() {
        dataStore.updateData {
            LocalOffersHolder(Instant.fromEpochMilliseconds(0), emptyMap())
        }
    }

    val offers: Flow<Map<String, Offers>> = dataStore.data.map { it.offers }

    val offersRequestTimestamp: Flow<Instant> = dataStore.data.map { it.timestamp }

    fun canShowDeals(): Flow<Map<String, Boolean>> {
        return dataStore.data.map { currentData ->
            currentData.offers.mapValues { (_, offers) ->
                offers.offers.isNotEmpty() || !isFromToday(currentData.timestamp)
            }
        }
    }
}
