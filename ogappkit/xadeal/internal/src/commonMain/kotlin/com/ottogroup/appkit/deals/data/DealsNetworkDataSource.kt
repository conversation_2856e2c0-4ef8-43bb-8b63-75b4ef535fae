package com.ottogroup.appkit.deals.data

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.getOrThrow
import com.ottogroup.appkit.base.http.OGAppKitHeadersPlugin
import com.ottogroup.appkit.base.lenientJson
import com.ottogroup.appkit.deals.config.DealsConfigProvider
import com.ottogroup.appkit.deals.model.Campaign
import com.ottogroup.appkit.deals.model.Campaigns
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.plugins.cache.HttpCache
import io.ktor.client.plugins.cache.storage.CacheStorage
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.accept
import io.ktor.client.request.get
import io.ktor.client.request.headers
import io.ktor.client.statement.HttpResponse
import io.ktor.http.ContentType
import io.ktor.http.isSuccess
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.ExperimentalSerializationApi

internal class DealsNetworkDataSource(
    configProvider: DealsConfigProvider,
    ogKitHeaders: OGAppKitHeadersPlugin
) {
    private val cache by lazy { CacheStorage.Unlimited() }

    @OptIn(ExperimentalSerializationApi::class)
    private val client: HttpClient by lazy {
        HttpClient {
            install(ogKitHeaders)
            install(HttpCache) {
                // FIXME insert file cache here
                privateStorage(cache)
            }
            install(ContentNegotiation) {
                val json = lenientJson
                json(
                    json,
                    ContentType.Application.OGAppKit.json(2)
                )
                json(json)
            }
        }
    }

    private val config = configProvider.configState

    private fun validateParameters(): Result<Unit> {
        return try {
            require(config.value.baseUrl.isNotEmpty()) { Throwable("Base URL is empty") }
            require(config.value.locale.isNotEmpty()) { Throwable("Locale is empty") }
            require(config.value.apiKey.isNotEmpty()) { Throwable("API key is empty") }
            Result.Success(Unit)
        } catch (e: Throwable) {
            Result.Failure(e)
        }
    }

    @Throws(Exception::class)
    suspend fun getCampaigns(): Result<Campaigns> {
        return try {
            validateParameters().getOrThrow()

            val response: HttpResponse = client.get("${config.value.baseUrl}/campaigns") {
                headers {
                    append("Accept-Language", config.value.locale)
                    append("x-api-key", config.value.apiKey)
                }
                accept(ContentType.Application.OGAppKit.json(2))
            }

            if (response.status.isSuccess()) {
                val campaigns: Campaigns = response.body()
                Result.Success(campaigns)
            } else {
                Result.Failure(Throwable("Failed to fetch campaigns: ${response.status}"))
            }
        } catch (e: Exception) {
            Result.Failure(e)
        }
    }

    @Throws(Exception::class)
    suspend fun getCampaign(campaignId: String): Result<Campaign> {
        return try {
            validateParameters().getOrThrow()

            val response: HttpResponse =
                client.get("${config.value.baseUrl}/campaigns/$campaignId") {
                    headers {
                        append("Accept-Language", config.value.locale)
                        append("x-api-key", config.value.apiKey)
                    }
                    accept(ContentType.Application.OGAppKit.json(2))
                }

            if (response.status.isSuccess()) {
                val campaign: ResolvedCampaign = response.body()
                Result.Success(campaign.campaign)
            } else {
                Result.Failure(Throwable("Failed to fetch offers: ${response.status}"))
            }
        } catch (e: Exception) {
            Result.Failure(e)
        }
    }
}
