package com.ottogroup.appkit.xadeal.data.datasource

import com.ottogroup.appkit.base.di.InternalKoinComponent
import com.ottogroup.appkit.base.lenientJson
import com.ottogroup.appkit.xadeal.config.XaDealConfigProvider
import com.ottogroup.appkit.xadeal.model.Campaigns
import com.ottogroup.appkit.xadeal.model.Offers
import com.ottogroup.appkit.xadeal.utils.CampaignModule
import com.ottogroup.appkit.xadeal.utils.offerModule
import io.ktor.client.HttpClient
import io.ktor.client.call.body
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.get
import io.ktor.client.request.headers
import io.ktor.client.statement.HttpResponse
import io.ktor.http.isSuccess
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.json.Json
import kotlinx.serialization.modules.SerializersModule

internal class XaDealNetworkDataSource(
    configProvider: XaDealConfigProvider
) : InternalKoinComponent {

    private val client: HttpClient
        get() = HttpClient {
            install(ContentNegotiation) {
                json(
                    Json(from = lenientJson) {
                        serializersModule = SerializersModule {
                            include(offerModule)
                            include(CampaignModule)
                        }
                    }
                )
            }
        }

    private val config = configProvider.configState

    private fun validateParameters(): Result<Unit> {
        return try {
            require(config.value.baseUrl.isNotEmpty()) { Throwable("Base URL is empty") }
            require(config.value.locale.isNotEmpty()) { Throwable("Locale is empty") }
            require(config.value.apiKey.isNotEmpty()) { Throwable("API key is empty") }
            Result.success(Unit)
        } catch (e: Throwable) {
            Result.failure(e)
        }
    }

    suspend fun getCampaigns(): Result<Campaigns> {
        return try {
            validateParameters().getOrThrow()

            val response: HttpResponse = client.get("${config.value.baseUrl}/campaigns") {
                headers {
                    append("Accept-Language", config.value.locale)
                    append("x-api-key", config.value.apiKey)
                }
            }

            if (response.status.isSuccess()) {
                val campaigns: Campaigns = response.body()
                Result.success(campaigns)
            } else {
                Result.failure(Throwable("Failed to fetch campaigns: ${response.status}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        } finally {
            client.close()
        }
    }

    suspend fun getOffers(campaignId: String): Result<Offers> {
        return try {
            validateParameters().getOrThrow()

            val response: HttpResponse = client.get("${config.value.baseUrl}/campaigns/$campaignId/offers") {
                headers {
                    append("Accept-Language", config.value.locale)
                    append("x-api-key", config.value.apiKey)
                }
            }

            if (response.status.isSuccess()) {
                val offers: Offers = response.body()
                Result.success(offers)
            } else {
                Result.failure(Throwable("Failed to fetch offers: ${response.status}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        } finally {
            client.close()
        }
    }
}
