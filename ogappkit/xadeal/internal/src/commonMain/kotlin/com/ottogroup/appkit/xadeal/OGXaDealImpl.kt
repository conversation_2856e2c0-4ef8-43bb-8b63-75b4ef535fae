package com.ottogroup.appkit.xadeal

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.di.InternalKoinComponent
import com.ottogroup.appkit.base.resultFor
import com.ottogroup.appkit.xadeal.config.XaDealConfig
import com.ottogroup.appkit.xadeal.config.XaDealConfigProvider
import com.ottogroup.appkit.xadeal.data.XaDealRepository
import com.ottogroup.appkit.xadeal.model.Campaigns
import com.ottogroup.appkit.xadeal.model.DealProgressHolder
import com.ottogroup.appkit.xadeal.model.Offer
import com.ottogroup.appkit.xadeal.model.OffersHolder
import com.ottogroup.appkit.xadeal.utils.isFromToday
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

internal class OGXaDealImpl(
    private val configProvider: XaDealConfigProvider,
    private val xaDealRepository: XaDealRepository,
    private val coroutineScope: CoroutineScope
) : OGXaDeal, InternalKoinComponent {

    override fun configure(config: XaDealConfig) {
        configProvider.update(config)
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun getCampaigns(): Flow<Campaigns> {
        return xaDealRepository.getCampaignsRequestTimestamp().flatMapLatest { requestTimestamp ->
            if (!isFromToday(requestTimestamp)) {
                xaDealRepository.fetchAndStoreCampaigns()
            }
            xaDealRepository.getLocalCampaigns()
        }
    }

    override suspend fun getOffers(campaignId: String): Result<OffersHolder> {
        return resultFor {
            val requestTimestamp = xaDealRepository.getOffersRequestTimestamp().first()
            if (!isFromToday(requestTimestamp)) {
                xaDealRepository.fetchAndStoreOffers(campaignId)
            }
            OffersHolder(
                newDeals = !isFromToday(requestTimestamp),
                offers = xaDealRepository.getLocalOffers().map {
                    it.getValue(campaignId)
                }
            )
        }
    }

    override fun removeCampaignOffers(campaignId: String) {
        coroutineScope.launch {
            xaDealRepository.removeCampaignOffers(campaignId)
        }
    }

    override fun consumeOffer(campaignId: String, offer: Offer) {
        coroutineScope.launch {
            xaDealRepository.consumeOffer(campaignId, offer)
        }
    }

    override fun getDealProgress(): Flow<DealProgressHolder> {
        return xaDealRepository.getDealProgress()
    }

    override fun updateDealProgress(showOverlay: Boolean, timeLeft: Long) {
        coroutineScope.launch {
            xaDealRepository.updateDealProgress(showOverlay, timeLeft)
        }
    }

    override fun clearDealProgress() {
        coroutineScope.launch {
            xaDealRepository.clearDealProgress()
        }
    }

    override fun canShowDeals(): Flow<Map<String, Boolean>> {
        return xaDealRepository.canShowDeals()
    }

    override suspend fun clearData() {
        xaDealRepository.clearCampaigns()
        xaDealRepository.clearDealProgress()
        xaDealRepository.clearOffers()
    }
}
