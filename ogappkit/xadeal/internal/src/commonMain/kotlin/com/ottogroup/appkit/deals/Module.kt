package com.ottogroup.appkit.deals

import com.ottogroup.appkit.base.http.ogAppKitHeadersPlugin
import com.ottogroup.appkit.deals.config.DealsConfigProvider
import com.ottogroup.appkit.deals.data.DealsNetworkDataSource
import com.ottogroup.appkit.deals.data.DealsRepository
import com.ottogroup.appkit.deals.data.DealsRepositoryImpl
import org.koin.core.module.Module
import org.koin.dsl.module

public val dealsModule: Module = module {
    single<OGDeals> { OGDealsImpl(get(), get()) }
    single { DealsConfigProvider() }
    single<DealsRepository> { DealsRepositoryImpl(get(), get()) }
    single { DealsNetworkDataSource(get(), ogAppKitHeadersPlugin(get())) }
}
