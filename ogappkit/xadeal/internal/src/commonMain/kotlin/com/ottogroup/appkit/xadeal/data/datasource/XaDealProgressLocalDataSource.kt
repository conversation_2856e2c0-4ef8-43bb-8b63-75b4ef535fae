package com.ottogroup.appkit.xadeal.data.datasource

import androidx.datastore.core.DataStore
import com.ottogroup.appkit.xadeal.model.DealProgressHolder
import kotlinx.coroutines.flow.Flow

internal class XaDealProgressLocalDataSource(private val dataStore: DataStore<DealProgressHolder>) {

    suspend fun storeProgress(showOverlay: Boolean, timeLeft: Long) {
        val data = DealProgressHolder(
            showOverlay = showOverlay,
            timeLeft = timeLeft
        )
        dataStore.updateData {
            data
        }
    }

    suspend fun clearProgress() {
        dataStore.updateData {
            DealProgressHolder()
        }
    }

    val dealProgress: Flow<DealProgressHolder> = dataStore.data
}
