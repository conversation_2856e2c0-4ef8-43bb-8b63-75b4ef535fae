package com.ottogroup.appkit.xadeal.utils

import com.ottogroup.appkit.xadeal.model.Campaign
import com.ottogroup.appkit.xadeal.model.Offer
import kotlinx.serialization.modules.SerializersModule
import kotlinx.serialization.modules.polymorphic
import kotlinx.serialization.modules.subclass

internal val CampaignModule = SerializersModule {
    polymorphic(Campaign.GameMechanic::class) {
        subclass(Campaign.GameMechanic.ShakeADeal::class)
        subclass(Campaign.GameMechanic.ScratchADeal::class)
        subclass(Campaign.GameMechanic.TapADeal::class)
        defaultDeserializer { Campaign.GameMechanic.Unknown.serializer() }
    }
}

internal val offerModule = SerializersModule {
    polymorphic(Offer::class) {
        subclass(Offer.Voucher::class)
        subclass(Offer.Product::class)
        defaultDeserializer { Offer.Unknown.serializer() }
    }
}
