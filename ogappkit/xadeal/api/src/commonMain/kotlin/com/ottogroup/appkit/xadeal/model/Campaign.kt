package com.ottogroup.appkit.xadeal.model

import com.ottogroup.kotlinx.serialization.LossyString
import kotlin.experimental.ExperimentalObjCName
import kotlin.native.ObjCName
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonClassDiscriminator

@Serializable
@OptIn(ExperimentalObjCName::class)
@ObjCName("XCampaign")
public data class Campaign(
    val id: String,
    val bannerId: String,
    val onboardingText: LossyString?,
    val offboardingText: LossyString?,
    val buttonText: LossyString?,
    val startDate: String,
    val endDate: String,
    val gameMechanic: GameMechanic
) {

    @Serializable
    @OptIn(ExperimentalSerializationApi::class)
    @JsonClassDiscriminator("type")
    public sealed interface GameMechanic {

        @Serializable
        @SerialName("ShakeADeal")
        public data class ShakeADeal(
            val countdown: Int
        ) : GameMechanic

        @Serializable
        @SerialName("ScratchADeal")
        public data class ScratchADeal(
            val scratchPercentage: Double
        ) : GameMechanic

        @Serializable
        @SerialName("TapADeal")
        public data object TapADeal : GameMechanic

        @Serializable
        public data object Unknown : GameMechanic
    }
}

@Serializable
public data class Campaigns(
    val campaigns: List<Campaign>
)
