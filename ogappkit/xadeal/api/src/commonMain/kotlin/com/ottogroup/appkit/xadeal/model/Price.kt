package com.ottogroup.appkit.xadeal.model

import kotlin.experimental.ExperimentalObjCName
import kotlin.native.ObjCName
import kotlinx.serialization.Serializable

@Serializable
@OptIn(ExperimentalObjCName::class)
@ObjCName("XPrice")
public data class Price(
    val value: Int,
    val oldValue: Int? = null,
    val currency: String,
    val priceRange: Boolean? = null,
    val discounted: Boolean? = null
)
