package com.ottogroup.appkit.xadeal

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.xadeal.config.XaDealConfig
import com.ottogroup.appkit.xadeal.model.Campaigns
import com.ottogroup.appkit.xadeal.model.DealProgressHolder
import com.ottogroup.appkit.xadeal.model.Offer
import com.ottogroup.appkit.xadeal.model.OffersHolder
import kotlinx.coroutines.flow.Flow

/**
 * The main entry point to XaDeal functionality. Obtain an instance from
 * the `OGAppKitSdk` object.
 */
public interface OGXaDeal {
    /**
     * Configures the XaDeal SDK. MUST be called before performing any other
     * operations.
     */
    public fun configure(config: XaDealConfig)

    /**
     * Gets the available campaigns for the configured tenant.
     */
    public fun getCampaigns(): Flow<Campaigns>

    /**
     * Gets the available offers for the selected campaign.
     */
    public suspend fun getOffers(campaignId: String): Result<OffersHolder>

    /**
     * Removes the offers for the selected campaign.
     */
    public fun removeCampaignOffers(campaignId: String)

    /**
     * Removes a single offers for the selected campaign.
     */
    public fun consumeOffer(campaignId: String, offer: Offer)

    /**
     * Gets the status of the current deal.
     */
    public fun getDealProgress(): Flow<DealProgressHolder>

    /**
     * Updates the status of the current deal.
     */
    public fun updateDealProgress(showOverlay: Boolean, timeLeft: Long)

    /**
     * Clears the status of the current deal.
     */
    public fun clearDealProgress()

    /**
     * Checks if deals can be shown today for the available campaigns.
     */
    public fun canShowDeals(): Flow<Map<String, Boolean>>

    /**
     * Clears the stored data for campaigns, offers and progress.
     */
    public suspend fun clearData()
}
