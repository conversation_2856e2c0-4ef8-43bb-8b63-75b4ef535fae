package com.ottogroup.appkit.xadeal.model

import com.ottogroup.kotlinx.serialization.LossyString
import kotlin.experimental.ExperimentalObjCName
import kotlin.native.ObjCName
import kotlinx.coroutines.flow.Flow
import kotlinx.serialization.ExperimentalSerializationApi
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonClassDiscriminator

@Serializable
@OptIn(ExperimentalSerializationApi::class, ExperimentalObjCName::class)
@JsonClassDiscriminator("type")
@ObjCName("XOffer")
public sealed interface Offer {
    public val id: String
    public val title: String
    public val url: String
    public val imageUrl: String
    public val legalText: LossyString?

    @Serializable
    @SerialName("voucher")
    public data class Voucher(
        override val id: String,
        override val title: String,
        override val url: String,
        override val imageUrl: String,
        override val legalText: LossyString?
    ) : Offer

    @Serializable
    @SerialName("product")
    public data class Product(
        override val id: String,
        override val title: String,
        override val url: String,
        override val imageUrl: String,
        override val legalText: LossyString?,
        val price: Price,
        val label: LossyString?,
        val rating: Rating? = null
    ) : Offer

    @Serializable
    public data class Unknown(
        override val id: String = "",
        override val title: String = "",
        override val url: String = "",
        override val imageUrl: String = "",
        override val legalText: LossyString?
    ) : Offer
}

@OptIn(ExperimentalObjCName::class)
@Serializable
@ObjCName("XOffers")
public data class Offers(
    val offers: List<Offer>
)

@OptIn(ExperimentalObjCName::class)
@Serializable
@ObjCName("XOffersHolder")
public data class OffersHolder(
    val newDeals: Boolean,
    val offers: Flow<Offers>
)
