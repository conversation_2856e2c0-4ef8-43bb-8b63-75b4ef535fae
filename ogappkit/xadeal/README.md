# XaDeal

How to use the XaDeal SDK.

## Obtain the `OGXaDeal` instance

```kotlin
// Android
val xaDeal = OGAppKitSdk.init(androidApplication()).xadeal()
```

```swift
// Swift
let xaDeal = OGAppKitSdk.shared.xadeal()
```

## Configure it

```kotlin
xaDeal.configure(
    XaDealConfig(
        baseUrl = config.apiUrl ?: "",
        locale = tenantRepository.tenant.identifier,
        apiKey = "apiKey"
    )
)
```

Where

- `baseUrl` is the base url defined in the app config
- `tenant` is the current tenant used by the app
- `apiKey` the apiKey defined for the XaDeal feature

## Request data

### Campaigns
After configuring the SDK, the apps should request the campaign data when they show the banners in the assortment. To do that
you should use `getCampaigns()`. This function will check the timestamp of the latest request and depending on its value do the
following:
- If the timestamp is from the current day, it will return the campaigns in the local storage.
- If it's not from the current day, it will request new campaigns to the API, store them in the local storage with the timestamp of
the request and return them. If the request fails it will return an empty list.

This data is exposed as a `Flow` in Kotlin, which is translated to an `AsyncSequence` in Swift.

#### Clear campaigns
When changing environment or tenant in the client apps a call to `clearCampaigns()` should be made to remove the stored
campaigns as they are no longer valid.

### Offers
When the user clicks on the campaign banner the offers for that specific campaign should be requested. To do that, call
`getOffers(campaignId)` with the corresponding campaignId. This function is suspend (async) and will return a `Result`
type, as it might be the case that an initial request fails, but a later retry or an update of cached data offers a value.
To work with `Result`, you can do the following:
```kotlin
// Kotlin
xaDeal.getOffers(campaignId = "123456").collect { offersHolder ->
    when (offersHolder) {
        is Result.Success -> handleSuccess(offersHolder.value)
        is Result.Failure<*> -> handleError(offersHolder.failure)
    }
}
```

```swift
// Swift
let offersHolder = xaDeal.getOffers(campaignId: "123456")
switch onEnum(of: offersHolder) {
  case .failure(let f):
    handleError(f.failure)
  case .success(let s):
    for await result in s.value.offers {
      handleSuccess(result)
    }
 }
```

The success return type is `OffersHolder`, which contains two fields:
- `newDeals`: Boolean that shows if the values are new (from the API) or not (from the local store). This is useful to know if
the client app should show the `OnBoarding` screen or go directly to the `DealScreen`.
- `offers`: Flow containing the offers available for the specified campaign.

#### Consume offer
When an offer is shown the user has two options: either accept it or wait until the timer runs out to get the next one. In both
cases, the client app should consume the offer to remove it from the local store calling`consumeOffer(campaignId: String, offer: Offer)`.

### Deal progress
For `ShakeADeal` game type we need to store the progress of the current deal; that means the remaining time and if the overlay
was already dismissed or not. To do that the SDK provides the following functions:
- `getDealProgress()`: Returns a Flow of `DealProgressHolder`, that contains a Boolean and a Long value. It should be
called from the client app when the deal screen is going to be shown.
- `updateDealProgress(showOverlay: Boolean, timeLeft: Long)`: Updates the local store with the specified data.
Should be called from the client app when the user closes the deal screen or puts the app in background while in the deal
screen.
- `clearDealProgress()`: Clears the deal progress data from the local storage. Should be called from the client apps when
a deal is accepted, the timer reaches 0, new deals come from the API or if the user switches environment or tenant.

### Deal availability
When there is a campaign running in the current date a banner will be shown in the `Assortment` screen of the client app.
However, we need to know if the banner is shown with the `active` status or not. To do that a check must be made to see if all
the stored offers for the campaign have been consumed and, if so, if the last request timestamp was not from today.

For that, the `canShowDeals()` function is provided. This function will return a Flow with a Map<String, Boolean>, that
represent each of the available campaigns with its campaignId and a Boolean value to show if offers can be shown or not.
This should be called from the client app when resolving the banner status in the `Assortment` screen.
