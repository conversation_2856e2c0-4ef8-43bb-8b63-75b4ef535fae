{"AppBuild": "App Build", "AppVersion": "App Version", "NSCameraUsageDescription": "Please allow access to your camera so the app can scan products.", "NSLocationAlwaysAndWhenInUseUsageDescription": "Location is required to deliver a better user experience.", "NSLocationAlwaysUsageDescription": "Location is required to deliver a better user experience.", "NSLocationUsageDescription": "Location is required to deliver a better user experience.", "NSMicrophoneUsageDescription": "By enabling speech recognition you can search via voice.", "NSPhotoLibraryAddUsageDescription": "Please allow access to your photo library in order to save your screenshot.", "NSPhotoLibraryUsageDescription": "The app saves photos and videos to your photo library.", "NSSpeechRecognitionUsageDescription": "The speech recognition is used to process your voice input.", "NSUserTrackingUsageDescription": "The tracking feature allows us and our partners to learn about your tastes and preferences so that we can send you advertising that is tailored to your needs.", "account.customerAccountEntry.subtitle": "Orderstatus, Balance, Bills", "account.customerAccountEntry.subtitle.YLFLNL": "Delivery Status, Orders, Bills, Balance", "account.customerAccountEntry.subtitle.YLFLSE": "Delivery Status, Orders, Bills, Balance", "account.customerAccountEntry.title": "Customer account", "account.customerAccountEntry.title.HEI": "My account", "account.customerAccountEntry.title.LAS.android": "My customeraccount", "account.customerAccountEntry.title.MAN.android": "My customeraccount", "account.customerAccountEntry.title.SAN.android": "My Account", "account.customerAccountEntry.title.SHE.android": "My customeraccount", "account.customerAccountEntry.title.SHE.ios": "My account", "account.customerAccountEntry.title.WIT.android": "My account", "account.customerAccountEntry.title.YLFLNL.android": "My account", "account.customerAccountEntry.title.YLFLNL.ios": "My Account", "account.customerAccountEntry.title.YLFLSE.android": "My account", "account.customerAccountEntry.title.YLFLSE.ios": "My Account", "account.loginButton.title": "<PERSON><PERSON>", "account.loginButton.title.SAN.ios": "Anmelden", "account.loginButton.title.SHE.ios": "Anmelden", "account.logout": "Logout", "account.logout.HEI.android": "Sign out", "account.logout.MAN": "Sign out", "account.logout.SAN.ios": "Abmelden", "account.logout.SHE.android": "Sign out", "account.logout.SHE.ios": "Abmelden", "account.rate.title": "Rate app", "account.settings.sectionTitle": "Settings", "account.settings.title": "App settings", "account.settings.title.BON.android": "Settings", "account.settings.title.BON.ios": "App settings", "account.settings.title.BPX.android": "Settings", "account.settings.title.BPX.ios": "App settings", "account.settings.title.HEI.android": "Settings", "account.settings.title.HEI.ios": "App-Einstellungen", "account.settings.title.LAS.android": "Settings", "account.settings.title.LAS.ios": "App-Einstellungen", "account.settings.title.MAN.android": "Settings", "account.settings.title.MAN.ios": "App settings", "account.settings.title.SAN.android": "Settings", "account.settings.title.SAN.ios": "App settings", "account.settings.title.SHE.android": "Settings", "account.settings.title.SHE.ios": "App-Einstellungen", "account.settings.title.WIT.android": "Settings", "account.settings.title.WIT.ios": "App settings", "account.settings.title.YLFLNL.android": "Settings", "account.settings.title.YLFLNL.ios": "App settings", "account.settings.title.YLFLSE.android": "Settings", "account.settings.title.YLFLSE.ios": "App settings", "account.title": "Customer Account", "account.title.HEI.android": "My Account", "account.title.HEI.ios": "My account", "account.title.LAS.android": "My Account", "account.title.LAS.ios": "Customer Account ", "account.title.MAN.android": "My Account", "account.title.MAN.ios": "Customer Account ", "account.title.SAN.android": "My Account", "account.title.SAN.ios": "Kundenkonto", "account.title.SHE.android": "My Account", "account.title.SHE.ios": "Kundenkonto", "account.title.WIT.android": "My Account", "account.title.WIT.ios": "Account", "account.title.YLFLNL": "My Account", "account.title.YLFLSE": "My Account", "assortment.categories.accessibility": "Categories", "assortment.categoriesList.item.accessibility": "%{itemName} - %{itemNumber} of %{itemCount} - in the categories list", "assortment.listTitle.accessibility": "Categories list", "assortment.newsletter.title": "Newsletter", "assortment.orderForm.title": "Order form", "assortment.outlet.title": "Outlet", "assortment.services.accessibility": "Services", "assortment.services.title": "More services", "assortment.services.title.BON": "Other services", "assortment.services.title.BPX": "Other services", "assortment.services.title.CRE": "Other services", "assortment.services.title.HEI": "Other services", "assortment.services.title.LAS": "Other services", "assortment.services.title.MAN": "Other services", "assortment.services.title.SAN": "Other services", "assortment.services.title.SHE": "Other services", "assortment.services.title.WIT": "Other services", "assortment.services.title.YLFLNL": "Other services", "assortment.services.title.YLFLSE": "Other services", "assortment.tabList.activeTab.accessibility": "Active %{tabName} - Tab %{tabNumber} of %{tabCount}", "assortment.tabList.inactiveTab.accessibility": "%{tabName} - Tab %{tabNumber} from %{tabCount} - double tap to activate", "assortment.title": "Assortment", "bfg.category.button": "Show size recommendation", "bfg.category.button.HEI.ios": "GRÖSSENEMPFEHLUNG ANSEHEN", "bfg.category.button.LAS.ios": "Größenempfehlung ansehen", "bfg.category.button.MAN.ios": "GRÖSSENEMPFEHLUNG ANSEHEN", "bfg.category.button.SAN.ios": "GRÖSSENEMPFEHLUNG ANSEHEN", "bfg.category.button.SHE.ios": "GRÖSSENEMPFEHLUNG ANSEHEN", "bfg.category.button.WIT.ios": "GRÖSSENEMPFEHLUNG ANSEHEN", "bfg.category.button.YLFLNL.ios": "GRÖSSENEMPFEHLUNG ANSEHEN", "bfg.category.button.YLFLSE.ios": "GRÖSSENEMPFEHLUNG ANSEHEN", "bfg.contentDescription.closeButton": "Icon for closing", "bfg.contentDescription.infoButton": "Icon that displays more info", "bfg.continue.from.to.description": "Continue to step %{to} of %{of}", "bfg.continue.lastStep.description": "Continue to last step", "bfg.continue.lastStep.description.LAS.ios": "<PERSON>ter zum letzten Schritt", "bfg.continue.lastStep.description.SHE.ios": "WEITER ZUM LETZTEN SCHRITT", "bfg.cup.details.text.loose": "If the cup edge of your bra sticks out or the material wrinkles, they are too big.", "bfg.cupFit.title": "How do the cups fit?", "bfg.cupFit.title.LAS.ios": "Wie passen die Cups?", "bfg.cupFit.title.SHE.ios": "WIE PASSEN DIE CUPS?", "bfg.cupSize.loose.description": "If the cup edge of your bra sticks out or the material wrinkles, they are too big.", "bfg.cupSize.loose.description.HEI.ios": "Wenn die Cup-Kante deines BHs absteht oder das Material Falten wirft, sind diese zu groß gewählt.", "bfg.cupSize.loose.description.LAS.ios": "Wenn die Cup-Kante deines BHs absteht oder das Material Falten wirft, sind diese zu groß gewählt.", "bfg.cupSize.loose.description.MAN.ios": "Wenn die Cup-Kante deines BHs absteht oder das Material Falten wirft, sind diese zu groß gewählt.", "bfg.cupSize.loose.description.SAN.ios": "Wenn die Cup-Kante deines BHs absteht oder das Material Falten wirft, sind diese zu groß gewählt.", "bfg.cupSize.loose.description.SHE.ios": "Wenn die Cup-Kante deines BHs absteht oder das Material Falten wirft, sind diese zu groß gewählt.", "bfg.cupSize.loose.description.WIT.ios": "Wenn die Cup-Kante deines BHs absteht oder das Material Falten wirft, sind diese zu groß gewählt.", "bfg.cupSize.loose.description.YLFLNL.ios": "Wenn die Cup-Kante deines BHs absteht oder das Material Falten wirft, sind diese zu groß gewählt.", "bfg.cupSize.loose.description.YLFLSE.ios": "Wenn die Cup-Kante deines BHs absteht oder das Material Falten wirft, sind diese zu groß gewählt.", "bfg.cupSize.loose.title": "The cups are too big and the cup edge stands off", "bfg.cupSize.loose.title.LAS.ios": "Die Cups sind zu groß und die Cup-Kante steht ab", "bfg.cupSize.loose.title.SHE.ios": "DIE CUPS SIND ZU GROß UND DIE CUP-KANTE STEHT AB", "bfg.cupSize.small.LAS.ios": "zu klein", "bfg.cupSize.small.SHE.ios": "zu klein", "bfg.cupSize.small.title.LAS.ios": "Die Cups sind zu klein", "bfg.cupSize.small.title.SHE.ios": "DIE CUPS SIND ZU KLEIN", "bfg.cupSize.tight": "too small", "bfg.cupSize.tight.description": "The cups on your bra are too small if the chest is not properly enclosed and swells out both at the top and sides. Underwired bras can cause pressure points on the chest.", "bfg.cupSize.tight.description.LAS.ios": "<PERSON>ut sitzende Cups umschließen die Brust vollständig. Nichts drückt oder steht ab.", "bfg.cupSize.tight.description.SHE.ios": "<PERSON>ut sitzende Cups umschließen die Brust vollständig. Nichts drückt oder steht ab.", "bfg.cupSize.tight.title": "The cups are too small", "bfg.cupSize.underbust": "My underbust measurement", "bfg.cupSize.underbust.LAS.ios": "<PERSON><PERSON>", "bfg.cupSize.underbust.SHE.ios": "<PERSON><PERSON>", "bfg.cupSize.well": "fits well", "bfg.cupSize.well.LAS.ios": "sitzen gut", "bfg.cupSize.well.SHE.ios": "sitzen gut", "bfg.cupSize.well.description": "Well fitting cups completely enclose the chest. Nothing pushes or stands up.", "bfg.cupSize.well.description.LAS.ios": "<PERSON>ut sitzende Cups umschließen die Brust vollständig. Nichts drückt oder steht ab.", "bfg.cupSize.well.description.SHE.ios": "<PERSON>ut sitzende Cups umschließen die Brust vollständig. Nichts drückt oder steht ab.", "bfg.cupSize.well.title": "The cups fit well", "bfg.cupSize.well.title.LAS.ios": "Die Cups sitzen gut", "bfg.cupSize.well.title.SHE.ios": "DIE CUPS SITZEN GUT", "bfg.cupSizeFit.loose": "too loose", "bfg.cupSizeFit.title": "What size do you wear?", "bfg.cupSizeFit.title.LAS.ios": "Welche Größe\nträgst du?", "bfg.cupSizeFit.title.SHE.ios": "WELCHE GRÖSSE\nTRÄGST DU?", "bfg.cupSizeFit.wide": "too big", "bfg.cupSizeFit.wide.LAS.ios": "zu groß", "bfg.cupSizeFit.wide.SHE.ios": "zu groß", "bfg.details.tooLoose.description.HEI.ios": "Wenn das Unterbrustband nicht optimal am Körper anliegt, rutscht es im Rücken nach oben und bietet keinen Halt für die Brust.", "bfg.details.tooLoose.description.LAS.ios": "Wenn das Unterbrustband nicht optimal am Körper anliegt, rutscht es im Rücken nach oben und bietet keinen Halt für die Brust.", "bfg.details.tooLoose.description.MAN.ios": "Wenn das Unterbrustband nicht optimal am Körper anliegt, rutscht es im Rücken nach oben und bietet keinen Halt für die Brust.", "bfg.details.tooLoose.description.SAN.ios": "Wenn das Unterbrustband nicht optimal am Körper anliegt, rutscht es im Rücken nach oben und bietet keinen Halt für die Brust.", "bfg.details.tooLoose.description.SHE.ios": "Wenn das Unterbrustband nicht optimal am Körper anliegt, rutscht es im Rücken nach oben und bietet keinen Halt für die Brust.", "bfg.details.tooLoose.description.WIT.ios": "Wenn das Unterbrustband nicht optimal am Körper anliegt, rutscht es im Rücken nach oben und bietet keinen Halt für die Brust.", "bfg.details.tooLoose.description.YLFLNL.ios": "Wenn das Unterbrustband nicht optimal am Körper anliegt, rutscht es im Rücken nach oben und bietet keinen Halt für die Brust.", "bfg.details.tooLoose.description.YLFLSE.ios": "Wenn das Unterbrustband nicht optimal am Körper anliegt, rutscht es im Rücken nach oben und bietet keinen Halt für die Brust.", "bfg.error.recommendation.description": "Unfortunately no size recommendation could be determined. Just have a look around.", "bfg.error.recommendation.description.LAS.ios": "Leider sind aktuell keine Produkte in deiner Größe verfügbar. Wir empfehlen eine persönliche Beratung in einer unserer Filialen.", "bfg.error.recommendation.description.SHE.ios": "Leider sind aktuell keine Produkte in deiner Größe verfügbar. Wir empfehlen eine persönliche Beratung in einer unserer Filialen.", "bfg.fitHelper.cupSize.accessibility": "Helper for cupSize", "bfg.fitHelper.resultInfo.tip.accessibility": "Tipp %{tipNumber}", "bfg.fitHelper.resultInfo.title.accessibility": "Helper for result info", "bfg.fitHelper.underbust.accessibility": "Helper overlay for underbust band fit", "bfg.loading.isLoaded.accessibility": "Is loaded", "bfg.moreInfo.accessibility": "Tap for more information", "bfg.outcome.findStore.title": "Find store", "bfg.outcome.findStore.title.HEI.ios": "FILIALE FINDEN", "bfg.outcome.findStore.title.LAS.ios": "Store finden", "bfg.outcome.findStore.title.MAN.ios": "FILIALE FINDEN", "bfg.outcome.findStore.title.SAN.ios": "FILIALE FINDEN", "bfg.outcome.findStore.title.SHE.ios": "FILIALE FINDEN", "bfg.outcome.findStore.title.WIT.ios": "FILIALE FINDEN", "bfg.outcome.findStore.title.YLFLNL.ios": "FILIALE FINDEN", "bfg.outcome.findStore.title.YLFLSE.ios": "FILIALE FINDEN", "bfg.outcome.inspiration.title": "Let us inspire you", "bfg.outcome.inspiration.title.LAS.ios": "<PERSON><PERSON><PERSON><PERSON><PERSON> lassen", "bfg.outcome.inspiration.title.SHE.ios": "INSPIRIEREN LASSEN", "bfg.outcome.result.title": "Our size recommendation", "bfg.outcome.result.title.LAS.ios": "Unsere Größenempfehlung", "bfg.outcome.result.title.SHE.ios": "UNSERE GRÖSSENEMPFEHLUNG", "bfg.outcome.showMatchingProducts": "Show matching products", "bfg.outcome.showMatchingProducts.HEI.ios": "PASSENDE PRODUKTE ANZEIGEN", "bfg.outcome.showMatchingProducts.LAS.ios": "Passende Produkte anzeigen", "bfg.outcome.showMatchingProducts.MAN.ios": "PASSENDE PRODUKTE ANZEIGEN", "bfg.outcome.showMatchingProducts.SAN.ios": "PASSENDE PRODUKTE ANZEIGEN", "bfg.outcome.showMatchingProducts.SHE.ios": "PASSENDE PRODUKTE ANZEIGEN", "bfg.outcome.showMatchingProducts.WIT.ios": "PASSENDE PRODUKTE ANZEIGEN", "bfg.outcome.showMatchingProducts.YLFLNL.ios": "PASSENDE PRODUKTE ANZEIGEN", "bfg.outcome.showMatchingProducts.YLFLSE.ios": "PASSENDE PRODUKTE ANZEIGEN", "bfg.outcome.showProducts.title": "Show matching products", "bfg.outcome.showProducts.title.LAS.ios": "Passende Produkte anzeigen", "bfg.outcome.showProducts.title.SHE.ios": "PASSENDE PRODUKTE ANZEIGEN", "bfg.page.title.accessibility": "Bra fitting guide step %{currentStep} of %{totalStep}", "bfg.result.contentDescription.mainImage": "Bra selection image", "bfg.result.details.title": "3 tips for the right bra fitting", "bfg.result.details.title.HEI.ios": "3 TIPPS ZUM RICHTIGEN BH-FITTING", "bfg.result.details.title.LAS.ios": "3 Tipps zum richtigen BH-Fitting", "bfg.result.details.title.MAN.ios": "3 TIPPS ZUM RICHTIGEN BH-FITTING", "bfg.result.details.title.SAN.ios": "3 TIPPS ZUM RICHTIGEN BH-FITTING", "bfg.result.details.title.WIT.ios": "3 TIPPS ZUM RICHTIGEN BH-FITTING", "bfg.result.details.title.YLFLNL.ios": "3 TIPPS ZUM RICHTIGEN BH-FITTING", "bfg.result.details.title.YLFLSE.ios": "3 TIPPS ZUM RICHTIGEN BH-FITTING", "bfg.result.hint.description": "When you try on your bra, you should consider the following things again for the perfect fit:", "bfg.result.hint.description.LAS.ios": "Wenn du deinen BH anprobierst, solltest du für den perfekten Sitz noch einmal folgende Dinge berücksichtigen:", "bfg.result.hint.item1": "The chest must be placed in the cup. Raise the chest (breasts) laterally from below into the cup (to the correct position).", "bfg.result.hint.item1.LAS.ios": "Die Brust muss im Cup platziert werden. <PERSON><PERSON> die Brust (Brüste) seitlich von unten in das Cup hinein (an die richtige Position).", "bfg.result.hint.item1.SHE.ios": "Die Brust muss im Cup platziert werden. <PERSON><PERSON> die Brust (Brüste) seitlich von unten in das Cup hinein (an die richtige Position).", "bfg.result.hint.item2": "Adjust the length of the straps so that the straps do not cut into the shoulders and the cup is pulled upwards at an angle or lie loose and slip (slide down).", "bfg.result.hint.item2.LAS.ios": "Stelle deine Trägerlänge  so ein, dass die Träger nicht an den Schultern einschneiden und das Cup schräg nach oben verzogen wird oder lose liegen und verrutschen (runterrutschen).", "bfg.result.hint.item2.SHE.ios": "Stelle deine Trägerlänge  so ein, dass die Träger nicht an den Schultern einschneiden und das Cup schräg nach oben verzogen wird oder lose liegen und verrutschen (runterrutschen).", "bfg.result.hint.item3": "Adjust the underbust width via the lock. Select the optimum row of hooks for a comfortable fit.", "bfg.result.hint.item3.LAS.ios": "Reguliere die Unterbrustweite über den Verschluss. Wähle die optimale Hakenreihe für einen komfortablen Halt.", "bfg.result.hint.item3.SHE.ios": "Reguliere die Unterbrustweite über den Verschluss. Wähle die optimale Hakenreihe für einen komfortablen Halt.", "bfg.result.title.accessibility": "Helper for underbust", "bfg.segmentedControl.cupSize.accessibility": "the cup size", "bfg.segmentedControl.underbust.accessibility": "the underbust band", "bfg.sizeSelection.contentDescription.sizeSelectionImage": "Image of size selection screen", "bfg.sizeSelection.cupSize": "My cup size", "bfg.sizeSelection.cupSize.HEI.android": "MY CUP SIZE", "bfg.sizeSelection.cupSize.HEI.ios": "Meine Cup Größe", "bfg.sizeSelection.cupSize.LAS.android": "MY CUP SIZE", "bfg.sizeSelection.cupSize.LAS.ios": "Meine Cup Größe", "bfg.sizeSelection.cupSize.MAN.android": "MY CUP SIZE", "bfg.sizeSelection.cupSize.MAN.ios": "Meine Cup Größe", "bfg.sizeSelection.cupSize.SAN.android": "MY CUP SIZE", "bfg.sizeSelection.cupSize.SAN.ios": "Meine Cup Größe", "bfg.sizeSelection.cupSize.SHE.android": "MY CUP SIZE", "bfg.sizeSelection.cupSize.SHE.ios": "Meine Cup Größe", "bfg.sizeSelection.cupSize.WIT.android": "MY CUP SIZE", "bfg.sizeSelection.cupSize.WIT.ios": "Meine Cup Größe", "bfg.sizeSelection.cupSize.YLFLNL.android": "MY CUP SIZE", "bfg.sizeSelection.cupSize.YLFLNL.ios": "Meine Cup Größe", "bfg.sizeSelection.cupSize.YLFLSE.android": "MY CUP SIZE", "bfg.sizeSelection.cupSize.YLFLSE.ios": "Meine Cup Größe", "bfg.sizeSelection.measure.button": "I would like to measure my size", "bfg.sizeSelection.measure.button.HEI.ios": "Ich möchte meine Größe ausmessen", "bfg.sizeSelection.measure.button.LAS.ios": "Ich möchte meine Größe ausmessen", "bfg.sizeSelection.measure.button.MAN.ios": "Ich möchte meine Größe ausmessen", "bfg.sizeSelection.measure.button.SAN.ios": "Ich möchte meine Größe ausmessen", "bfg.sizeSelection.measure.button.SHE.ios": "Ich möchte meine Größe ausmessen", "bfg.sizeSelection.measure.button.WIT.ios": "Ich möchte meine Größe ausmessen", "bfg.sizeSelection.measure.button.YLFLNL.ios": "Ich möchte meine Größe ausmessen", "bfg.sizeSelection.measure.button.YLFLSE.ios": "Ich möchte meine Größe ausmessen", "bfg.sizeSelection.underbustSize": "MY UNDERBUST SIZE", "bfg.slider.instructions.accessibility": "Swipe up or down with one finger to adjust the value", "bfg.slider.valueChanged.accessibility": "New value: %{newValue}", "bfg.type.all.title": "I am interested in/nall bra types", "bfg.type.all.title.LAS.ios": "Ich interessiere mich für alle BH-Typen", "bfg.type.attractive.title": "I want to be \nin the limelight", "bfg.type.attractive.title.LAS.ios": "<PERSON>ch will mich in <PERSON><PERSON>e setzen", "bfg.type.comfort.title": "I prefer bras\nwithout bra reins.", "bfg.type.comfort.title.LAS.ios": "Ich bevorzuge Bügellose-BHs", "bfg.type.hide.title": "He should hide\na little.", "bfg.type.hide.title.LAS.ios": "Er sollte ein bisschen kaschieren", "bfg.type.support.title": "I prefer\nunderwired bras.", "bfg.type.support.title.LAS.ios": "Ich bevorzuge Bügel-BHs", "bfg.type.title": "What is particularly important\nto you about your bra?", "bfg.type.title.LAS.ios": "Was ist dir an deinem BH besonders wichtig?", "bfg.type.title.SHE.ios": "WAS IST DIR AN DEINEM BH BESONDERS WICHTIG?", "bfg.underbust.details.ok.description.LAS.ios": "Ein optimal gewähltes Unterbrustband schmiegt sich bequem an den Körper an. Nichts schneidet ein.", "bfg.underbust.details.ok.description.SHE.ios": "Ein optimal gewähltes Unterbrustband schmiegt sich bequem an den Körper an. Nichts schneidet ein.", "bfg.underbust.tight": "too tight", "bfg.underbust.tight.HEI.ios": "zu eng", "bfg.underbust.tight.LAS.ios": "zu eng", "bfg.underbust.tight.MAN.ios": "zu eng", "bfg.underbust.tight.SAN.ios": "zu eng", "bfg.underbust.tight.SHE.ios": "zu eng", "bfg.underbust.tight.WIT.ios": "zu eng", "bfg.underbust.tight.YLFLNL.ios": "zu eng", "bfg.underbust.tight.YLFLSE.ios": "zu eng", "bfg.underbust.title": "How's the underbust band feel?", "bfg.underbust.title.LAS.ios": "Wie fühlt sich das Unterbrustband an?", "bfg.underbust.title.SHE.ios": "WIE FÜHLT SICH DAS UNTERBRUSTBAND AN?", "bfg.underbustFit.details.loose.title.HEI.ios": "DAS UNTERBRUSTBRAND IST ZU LOSE UND RUTSCHT NACH OBEN", "bfg.underbustFit.details.loose.title.LAS.ios": "Das Unterbrustbrand ist zu lose und rutscht nach oben", "bfg.underbustFit.details.loose.title.MAN.ios": "DAS UNTERBRUSTBRAND IST ZU LOSE UND RUTSCHT NACH OBEN", "bfg.underbustFit.details.loose.title.SAN.ios": "DAS UNTERBRUSTBRAND IST ZU LOSE UND RUTSCHT NACH OBEN", "bfg.underbustFit.details.loose.title.SHE.ios": "DAS UNTERBRUSTBRAND IST ZU LOSE UND RUTSCHT NACH OBEN", "bfg.underbustFit.details.loose.title.WIT.ios": "DAS UNTERBRUSTBRAND IST ZU LOSE UND RUTSCHT NACH OBEN", "bfg.underbustFit.details.loose.title.YLFLNL.ios": "DAS UNTERBRUSTBRAND IST ZU LOSE UND RUTSCHT NACH OBEN", "bfg.underbustFit.details.loose.title.YLFLSE.ios": "DAS UNTERBRUSTBRAND IST ZU LOSE UND RUTSCHT NACH OBEN", "bfg.underbustFit.details.tight.description.HEI.ios": "Durch ein zu eng sitzendes Unterbrustband bilden sich Abdrücke unterhalb der Brust und auf dem Rücken.", "bfg.underbustFit.details.tight.description.LAS.ios": "Durch ein zu eng sitzendes Unterbrustband bilden sich Abdrücke unterhalb der Brust und auf dem Rücken.", "bfg.underbustFit.details.tight.description.MAN.ios": "Durch ein zu eng sitzendes Unterbrustband bilden sich Abdrücke unterhalb der Brust und auf dem Rücken.", "bfg.underbustFit.details.tight.description.SAN.ios": "Durch ein zu eng sitzendes Unterbrustband bilden sich Abdrücke unterhalb der Brust und auf dem Rücken.", "bfg.underbustFit.details.tight.description.SHE.ios": "Durch ein zu eng sitzendes Unterbrustband bilden sich Abdrücke unterhalb der Brust und auf dem Rücken.", "bfg.underbustFit.details.tight.description.WIT.ios": "Durch ein zu eng sitzendes Unterbrustband bilden sich Abdrücke unterhalb der Brust und auf dem Rücken.", "bfg.underbustFit.details.tight.description.YLFLNL.ios": "Durch ein zu eng sitzendes Unterbrustband bilden sich Abdrücke unterhalb der Brust und auf dem Rücken.", "bfg.underbustFit.details.tight.description.YLFLSE.ios": "Durch ein zu eng sitzendes Unterbrustband bilden sich Abdrücke unterhalb der Brust und auf dem Rücken.", "bfg.underbustFit.loose": "too loose", "bfg.underbustFit.loose.HEI.ios": "zu lose", "bfg.underbustFit.loose.LAS.ios": "zu lose", "bfg.underbustFit.loose.MAN.ios": "zu lose", "bfg.underbustFit.loose.SAN.ios": "zu lose", "bfg.underbustFit.loose.SHE.ios": "zu lose", "bfg.underbustFit.loose.WIT.ios": "zu lose", "bfg.underbustFit.loose.YLFLNL.ios": "zu lose", "bfg.underbustFit.loose.YLFLSE.ios": "zu lose", "bfg.underbustFit.loose.description": "If the underbust band does not fit the body perfectly, it will slip up the back and not provide support for the breast.", "bfg.underbustFit.loose.title": "The underbust band is too loose", "bfg.underbustFit.tight.description": "An underbust band that is too tight will cause marks to form below the chest and on the back.", "bfg.underbustFit.tight.title": "The underbust band is too tight", "bfg.underbustFit.tight.title.HEI.ios": "DAS UNTERBRUSTBAND IST ZU ENG", "bfg.underbustFit.tight.title.LAS.ios": "Das Unterbrustband ist zu eng", "bfg.underbustFit.tight.title.MAN.ios": "DAS UNTERBRUSTBAND IST ZU ENG", "bfg.underbustFit.tight.title.SAN.ios": "DAS UNTERBRUSTBAND IST ZU ENG", "bfg.underbustFit.tight.title.SHE.ios": "DAS UNTERBRUSTBAND IST ZU ENG", "bfg.underbustFit.tight.title.WIT.ios": "DAS UNTERBRUSTBAND IST ZU ENG", "bfg.underbustFit.tight.title.YLFLNL.ios": "DAS UNTERBRUSTBAND IST ZU ENG", "bfg.underbustFit.tight.title.YLFLSE.ios": "DAS UNTERBRUSTBAND IST ZU ENG", "bfg.underbustFit.well": "fits well", "bfg.underbustFit.well.HEI.ios": "sitzt gut", "bfg.underbustFit.well.LAS.ios": "sitzt gut", "bfg.underbustFit.well.MAN.ios": "sitzt gut", "bfg.underbustFit.well.SAN.ios": "sitzt gut", "bfg.underbustFit.well.SHE.ios": "sitzt gut", "bfg.underbustFit.well.WIT.ios": "sitzt gut", "bfg.underbustFit.well.YLFLNL.ios": "sitzt gut", "bfg.underbustFit.well.YLFLSE.ios": "sitzt gut", "bfg.underbustFit.well.description": "An optimally selected underbust band nestles comfortably to the body. Nothing cuts in.", "bfg.underbustFit.well.title": "The underbust band fits well", "bfg.underbustFit.well.title.LAS.ios": "Das Unterbrustband sitzt gut", "bfg.underbustFit.well.title.SHE.ios": "DAS UNTERBRUSTBAND SITZT GUT", "catalogScanner.cameraPermission.settings.button": "Change settings", "catalogScanner.cameraPermission.text": "Camera permission required for this feature", "catalogScanner.cameraPermission.text.LAS.ios": "Die App benötigt Zugriff auf deine Kamera, um Katalogseiten zu erfassen.", "catalogScanner.cameraPermission.title": "Camera permission", "catalogScanner.errorDialog.button": "Try again", "catalogScanner.errorDialog.button.LAS.ios": "Noch<PERSON> versuchen", "catalogScanner.errorDialog.message": "Please try again", "catalogScanner.errorDialog.message.LAS.ios": "Bitte versuche es nochmal", "catalogScanner.errorDialog.title": "The catalog couldn't be loaded", "catalogScanner.errorDialog.title.LAS.ios": "Der Katalog konnte nicht geladen werden", "catalogScanner.hint.activity": "Please be patient for a moment.\nThe catalog is loading", "catalogScanner.hint.message": "Our tip: Make sure there is sufficient light & a flat surface for the catalog", "catalogScanner.hint.message.LAS.android": "Our tip Make sure there is enough light and a flat surface for the catalog", "catalogScanner.hint.message.LAS.ios": "Unser Tipp: Achte auf ausreichend Licht & eine ebene Auflagefläche für den Katalog", "catalogScanner.navigation.title": "Catalog Scanner", "catalogScanner.navigation.title.LAS.ios": "Katalogscanner", "catalogScanner.noProducts.hint": "There are no products in this page, please try another one.", "catalogScanner.onboarding.message": "Find catalog products now in the online shop", "catalogScanner.onboarding.orderbutton": "Order catalog", "catalogScanner.onboarding.orderbutton.LAS.ios": "Aktuellen Katalog gratis an<PERSON>", "catalogScanner.onboarding.scanButton": "Scan catalog page", "catalogScanner.onboarding.scanButton.LAS.ios": "Katalogseite erfassen", "catalogScanner.onboarding.scanPage.title": "Capture Catalog Page", "catalogScanner.onboarding.scanPage.title.LAS.ios": "Katalogseite erfassen", "catalogScanner.onboarding.text": "Find catalog products now in the online store.", "catalogScanner.onboarding.text.LAS.android": "Copy needs to be brief for the user", "catalogScanner.onboarding.text.LAS.ios": "Katalogprodukte jetzt im Online Shop finden.", "catalogScanner.onboarding.title": "Fall in love, scan, order!", "catalogScanner.onboarding.title.LAS.android": "Fall in love, scan & order!", "catalogScanner.onboarding.title.LAS.ios": "<PERSON><PERSON><PERSON><PERSON>, scannen, bestellen!", "catalogScanner.products.title": "Results for page %{catalogPage}", "catalogScanner.products.title.LAS.android": "Results for page %{catalogPage}", "catalogScanner.products.title.LAS.ios": "Ergebnisse für Seite %{catalogPage}", "catalogScanner.scan.description": "If the scan was successful, results will be displayed automatically", "catalogScanner.scan.title": "The catalog page must be placed within the specified frame below", "catalogScanner.scan.title.LAS.android": "Capture product page", "catalogScanner.search.button": "Scan article in catalogue", "catalogScanner.title": "Catalog Scanner", "catalogScanner.title.LAS.ios": "Katalogscanner", "catalogScanner.video.accessibility": "Campaign Video", "checkout.quitDialog.confirm": "Cancel purchase", "checkout.quitDialog.title": "Do you really want to cancel your purchase?", "checkout.title": "Purchase", "countryList.regionChangeHint.accessibility": "On tap, the region will be changed and you will be redirected to home screen", "countrySelection.dialog.cancelButton": "Cancel", "countrySelection.dialog.message": "This link guides into %{name} shop %{newShop} but you have chosen the shop %{currentShop}. Do you want to switch from %{currentShop} to %{name} shop %{newShop} or do you want to open the link in the browser?", "countrySelection.dialog.message.BON.android": "This link guides into bonprix shop %{newShop} but you have chosen the shop %{currentShop}. Do you want to switch from %{currentShop} to bonprix shop %{newShop} or do you want to open the link in the browser?", "countrySelection.dialog.message.BON.ios": "Your link is related to the bonprix shop %{newShop}. Your current setting is %{currentShop}. Do you want to change the country or open the link in the browser?", "countrySelection.dialog.message.BPX.android": "This link guides into bonprix shop %{newShop} but you have chosen the shop %{currentShop}. Do you want to switch from %{currentShop} to bonprix shop %{newShop} or do you want to open the link in the browser?", "countrySelection.dialog.message.BPX.ios": "Your link is related to the bonprix shop %{newShop}. Your current setting is %{currentShop}. Do you want to change the country or open the link in the browser?", "countrySelection.dialog.message.HEI.android": "This link guides into bonprix shop %{newShop} but you have chosen the shop %{currentShop}. Do you want to switch from %{currentShop} to %{name} shop %{newShop} or do you want to open the link in the browser?", "countrySelection.dialog.message.HEI.ios": "Ihr Link führt in den %{name} Shop %{newShop}. In der App ist derzeit %{currentShop} ausgewählt. Möchten Sie das Land ändern oder den Link im Browser öffnen?", "countrySelection.dialog.message.LAS.android": "This link guides into bonprix shop %{newShop} but you have chosen the shop %{currentShop}. Do you want to switch from %{currentShop} to %{name} shop %{newShop} or do you want to open the link in the browser?", "countrySelection.dialog.message.LAS.ios": "Ihr Link führt in den %{name} Shop %{newShop}. In der App ist derzeit %{currentShop} ausgewählt. Möchten Sie das Land ändern oder den Link im Browser öffnen?", "countrySelection.dialog.message.MAN.android": "This link guides into Manufactum shop %{newShop} but you have chosen the shop %{currentShop}. Do you want to switch from %{currentShop} to Manufactum shop %{newShop} or do you want to open the link in the browser?", "countrySelection.dialog.message.MAN.ios": "Your link will take you to the Manufactum Shop %{newShop}. The app currently has %{currentShop} selected. Do you want to change the country or open the link in the browser?", "countrySelection.dialog.message.SAN.android": "This link guides into Sieh an! shop %{newShop} but you have chosen the shop %{currentShop}. Do you want to switch from %{currentShop} to %{name} shop %{newShop} or do you want to open the link in the browser?", "countrySelection.dialog.message.SAN.ios": "Your link leads to the Sieh an! Shop %{newShop}. The app currently has %{currentShop} selected. Would you like to change the country or open the link in the browser?", "countrySelection.dialog.message.SHE.android": "This link guides into Sheego shop %{newShop} but you have chosen the shop %{currentShop}. Do you want to switch from %{currentShop} to %{name} shop %{newShop} or do you want to open the link in the browser?", "countrySelection.dialog.message.SHE.ios": "Your link leads to the Sheego Shop %{newShop}. The app currently has %{currentShop} selected. Would you like to change the country or open the link in the browser?", "countrySelection.dialog.message.WIT.android": "Your link leads to the shop %{newShop}. %{currentShop} is currently selected in the app. Would you like to change the country or open the link in the browser?", "countrySelection.dialog.message.WIT.ios": "Your link leads to the Witt Shop %{newShop}. The app currently has %{currentShop} selected. Would you like to change the country or open the link in the browser?", "countrySelection.dialog.message.YLFLNL.android": "This link guides into %{name} shop %{newShop} but you have chosen the shop %{currentShop}. Do you want to switch from %{currentShop} to %{name} shop %{newShop} or do you want to open the link in the browser?", "countrySelection.dialog.message.YLFLNL.ios": "Your link leads to the %{name} Shop %{newShop}. The app currently has %{currentShop} selected. Would you like to change the country or open the link in the browser?", "countrySelection.dialog.message.YLFLSE.android": "This link guides into %{name} shop %{newShop} but you have chosen the shop %{currentShop}. Do you want to switch from %{currentShop} to %{name} shop %{newShop} or do you want to open the link in the browser?", "countrySelection.dialog.message.YLFLSE.ios": "Your link leads to the %{name} Shop %{newShop}. The app currently has %{currentShop} selected. Would you like to change the country or open the link in the browser?", "countrySelection.dialog.neutralButton": "Open in browser", "countrySelection.dialog.neutralButton.BON.android": "Open in Browser", "countrySelection.dialog.neutralButton.BPX.android": "Open in Browser", "countrySelection.dialog.neutralButton.HEI.android": "Open in Browser", "countrySelection.dialog.neutralButton.HEI.ios": "<PERSON><PERSON>", "countrySelection.dialog.neutralButton.LAS.android": "Open in Browser", "countrySelection.dialog.neutralButton.LAS.ios": "<PERSON><PERSON>", "countrySelection.dialog.neutralButton.MAN.android": "Open in Browser", "countrySelection.dialog.neutralButton.SAN.android": "Open in Browser", "countrySelection.dialog.neutralButton.SHE.android": "Open in Browser", "countrySelection.dialog.neutralButton.SHE.ios": "<PERSON><PERSON>", "countrySelection.dialog.neutralButton.WIT.android": "Open in Browser", "countrySelection.dialog.neutralButton.YLFLNL.android": "Open in Browser", "countrySelection.dialog.neutralButton.YLFLSE.android": "Open in Browser", "countrySelection.dialog.positiveButton": "Change Country", "countrySelection.dialog.positiveButton.BON.ios": "Change country", "countrySelection.dialog.positiveButton.BPX.ios": "Change country", "countrySelection.dialog.positiveButton.HEI.ios": "Land ändern", "countrySelection.dialog.positiveButton.LAS.ios": "Land ändern", "countrySelection.dialog.positiveButton.SAN.ios": "Change country", "countrySelection.dialog.positiveButton.SHE.ios": "Land ändern", "countrySelection.dialog.positiveButton.WIT.ios": "Change country", "countrySelection.dialog.positiveButton.YLFLNL.ios": "Change country", "countrySelection.dialog.positiveButton.YLFLSE.ios": "Change country", "countrySelection.dialog.title": "Choose country", "countrySelection.dialog.title.HEI.android": "Select country", "countrySelection.dialog.title.HEI.ios": "Land auswählen", "countrySelection.dialog.title.MAN.android": "Select country", "countrySelection.dialog.title.MAN.ios": "Land auswählen", "countrySelection.dialog.title.SAN.ios": "Land auswählen", "countrySelection.dialog.title.WIT.ios": "Land auswählen", "countrySelection.hint": "Please select the country you are in. Note that if you change this setting, the language and assortment may change and your saved items will be lost.", "countrySelection.list.isSelected.accessibility": "Is selected", "countrySelection.listTitle.accessibility": "Regions list", "countrySelection.title": "Change country", "countrySelection.title.HEI.android": "Country selection", "countrySelection.title.HEI.ios": "L<PERSON>nderauswahl", "countrySelection.title.LAS.android": "Country selection", "countrySelection.title.MAN.android": "Your location", "countrySelection.title.MAN.ios": "Country selection", "countrySelection.title.SAN.android": "Country selection", "countrySelection.title.SHE.android": "Country selection", "countrySelection.title.SHE.ios": "L<PERSON>nderauswahl", "countrySelection.title.WIT.android": "Country selection", "countrySelection.title.YLFLNL.android": "Country selection", "countrySelection.title.YLFLSE.android": "Country selection", "countrychange.regionChanged.accessibility": "Region changed", "deals.dealVariant.carousel.accessibility": "Deals Carousel", "deals.dealVariant.carousel.amount.accessibility": "%{amount} deals", "deals.dealVariant.code.acceptButton": "Discover deals", "deals.dealVariant.code.acceptButton.HEI.android": "Activate & discover deals", "deals.dealVariant.code.acceptButton.LAS.android": "Activate & discover deals", "deals.dealVariant.code.acceptButton.SAN.android": "Activate & discover deals", "deals.dealVariant.code.acceptButton.WIT.android": "Activate &amp; discover deals", "deals.dealVariant.code.acceptButton.YLFLNL.android": "Activate & discover deals", "deals.dealVariant.code.acceptButton.YLFLSE.android": "Activate & discover deals", "deals.dealVariant.code.banner.title": "Use your coupon and save now!", "deals.dealVariant.code.copiedMessage.title": "CODE COPIED", "deals.dealVariant.code.copyButton": "COPY:", "deals.dealVariant.code.copyButton.accessibility": "Code %{code} - Copy code", "deals.dealVariant.image.accessibility": "Image displaying the deal", "deals.dealVariant.legal.sheet.accessibility": "Opens a sheet with more information.", "deals.dealVariant.product.acceptButton": "Shop Deal", "deals.dealVariant.product.accessibility": "Deal %{dealNumber} \n %{label} \n sale %{discount} \n %{altText} \n The product rating is %{stars} out of 5 stars, voted by %{voters} \n %{title} \n New price is %{newPrice}, old price is %{oldPrice}", "deals.dealVariant.product.pricePrefix": "now", "deals.dealVariant.product.priceRangePrefix": "from", "deals.dealVariant.promo.acceptButton": "Discover deals", "deals.dealVariant.timer": "%{hours} : %{minutes} : %{seconds}", "deals.dealVariant.timer.accessibility": "%{hours} hours : %{minutes} minutes : %{seconds} seconds", "deals.dealVariant.timer.days": "%{days} : %{hours} : %{minutes} : %{seconds}", "deals.dealVariant.timer.days.accessibility": "%{days} days : %{hours} hours : %{minutes} minutes : %{seconds} seconds", "deals.dealVariant.timer.title": "Valid for", "deals.dealVariant.voucher.acceptButton": "Activate &amp; discover deals", "deals.dealVariant.voucher.acceptButton.HEI": "Activate & discover deals", "deals.dealVariant.voucher.acceptButton.LAS.android": "Discover deals", "deals.dealVariant.voucher.acceptButton.SAN.android": "Discover deals", "deals.dealVariant.voucher.acceptButton.SAN.ios": "Activate & discover deals", "deals.dealVariant.voucher.acceptButton.WIT.android": "Discover deals", "deals.dealVariant.voucher.acceptButton.WIT.ios": "Activate & discover deals", "deals.dealVariant.voucher.acceptButton.YLFLNL.android": "Discover deals", "deals.dealVariant.voucher.acceptButton.YLFLNL.ios": "Activate & discover deals", "deals.dealVariant.voucher.acceptButton.YLFLSE.android": "Discover deals", "deals.dealVariant.voucher.acceptButton.YLFLSE.ios": "Activate & discover deals", "deals.gameScreen.navigationTitle": "Your deals", "deals.gameScreen.navigationTitle.BPX": "Your Deals", "deals.gameScreen.scratch.image.accessibility": "Image showing instructions to scratch image to reveal the deal", "deals.gameScreen.shake.image.accessibility": "Image showing instructions to shake phone to reveal the deal", "deals.gameScreen.tapADeal.image.accessibility": "Image showing instructions for tap a deal", "deals.revealDeal.scratch": "Reveal deal without scratching", "deals.revealDeal.shake": "Reveal deal without shaking", "error.contentDescription.close": "<PERSON><PERSON> to close the dialog", "error.contentDescription.tutorialImage": "Image showing an error icon", "error.deviceSupport.button": "Back to home", "error.deviceSupport.button.HEI.ios": "Zurück zum Shop", "error.deviceSupport.button.LAS.ios": "Zurück zum Shop", "error.deviceSupport.button.MAN.ios": "Back to Shop", "error.deviceSupport.button.SAN.ios": "Zurück zum Shop", "error.deviceSupport.button.SHE.ios": "Zurück zum Shop", "error.deviceSupport.button.WIT.ios": "Back to shop", "error.deviceSupport.button.YLFLNL.android": "Back to Home", "error.deviceSupport.button.YLFLNL.ios": "Back to shop", "error.deviceSupport.button.YLFLSE.android": "Back to Home", "error.deviceSupport.button.YLFLSE.ios": "Back to shop", "error.deviceSupport.text": "Unfortunately, this function of the app is not supported by your device.", "error.deviceSupport.text.HEI.ios": "In Kürze halten wir dich hier über spannende Updates und Aktionen auf dem Laufenden!", "error.deviceSupport.text.LAS.ios": "In Kürze halten wir dich hier über spannende Updates und Aktionen auf dem Laufenden!", "error.deviceSupport.text.MAN.ios": "Soon we will keep you up to date here on exciting updates and promotions!", "error.deviceSupport.text.SAN.ios": "In Kürze halten wir Sie hier über spannende Updates und Aktionen  auf dem Laufenden!", "error.deviceSupport.text.SHE.ios": "In Kürze halten wir dich hier über spannende Updates und Aktionen auf dem Laufenden!", "error.deviceSupport.text.WIT.ios": "New updates and offers will be available soon!", "error.deviceSupport.text.YLFLNL.ios": "New updates and offers will be available soon!", "error.deviceSupport.text.YLFLSE.ios": "New updates and offers will be available soon!", "error.deviceSupport.title": "Oops, something went wrong", "error.generic.accessibility": "Error screen", "error.generic.description": "Please try again.", "error.generic.description.BON.ios": "Something went wrong.\nPlease try again", "error.generic.description.BPX.ios": "Something went wrong.\nPlease try again", "error.generic.description.HEI.ios": "Da ist etwas schief gelaufen.\nVersuche es nochmal.", "error.generic.description.LAS.ios": "Da ist etwas schief gelaufen.\nVersuche es nochmal.", "error.generic.description.MAN.ios": "Something went wrong.\nPlease try again.", "error.generic.description.SAN.ios": "Da ist etwas schief gelaufen.\nVersuchen Sie es nochmal.", "error.generic.description.SHE.ios": "Da ist etwas schief gelaufen.\nVersuche es nochmal.", "error.generic.description.WIT.ios": "An error occurred.\nPlease try again.", "error.generic.description.YLFLNL.ios": "An error occurred.\nPlease try again.", "error.generic.description.YLFLSE.ios": "An error occurred.\nPlease try again.", "error.generic.reload.accessibility": "Screen correctly reloaded", "error.generic.retry": "Try again", "error.generic.retry.HEI.ios": "Noch<PERSON> versuchen", "error.generic.retry.LAS.ios": "Noch<PERSON> versuchen", "error.generic.retry.SAN.ios": "Noch<PERSON> versuchen", "error.generic.retry.SHE.ios": "Noch<PERSON> versuchen", "error.generic.title": "Oops, something went wrong", "error.generic.title.BON.ios": "Ooops!", "error.generic.title.BPX.ios": "Ooops!", "error.generic.title.HEI.ios": "Huch!", "error.generic.title.LAS.ios": "Huch!", "error.generic.title.MAN.ios": "Yikes!", "error.generic.title.SAN.ios": "Huch!", "error.generic.title.SHE.ios": "Huch!", "error.generic.title.WIT.ios": "Oh!", "error.generic.title.YLFLNL.ios": "Oh!", "error.generic.title.YLFLSE.ios": "Oh!", "error.instructions.connection": "Please check your internet connection and retry again", "error.instructions.update": "We added lots of new features and fix some bugs to make your experience as smooth as possible", "error.noInternet.button": "Try again", "error.noInternet.button.HEI.ios": "<PERSON><PERSON><PERSON> versuchen", "error.noInternet.button.LAS.ios": "<PERSON><PERSON><PERSON> versuchen", "error.noInternet.button.SAN.ios": "<PERSON><PERSON><PERSON> versuchen", "error.noInternet.button.SHE.ios": "<PERSON><PERSON><PERSON> versuchen", "error.noInternet.description": "Please check your internet connection and retry again", "error.noInternet.description.BON.ios": "Please check your internet connection \n and try again", "error.noInternet.description.BPX.ios": "Please check your internet connection \n and try again", "error.noInternet.description.HEI.ios": "Bitte überprüfen Sie Ihre Internetverbindung und versuchen Sie es erneut.", "error.noInternet.description.LAS.ios": "Bitte überprüfen Sie Ihre Internetverbindung und versuchen Sie es erneut.", "error.noInternet.description.MAN.ios": "Please check your internet connection and try again.", "error.noInternet.description.SAN.ios": "Bitte überprüfen Sie Ihre Internet- verbindung und versuchen es erneut.", "error.noInternet.description.SHE.ios": "Bitte überprüfen Sie Ihre Internetverbindung und versuchen Sie es erneut.", "error.noInternet.description.WIT.ios": "Please check your internet connection and try again.", "error.noInternet.description.YLFLNL.ios": "Please check your internet connection and try again.", "error.noInternet.description.YLFLSE.ios": "Please check your internet connection and try again.", "error.noInternet.title": "Can't load page", "error.noInternet.title.BON.ios": "No internet connection", "error.noInternet.title.BPX.ios": "No internet connection", "error.noInternet.title.HEI.ios": "Die Seite konnte nicht geladen werden!", "error.noInternet.title.LAS.ios": "Die Seite konnte nicht geladen werden!", "error.noInternet.title.MAN.ios": "The page could not be loaded!", "error.noInternet.title.SAN.ios": "Die Seite konnte nicht geladen werden!", "error.noInternet.title.SHE.ios": "Die Seite konnte nicht geladen werden!", "error.noInternet.title.WIT.ios": "The page could not be loaded!", "error.noInternet.title.YLFLNL.ios": "The page could not be loaded!", "error.noInternet.title.YLFLSE.ios": "The page could not be loaded!", "error.title": "An error has occured", "error.title.BON.android": "An error has occurred", "error.title.BON.ios": "Please check your internet connection", "error.title.BPX.android": "An error has occurred", "error.title.BPX.ios": "Please check your internet connection", "error.title.HEI.android": "An error has occurred", "error.title.HEI.ios": "Es ist ein Fehler aufgetreten", "error.title.LAS.android": "An error has occurred", "error.title.LAS.ios": "An error occured", "error.title.MAN": "An error has occurred", "error.title.SHE.android": "An error has occurred", "error.title.SHE.ios": "Es ist ein Fehler aufgetreten", "error.title.connection": "Can't load page", "error.title.update": "App update required!", "error.update.button": "Update the app", "error.update.button.BON.android": "Open Google Play", "error.update.button.BON.ios": "Back to shop", "error.update.button.BPX.android": "Open Google Play", "error.update.button.BPX.ios": "Back to shop", "error.update.button.HEI.android": "Open Google Play", "error.update.button.HEI.ios": "Zum App Store", "error.update.button.LAS.android": "Open Google Play", "error.update.button.LAS.ios": "Zum App Store", "error.update.button.MAN.android": "Open Google Play", "error.update.button.MAN.ios": "Visit app store", "error.update.button.SAN.android": "Open Google Play", "error.update.button.SAN.ios": "To the app store", "error.update.button.SHE.android": "Open Google Play", "error.update.button.SHE.ios": "Zum App Store", "error.update.button.WIT.android": "Open Google Play", "error.update.button.WIT.ios": "To the app store", "error.update.button.YLFLNL.android": "Open Google Play", "error.update.button.YLFLNL.ios": "To the app store", "error.update.button.YLFLSE.android": "Open Google Play", "error.update.button.YLFLSE.ios": "To the app store", "error.update.description": "Download the latest app version and experience new features and products.", "error.update.description.BON.android": "Find the newest version on Google Play and experience new features and products.", "error.update.description.BON.ios": "We will inform you about exciting updates and promotions soon!", "error.update.description.BPX.android": "Find the newest version on Google Play and experience new features and products.", "error.update.description.BPX.ios": "We will inform you about exciting updates and promotions soon!", "error.update.description.HEI.android": "Find the newest version on Google Play and experience new features and products.", "error.update.description.HEI.ios": "Laden Sie im App Store die neuste Version herunter und profitiere Sie von neuen Features und Produkten.", "error.update.description.LAS.android": "Find the newest version on Google Play and experience new features and products.", "error.update.description.LAS.ios": "Laden Sie im App Store die neuste Version herunter und profitiere Sie von neuen Features und Produkten.", "error.update.description.MAN.android": "Download the latest version from Google Play and discover new features and products.", "error.update.description.MAN.ios": "Download the latest version from the App Store and discover new features and products.", "error.update.description.SAN.android": "Find the newest version on Google Play and experience new features and products.", "error.update.description.SAN.ios": "You can download the latest update in the App Store and continue enjoying our shopping experience.", "error.update.description.SHE.android": "Find the newest version on Google Play and experience new features and products.", "error.update.description.SHE.ios": "Laden Sie im App Store die neuste Version herunter und profitiere Sie von neuen Features und Produkten.", "error.update.description.WIT.android": "Find the newest version on Google Play and experience new features and products.", "error.update.description.WIT.ios": "You can download the latest update in the App Store and continue enjoying our shopping experience.", "error.update.description.YLFLNL.android": "Find the newest version on Google Play and experience new features and products.", "error.update.description.YLFLNL.ios": "You can download the latest update in the App Store and continue enjoying our shopping experience.", "error.update.description.YLFLSE.android": "Find the newest version on Google Play and experience new features and products.", "error.update.description.YLFLSE.ios": "You can download the latest update in the App Store and continue enjoying our shopping experience.", "error.update.title": "App update required!", "error.update.title.HEI.ios": "App-Update erford<PERSON>lich!", "error.update.title.LAS.ios": "App-Update erford<PERSON>lich!", "error.update.title.SAN.ios": "App update needed!", "error.update.title.SHE.ios": "App-Update erford<PERSON>lich!", "error.update.title.WIT.ios": "App update needed!", "error.update.title.YLFLNL.ios": "App update needed!", "error.update.title.YLFLSE.ios": "App update needed!", "general.back": "Back", "general.banner.code.accessibility": "%{promoText} - Code %{promoCode} Copy code", "general.banner.code.copy": "COPY:", "general.banner.code.didCopy": "COPIED CODE", "general.cancel": "Cancel", "general.close": "Close", "general.collapsed.accessibility": "Closed", "general.doubleTabToEdit.accessibility": "Double tap to edit", "general.expanded.accessibility": "Maximized", "general.list.items.accessibility": "%{amount} items", "general.motionSetting.sheet.copy": "You can permanently disable the shake function if you have problems.", "general.motionSetting.sheet.title": "Issues with motion activation?", "general.motionSetting.toggle.title": "Use mobile shake funcationality", "general.notification": "Notification", "general.overlay": "Overlay", "general.segmentedControl.buttonLabel.accessibility": "button", "general.segmentedControl.selectionPosition.accessibility": "Selection %{itemPosition} of %{totalItems}", "general.segmentedControl.selectionPrefix.accessibility": "Selected %{element}", "general.video.accessibility": "Video content", "general.webview.accessibility.ios": "web view", "general.yesterday": "Yesterday", "general.yesterday.SAN.android": "Gestern", "inbox.button.accessibility": "News", "inbox.button.unreadCount.accessibility": "%{title} - %{value} new notifications", "inbox.dialog.cancel": "Cancel", "inbox.dialog.delete": "Delete", "inbox.dialog.delete.HEI.ios": "Löschen", "inbox.dialog.delete.LAS.ios": "Löschen", "inbox.dialog.delete.SAN.ios": "Löschen", "inbox.dialog.delete.SHE.ios": "Löschen", "inbox.empty.title": "No news yet", "inbox.message.deleteDialog.text": "Do you want to delete this message?", "inbox.message.deleted.accessibility": "Message deleted", "inbox.message.error.title": "Could not load message", "inbox.noMessages.pushNotActive.button": "Enable Notifications", "inbox.noMessages.pushNotActive.footnote": "You can revoke your consent at any time.", "inbox.noMessages.pushNotActive.privacyPolicy": "privacy policy", "inbox.noMessages.pushNotActive.text": "Enable Notifications to never miss any offers", "inbox.noMessages.text": "You will find exciting updates and offers here soon", "inbox.overview.message.read.contentDescription": "Read message", "inbox.overview.message.unread.contentDescription": "Unread message", "inbox.overview.title": "Messages", "inbox.title": "NEWS", "inbox.title.HEI": "News", "inbox.title.LAS": "News", "inbox.title.MAN": "News", "inbox.title.SAN": "News", "inbox.title.SHE": "News", "inbox.title.WIT": "News", "inbox.title.YLFLNL": "News", "inbox.title.YLFLSE": "News", "login.successful.accessibility": "Login successful", "logout.successful.accessibility": "Logout successful", "motionSetting.toggle.copy": "To deactivate turn off", "navigation.assortment.entry.bfg.title": "BH consultant", "navigation.assortment.section.moreServices.title": "More services", "navigation.assortment.title": "Assortment", "navigation.assortment.title.HEI.ios": "Assortment ", "navigation.assortment.title.SAN.ios": "Sortiment", "navigation.assortment.title.YLFLNL.ios": "Sortiment", "navigation.assortment.title.YLFLSE.ios": "Sortiment", "navigation.cart.title": "<PERSON><PERSON>", "navigation.forMe.title": "Sheego FOR ME", "navigation.forMe.title.SHE": "Sheego FOR ME", "navigation.profile.title": "Profile", "navigation.recommendation.title": "Recommend app", "navigation.shop.title": "Shop", "navigation.toastNoSupportedAppForLinkFound.BON.android": "No app that supports this link found", "navigation.toastNoSupportedAppForLinkFound.BPX.android": "No app that supports this link found", "navigation.toastNoSupportedAppForLinkFound.CRE.android": "No app that supports this link found", "navigation.toastNoSupportedAppForLinkFound.HEI.android": "No app that supports this link found", "navigation.toastNoSupportedAppForLinkFound.LAS.android": "No app that supports this link found", "navigation.toastNoSupportedAppForLinkFound.MAN.android": "No app that supports this link found", "navigation.toastNoSupportedAppForLinkFound.SAN.android": "No app that supports this link found", "navigation.toastNoSupportedAppForLinkFound.SHE.android": "No app that supports this link found", "navigation.toastNoSupportedAppForLinkFound.WIT.android": "No app that supports this link found", "navigation.toastNoSupportedAppForLinkFound.YLFLNL.android": "No app that supports this link found", "navigation.toastNoSupportedAppForLinkFound.YLFLSE.android": "No app that supports this link found", "navigation.toastNoSupportedAppForLinkFound.android": "No app that supports this link found", "navigation.wishlist.title": "Wishlist", "onboarding.welcome.bulletPointListTitle.accessibility": "List with advantages", "onboarding.welcome.buttonCountryChoose.accessibility": "Choose your region", "onboarding.welcome.countryChooser.button.accessibility": "choose region", "onboarding.welcome.countryChooser.button.accessibility.HEI.ios": "Tenant <PERSON> selected", "onboarding.welcome.countryChooser.button.accessibility.SAN.ios": "Tenant <PERSON> selected", "onboarding.welcome.countryChooser.button.accessibility.SHE.ios": "Tenant <PERSON> selected", "onboarding.welcome.countryChooser.button.accessibility.WIT.ios": "Tenant <PERSON> selected", "onboarding.welcome.countryChooser.button.accessibility.YLFLNL.ios": "Tenant <PERSON> selected", "onboarding.welcome.countryChooser.button.accessibility.YLFLSE.ios": "Tenant <PERSON> selected", "onboarding.welcome.login.button": "Sign in", "onboarding.welcome.login.button.HEI.ios": "Already registered?\nSign in now!", "onboarding.welcome.login.button.LAS.ios": "Bereits registriert? Jetzt anmelden", "onboarding.welcome.login.button.MAN.ios": "Sign up now", "onboarding.welcome.login.button.SAN.android": "<PERSON><PERSON>", "onboarding.welcome.login.button.SAN.ios": "Already registered?\nSign in now", "onboarding.welcome.login.button.SHE.ios": "Bereits registriert? Jetzt anmelden", "onboarding.welcome.login.button.WIT.android": "<PERSON><PERSON>", "onboarding.welcome.login.button.WIT.ios": "Already registered?\nSign in now!", "onboarding.welcome.login.button.YLFLNL.android": "<PERSON><PERSON>", "onboarding.welcome.login.button.YLFLNL.ios": "Already registered?\nSign in now!", "onboarding.welcome.login.button.YLFLSE.android": "<PERSON><PERSON>", "onboarding.welcome.login.button.YLFLSE.ios": "Already registered?\nSign in now!", "onboarding.welcome.register.button": "Register", "onboarding.welcome.register.button.HEI.ios": "Registrieren", "onboarding.welcome.register.button.LAS.ios": "Registrieren", "onboarding.welcome.register.button.SHE.ios": "Registrieren", "onboarding.welcome.register.button.YLFLNL.ios": "<PERSON><PERSON>", "onboarding.welcome.register.button.YLFLSE.ios": "<PERSON><PERSON>", "onboarding.welcome.shop.button": "Directly to the shop", "onboarding.welcome.shop.button.BON.android": "To shop", "onboarding.welcome.shop.button.BPX.android": "To shop", "onboarding.welcome.shop.button.HEI.ios": "Zum Shop", "onboarding.welcome.shop.button.LAS.ios": "Zum Shop", "onboarding.welcome.shop.button.MAN.ios": "To the shop", "onboarding.welcome.shop.button.SAN.ios": "To shop", "onboarding.welcome.shop.button.SHE.ios": "Zum Shop", "onboarding.welcome.shop.button.WIT.ios": "To shop", "onboarding.welcome.shop.button.YLFLNL.ios": "To shop", "onboarding.welcome.shop.button.YLFLSE.ios": "To shop", "onboarding.welcome.title": "Welcome to %{name}", "onboarding.welcome.title.HEI.android": "Welcome to the Otto Application", "onboarding.welcome.title.HEI.ios": "Your personal Heine App", "onboarding.welcome.title.LAS.android": "Welcome to the Otto App", "onboarding.welcome.title.LAS.ios": "Alles von %{name} in einer App", "onboarding.welcome.title.MAN.android": "Welcome to the Manufactum App", "onboarding.welcome.title.MAN.ios": "Your personal\nManufactum App", "onboarding.welcome.title.SAN.android": "Welcome to the Otto Application", "onboarding.welcome.title.SAN.ios": "The colorful fashion world\nfrom Sieh an!", "onboarding.welcome.title.SHE.android": "Welcome to the Otto Application", "onboarding.welcome.title.SHE.ios": "Alles von %{name} in einer App", "onboarding.welcome.title.WIT.android": "Welcome to the\nWitt Application", "onboarding.welcome.title.WIT.ios": "Ihre persönliche\nWitt A<PERSON>", "onboarding.welcome.title.YLFLNL.android": "Welcome to the Otto Application", "onboarding.welcome.title.YLFLNL.ios": "Your personal YLFL app", "onboarding.welcome.title.YLFLSE.android": "Welcome to the Otto Application", "onboarding.welcome.title.YLFLSE.ios": "Your personal YLFL app", "onboarding.welcome.title.accessibility": "Welcome screen", "onboarding.welcome.uspList": "- Your favorite styles\n- Secure payment\n- Fast shipping\n- And much more!", "onboarding.welcome.uspList.HEI.android": "- Secure payment\n- Fast shipping\n- And much more!\n", "onboarding.welcome.uspList.HEI.ios": "- Take your wishlist with you at any time\n- See your orders with just one tap\n- Stay informed about exclusive discounts and other benefits that will force a line\n  break here", "onboarding.welcome.uspList.LAS.android": "- Your favorite styles\n- Secure payment\n- Fast shipping\n- And much more!\n", "onboarding.welcome.uspList.LAS.ios": "- Your favorite styles\n- Secure payment\n- Fast shipping\n- And much more!", "onboarding.welcome.uspList.MAN": "- See the entire range at a click\n- Stay conveniently logged in\n- Stay informed about discounts and events", "onboarding.welcome.uspList.MAN.android": "- Create and save your wishlist\n- Always be the first to know about news and special offers\n- And much more!\n", "onboarding.welcome.uspList.MAN.ios": "- Take your wishlist with you at any time\n- See your orders with just one tap\n- Stay informed about exclusive discounts and other benefits that will force a line\n  break here", "onboarding.welcome.uspList.SAN": "- All sizes 1 price \n- purchase on account \n- 100 days free return", "onboarding.welcome.uspList.SAN.android": "- Secure payment\n- Fast shipping\n- And much more!\n", "onboarding.welcome.uspList.SAN.ios": "- Take your wishlist with you at any time\n- See your orders with just one tap\n- Stay informed about exclusive discounts and other benefits that will force a line\n  break here", "onboarding.welcome.uspList.SHE.android": "- Secure payment\n- Fast shipping\n- And much more!\n", "onboarding.welcome.uspList.SHE.ios": "- Take your wishlist with you at any time\n- See your orders with just one tap\n- Stay informed about exclusive discounts and other benefits that will force a line\n  break here", "onboarding.welcome.uspList.WIT.android": "- Secure payment\n- Fast shipping\n- And much more!\n", "onboarding.welcome.uspList.WIT.ios": "- Take your wishlist with you at any time\n- See your orders with just one tap\n- Stay informed about exclusive discounts and other benefits that will force a line\n  break here", "onboarding.welcome.uspList.YLFLNL.android": "- Secure payment\n- Fast shipping\n- And much more!\n", "onboarding.welcome.uspList.YLFLNL.ios": "- Take your wishlist with you at any time\n- See your orders with just one tap\n- Stay informed about exclusive discounts and other benefits that will force a line\n  break here", "onboarding.welcome.uspList.YLFLSE.android": "- Secure payment\n- Fast shipping\n- And much more!\n", "onboarding.welcome.uspList.YLFLSE.ios": "- Take your wishlist with you at any time\n- See your orders with just one tap\n- Stay informed about exclusive discounts and other benefits that will force a line\n  break here", "onboarding.welcome.uspList.accessibility": "list with advantages", "productDetail.availability.days": "Working days", "productDetail.availability.deliveryTimeRange": "Available at your location in %{minTime}-%{maxTime} %{timeUnit}", "productDetail.availability.inStock": "Available", "productDetail.availability.lowStock": "Almost sold out", "productDetail.availability.months": "Months", "productDetail.availability.permanentlyOutOfStock": "Out of stock", "productDetail.availability.preOrderable": "Pre -orderable", "productDetail.availability.quantity": "Extremely popular: only %{count}x available", "productDetail.availability.quantity.LAS": "Very popular: only %{count}x available", "productDetail.availability.quantity.short": "Only available %{count}x", "productDetail.availability.quantity.short.LAS": "Only %{count}x available", "productDetail.availability.temporarilyOutOfStock": "Soon back in stock", "productDetail.availability.unknown": "Unknown", "productDetail.availability.weeks": "Weeks", "productDetail.basket.button.title.accessibility": "Put in the shopping cart", "productDetail.basketSuccessView.continueShopping": "Continue shopping", "productDetail.basketSuccessView.goToBasket": "To the basket of gooods", "productDetail.basketSuccessView.title": "Successfully added", "productDetail.button.basket.title": "Add to Cart", "productDetail.button.basket.withPrice.title": "In the shopping cart %{price}", "productDetail.close.button.accessibility": "Close", "productDetail.color.title": "Color:", "productDetail.colors.accessibility": "%{name} - color %{numberOfCurrentColor} from %{numberOfColors} - %{availabilityInfo}", "productDetail.colors.changed.accessibility": "Color changed", "productDetail.colors.title.accessibility": "Color selection", "productDetail.dimension.unavailable.accessibility": "Unavailable", "productDetail.dynamicYieldBanner.info.button.accessibility": "Open a popup with more information", "productDetail.dynamicYieldBanner.sheet.discount.title": "Discount conditions", "productDetail.error.addToBasket": "Excuse me! Article could not be added.", "productDetail.error.addToWishlist": "Excuse me! Article could not be added.", "productDetail.error.giftCardAlreadyInBasket": "A gift voucher is already in your shopping cart.", "productDetail.error.giftCardNameTooLong": "The end of the gift voucher is too long.", "productDetail.error.giftCardProductAlreadyInBasket": "The purchase of a gift voucher cannot be combined with an item order. Please complete the current purchase process first", "productDetail.error.giftCardSameValueAlreadyInBasket": "This gift voucher is already in your shopping cart. The same voucher can only be added once per shopping cart.", "productDetail.error.itemCountExceeded": "This article is already in your shopping cart. Unfortunately, the shopping cart per item cannot be added to a higher amount than 3.", "productDetail.error.productUnavailable": "Excuse me! The article is not available.", "productDetail.error.removeFromWishlist": "Excuse me! Article could not be removed.", "productDetail.error.slowLoadingBannerText": "The connection is slow. Please wait a moment.", "productDetail.gallery.count.accessibility": "Image %{numberOfCurrentImage} from %{numberOfImages}", "productDetail.gallery.flag.exclusiveOnline": "Exclusive online", "productDetail.gallery.flag.mixMatch": "Mix & match", "productDetail.gallery.flag.new": "New", "productDetail.gallery.flag.pack": "pack", "productDetail.gallery.flag.priceHighlight": "Price tip", "productDetail.gallery.flag.sale": "Sale", "productDetail.gallery.flag.salesUnit": "%{quantity} piece", "productDetail.gallery.flag.set": "set", "productDetail.information.articleStandards.categories.animalWelfare.detail": "Articles with this symbol go beyond our minimum requirements regarding animal materials. They are characterized by the fact that when extracting the animal components (e.g. feathers, down or fibers), paying particular attention to a species -appropriate attitude and treatment of the animals. Articles with this symbol are certified according to an independent standard.", "productDetail.information.articleStandards.categories.animalWelfare.title": "Consider animal welfare", "productDetail.information.articleStandards.categories.improvedProduction.detail": "Articles with this symbol are characterized by a demonstrably improved manufacturing process. B. a resource -saving coloring process, and / or the reduced use of chemicals. An article falls into this category as soon as one of the following brand fibers or standards is available.", "productDetail.information.articleStandards.categories.improvedProduction.title": "Improved production", "productDetail.information.articleStandards.categories.organicMaterials.detail": "Articles with this symbol contain natural fibers such as cotton, hemp or linen that have been biodegraded. In contrast to conventional cultivation, the use of chemical-synthetic crop protection and fertilizers is dispensed with, which protects natural soil fertility and biodiversity. Articles with this symbol are certified according to an independent standard.", "productDetail.information.articleStandards.categories.organicMaterials.title": "Biodegrading materials", "productDetail.information.articleStandards.categories.recycledMaterials.detail": "Articles with this symbol contain materials that either drop out as remains during production or have already been used and then added to the recycling cycle. This allows valuable resources such as water and energy. Articles with this symbol contain recognized brand fibers or are certified according to an independent standard.", "productDetail.information.articleStandards.categories.recycledMaterials.title": "Recycled materials", "productDetail.information.articleStandards.categories.responsiblySourcedMaterials.detail": "Articles with this symbol are made of plant -based raw materials that are obtained from responsible forestry (e.g. viscose) or agricultural residues. In doing so, paid attention to the preservation of soil fertility and biodiversity. Articles with this symbol contain recognized brand fibers or are certified according to an independent standard.", "productDetail.information.articleStandards.categories.responsiblySourcedMaterials.title": "Improved procurement of raw materials", "productDetail.information.articleStandards.categories.supportingSocialInitiatives.detail": "Articles with this symbol are proven to be characterized by the fact that the purchase supports a social initiative. For example, this promotes better growing conditions of the raw materials or supports (further) educational offers for the people on site.", "productDetail.information.articleStandards.categories.supportingSocialInitiatives.title": "Promotion of social initiatives", "productDetail.information.articleStandards.seals.bioRe.detail": "Our products with the \"Bior® Sustainable Textile\" seal meet high sustainable demands. They promote biological agriculture, ecological production and can be traced back in the clothing via a code.", "productDetail.information.articleStandards.seals.bioRe.title": "Bior Sustainable Textiles", "productDetail.information.articleStandards.seals.blauerEngel.detail": "Articles with this seal meet high demands on environmental compatibility, usability and health. The seal is awarded by the Federal Ministry for the Environment, Nature Conservation and Nuclear Safety in cooperation with the independent \"jury of environmental signs\". Actively contribute to environmental protection.", "productDetail.information.articleStandards.seals.blauerEngel.title": "The blue angel", "productDetail.information.articleStandards.seals.bluesignProduct.detail": "This seal is only assigned to textiles, in the production of which the environment is minimally stressed and the resources are spared at a maximum. The article fulfills strict criteria for environmental and health protection as well as security. This seal sets an example for the protection of people and nature.", "productDetail.information.articleStandards.seals.bluesignProduct.title": "Bluesign® product", "productDetail.information.articleStandards.seals.cottonMadeInAfrica.detail": "With the purchase of this article you support the Cotton Made initiative in Africa and are committed to sustainable and fair cotton cultivation in Subsahara Africa. This initiative has already been actively supported by over 1 million farmers. By buying articles with the Cotton Made in Africa Siegel, you automatically make a contribution to this important concern.", "productDetail.information.articleStandards.seals.cottonMadeInAfrica.title": "Supports Cotton Made in Africa", "productDetail.information.articleStandards.seals.downpass.detail": "Down and feathers for articles with this seal can be traced back and come from monitored supply chains. Stuffmast and life gluts are prohibited. Independent test institutes control both compliance with the strict animal welfare and high quality requirements", "productDetail.information.articleStandards.seals.downpass.title": "Downpass", "productDetail.information.articleStandards.seals.econyl.detail": "Discover a sustainable alternative to conventional nylon with Econyl®. This textile yarn is not made from oil, but from remnants of production and fabric as well as from the sea removed fishing nets. As a result, the fibers are 100% recycled and help to further reduce part of our waste volume.", "productDetail.information.articleStandards.seals.econyl.title": "Econyl®", "productDetail.information.articleStandards.seals.ecovero.detail": "The viscose used in this article is made exclusively from certified wood from Europe. With this sustainable production, 50% of emissions and water consumption can be saved in the production of the viscose. The fibers meet the high environmental standards of the EU Eco label and are produced in an environmentally friendly manner.", "productDetail.information.articleStandards.seals.ecovero.title": "Lenzing ™ Ecovero ™", "productDetail.information.articleStandards.seals.euecolabel.detail": "Articles with the \"EU Ecolabel\" seal actively contribute to the protection of the environment. These products cause less waste, pollution and CO2 emissions because they comply with strict requirements to avoid dangerous chemicals and deal efficiently with energy, water and raw materials. They are also characterized by their durability, repairability and recyclability. Choose for articles with the \"EU Ecolabel\" seal and contribute to the promotion of sustainable and environmentally friendly products.", "productDetail.information.articleStandards.seals.euecolabel.title": "EU Ecolabel", "productDetail.information.articleStandards.seals.fairtradeCotton.detail": "With this seal we guarantee fairly traded cotton of certified cooperatives in Asia and Africa. The small farmers receive fair minimum prices for their harvest and an additional bonus to support joint projects. In addition, the strictest social and ecological standards are observed in cultivation.", "productDetail.information.articleStandards.seals.fairtradeCotton.title": "Fairtrade Cotton", "productDetail.information.articleStandards.seals.goodCashmere.detail": "The Good Cashmere Standard® BY ABTF was launched by the AID by Traide Foundation to protect cashmere goals, to improve the life of farmers and workers and to preserve the environment in which they live. This standard is committed to sustainable and responsible production.", "productDetail.information.articleStandards.seals.goodCashmere.title": "Good Cashmere Standard®", "productDetail.information.articleStandards.seals.gotsMadeWithOrganic.detail": "The purchase of clothing with the 95% bio seal promotes sustainable and environmentally conscious consumption, since this seal guarantees that at least 95% of the natural fibers used are biologically grown and the entire production process is subject to strict ecological and social criteria.", "productDetail.information.articleStandards.seals.gotsMadeWithOrganic.title": "Global Organic Textile Standard Made with Organic Materials", "productDetail.information.articleStandards.seals.gotsOrganic.detail": "Discover our clothes with the 70%organic seal. At least 70% of the cotton used comes from organic cultivation, while strict ecological standards and fair production conditions are guaranteed. Consciously choose sustainable fashion and set an example for the environment and social responsibility.", "productDetail.information.articleStandards.seals.gotsOrganic.title": "Global Organic Textile Standard Organic", "productDetail.information.articleStandards.seals.grs.detail": "In our articles with this seal you will find a share of recycled material that consists of \"pre-consumer\" (remains from production) and \"post-consumer\" (already worn textiles) material. In addition, strict ecological and social criteria are observed during the entire production process.", "productDetail.information.articleStandards.seals.grs.title": "Global Recycled Standard", "productDetail.information.articleStandards.seals.gruenerKnopf.detail": "Simply recognize sustainable fashion by the \"green button\"! This state seal stands for textiles in which strict requirements are placed on the entire production process to protect people and the environment. A total of 46 demanding social and environmental criteria are observed, from wastewater limit values ​​to the ban on forced labor. Compliance with these criteria is checked by independent test centers. The special thing about the \"green button\" is that not only the article itself, but also the entire company is checked.", "productDetail.information.articleStandards.seals.gruenerKnopf.title": "Green button", "productDetail.information.articleStandards.seals.ivn.detail": "Our articles with this seal consist 100% of biologically grown natural fibers. Extensive social and ecological criteria are observed, such as the ban on pesticides and child and forced labor.", "productDetail.information.articleStandards.seals.ivn.title": "Natural textile IVN certified best", "productDetail.information.articleStandards.seals.leatherWorkingGroup.detail": "With the Leather Working Group, we are committed to improved environmental practices in the leather industry. Tanning that bears this seal attach great importance to the responsible handling of water and energy, security at the workplace and the strict regulation of the use of chemicals. Sustainable practices in the leather industry are supported with leather products with this seal.", "productDetail.information.articleStandards.seals.leatherWorkingGroup.title": "Leather Working Group", "productDetail.information.articleStandards.seals.lyocell.detail": "The Tencel ™ Lyocell fibers in this article are made from wood from certified renewable forests and are therefore biodegradable. Not only do they offer a natural feeling of fit, but also ensure optimal moisture transport to ensure a pleasant skin climate.", "productDetail.information.articleStandards.seals.lyocell.title": "Tencel ™ Lyocell", "productDetail.information.articleStandards.seals.madeInGreen.detail": "With this seal we guarantee pollutant -tested materials, environmentally friendly companies and secure, socially acceptable working conditions. The special thing: Each article is provided with a clear product ID with which you can trace the entire production down to detail. Consciously choose products with this seal and experience transparency and responsibility.", "productDetail.information.articleStandards.seals.madeInGreen.title": "Made in Green by Oeko-Tex®", "productDetail.information.articleStandards.seals.modal.detail": "The Tencel ™ Modal fibers in this article are made from wood from certified renewable forests. The production process is energy -saving and environmentally friendly, which is certified by the EU Ecolabel. Due to the high quality, the article retains its wonderfully soft nature even after wearing frequently.", "productDetail.information.articleStandards.seals.modal.title": "Tencel ™ Modal", "productDetail.information.articleStandards.seals.nordicSwan.detail": "Articles with the Nordic seal support environmentally friendly production in which energy, water and CO2 are saved. The materials used are checked for your health compatibility and quality. In addition, regular checks are carried out to ensure that certified companies meet the high standards. Articles with the Nordic seal actively contribute to the protection of nature.", "productDetail.information.articleStandards.seals.nordicSwan.title": "Nordic Swan Ecolabel", "productDetail.information.articleStandards.seals.ocs100.detail": "Our articles with the seal contain at least 95% biologically grown natural fibers such as cotton and linen. Through independent controls along the value chain, we ensure that no conventional cotton is included in the end product.", "productDetail.information.articleStandards.seals.ocs100.title": "Organic Content Standard (OCS) 100", "productDetail.information.articleStandards.seals.ocsBlended.detail": "Discover our articles with the seal, which contain at least 70% biologically grown natural fibers. We attach great importance to environmentally friendly criteria and take into account ecological and social aspects during production.", "productDetail.information.articleStandards.seals.ocsBlended.title": "Organic Content Standard (OCS) Blended", "productDetail.information.articleStandards.seals.oekoTex.detail": "Our articles with this seal contain cotton from organic cultivation, which is certified according to the Oeko-Tex® Organic Cotton Standard. This globally recognized standard offers transparency and traceability of textile products made from organic cotton or organic cotton mixtures.", "productDetail.information.articleStandards.seals.oekoTex.title": "Oeko-Tex® Organic Cotton", "productDetail.information.articleStandards.seals.organicCotton.detail": "The conventional cultivation of cotton contains both the environment and the health of people due to high water consumption and the use of toxic plant protection products. Our articles, on the other hand, consist of certified organic cotton (e.g. GOTS), when growing genetically manipulated seeds and harmful pesticides are prohibited. In addition, up to 90% water can be saved compared to conventional cultivation.", "productDetail.information.articleStandards.seals.organicCotton.title": "Organic cotton", "productDetail.information.articleStandards.seals.rcs100.detail": "Products with this seal consist of at least 95% recycled materials, which include both \"pre-consumers\" (remnants from production) and \"post-consumers\" (already worn textiles). We are characterizing articles that make an important contribution to reducing waste and resource consumption.", "productDetail.information.articleStandards.seals.rcs100.title": "Recycled Claim Standard (RCS) 100", "productDetail.information.articleStandards.seals.rcsBlended.detail": "This product contains at least 30% recycled material (for cotton 20%). Independent test institutions ensure that the proportion of recycling material is checked along the entire supply chain. Please note that there are no specific ecological or social standards for the production of the articles.", "productDetail.information.articleStandards.seals.rcsBlended.title": "Recycled Claim Standard (RCS) Blended", "productDetail.information.articleStandards.seals.recycledMaterial.detail": "This beautiful fashion piece consists of at least 30% recycling material (cotton 20%) and is certified with a recognized seal or a brand fiber such as Reborn ™ or Recover®. Raw materials such as textiles or plastic waste are no longer used, prepared and processed into new recycled fibers or plastics. This procedure saves valuable resources such as water and energy.", "productDetail.information.articleStandards.seals.recycledMaterial.title": "Recycled materials", "productDetail.information.articleStandards.seals.refibra.detail": "Experience the circulatory idea: This article was produced with Tencel ™ X Refibra ™ fibers, which are obtained from cotton residues. In an innovative process, the remains are processed together with cellulose into new Tencel ™ Lyocell fibers. This circulatory process saves raw materials and energy and reduced garbage production.", "productDetail.information.articleStandards.seals.refibra.title": "Tencel ™ with Refibra ™ technology", "productDetail.information.articleStandards.seals.repreve.detail": "With the Repreve® fiber, an environmentally friendly alternative to conventional polyester is made from recycled polyester, obtained from plastic bottles. This fiber offers the same stable properties as conventional polyester, while water, energy and resources are saved at the same time. By buying this article, you make an active contribution to sustainability.", "productDetail.information.articleStandards.seals.repreve.title": "Repreve® Unifi, Inc.", "productDetail.information.articleStandards.seals.responsibleDown.detail": "To the good of the animals: Articles with this seal guarantee a species -appropriate attitude and treatment of geese and ducks. Strict guidelines for food quality and accommodation ensure that stuff masts and life plug are prohibited. This seal ensures the traceability of down and feathers.", "productDetail.information.articleStandards.seals.responsibleDown.title": "Responsible Down Standard", "productDetail.information.articleStandards.seals.responsibleWool.detail": "With this seal, the animal welfare is promoted by sheep. Strict criteria for a species -appropriate attitude are determined, including the availability of clean drinking water and sufficient feed. Animal cruelty, such as the painful \"Museling\", is strictly prohibited. In addition, the seal is committed to fair working conditions in shearing and the protection of biodiversity.", "productDetail.information.articleStandards.seals.responsibleWool.title": "Responsible Wool Standard", "productDetail.information.articleStandards.seals.seaqual.detail": "This article contains at least 30% Seaqual ™ fibers, of which at least 10% are made of sea plastic, the rest is \"post-consumer\" polyester. The innovative technology of Seaqual ™ produces sustainable polyester fibers made of recycled sea sculpture.", "productDetail.information.articleStandards.seals.seaqual.title": "Seaqual ™", "productDetail.information.articleStandards.seals.spinnova.detail": "The company has set itself the goal of making the textile industry more sustainable. With your 100% natural Spinnova fiber, you offer a material that is manufactured without the use of chemicals and can be biodegradable. The fibers consist of FSC® or PEFC-certified wood and consume significantly less water than cotton when manufacturing.", "productDetail.information.articleStandards.seals.spinnova.title": "<PERSON><PERSON>", "productDetail.information.articleStandards.seals.sustainableViscose.detail": "The viscose of this stylish article is obtained from sustainable, certified wood from Europe. By using regional wood, short transport routes are made possible and sustainable cultivation is ensured. This enables us to save 50% of water consumption and emissions that would be incurred if the viscotic creation is conventional. Our fibers are manufactured in an environmentally friendly manner and meet high environmental standards.", "productDetail.information.articleStandards.seals.sustainableViscose.title": "Sustainable viscose", "productDetail.information.articleStandards.title": "sustainability", "productDetail.information.brand.title": "brand", "productDetail.information.description.articleNumber": "Article number:", "productDetail.information.description.title": "Description", "productDetail.information.details.title": "Details", "productDetail.information.importantInformation.button.title": "Details on product safety", "productDetail.information.importantInformation.productSafety.responsible.detail": "The economic actor based in the EU is responsible for this product:", "productDetail.information.importantInformation.productSafety.responsible.note": "You will also find the economic actor responsible for the product on the respective product or packaging or in a document attached to the product.", "productDetail.information.importantInformation.productSafety.responsible.title": "Responsible person for the EU:", "productDetail.information.importantInformation.productSafety.title": "Product security", "productDetail.information.importantInformation.title": "Important information", "productDetail.information.return.copy": "If the delivery does not meet your ideas, you can send the goods back to us free of charge. Further information on the {return conditions}.", "productDetail.information.return.copy.SHE": "If the delivery does not meet your ideas, you can send the goods back to us free of charge. Further information on the {return conditions}.", "productDetail.information.return.link": "Return conditions", "productDetail.information.return.title": "return", "productDetail.information.sustainability.copy": "Certifications for this product", "productDetail.information.sustainability.moreInfo": "Learn more", "productDetail.loading.accessibility": "Load", "productDetail.notifyMe.button.title": "Memory email", "productDetail.notifyMe.button.title.LAS": "Notify me", "productDetail.payBack.title": "%{points} payback points", "productDetail.payBack.title.accessibility": "You get %{points} payback points", "productDetail.price.accessibility": "Price %{price}", "productDetail.price.new.accessibility": "New price %{price}", "productDetail.price.old.accessibility": "Old price %{oldPrice}", "productDetail.price.vat.link": "Shipping costs", "productDetail.price.vat.title": "incl. VAT plus {shipping costs}", "productDetail.price.vat.title.SHE": "VAT included.", "productDetail.rating.stars.accessibility": "%{rating} of 5 stars", "productDetail.recommendation.matchingTitle": "Appropriate to this", "productDetail.recommendation.moreFromTheSeriesSubTitle": "Appropriate to this", "productDetail.recommendation.moreFromTheSeriesTitle": "More from the series", "productDetail.recommendation.priceDiscountTitle": "away", "productDetail.recommendation.product.accessibility": "Product recommendation:", "productDetail.recommendation.product.count.accessibility": "Product recommendation %{numberOfCurrentProduct} from %{numberOfProducts}", "productDetail.recommendation.product.title.count.accessibility": "%{title} %{numberOfCurrentProduct} from %{numberOfProducts}", "productDetail.recommendation.recentlyViewedTitle": "Recently seen", "productDetail.recommendation.recommendationSubTitle": "Browse now", "productDetail.recommendation.recommendationTitle": "Exciting alternatives", "productDetail.recommendation.shopTheLook.accessibility": "Outfit recommendation - contains %{numberOfProducts} products", "productDetail.recommendation.shopTheLookSubTitle": "Inspirations for you", "productDetail.recommendation.shopTheLookTitle": "Shop the look", "productDetail.review.bar.accessibility": "%{count} customers have evaluated the article with %{rating} stars", "productDetail.review.button.plural.title": "Show all %{count} reviews", "productDetail.review.button.singular.title": "View rating", "productDetail.review.emptyTitle": "This article has not been reviewed", "productDetail.review.navigation.accessibility": "To all reviews", "productDetail.review.newReview.button.title": "Write product evaluation", "productDetail.review.pagination.accessibility": "You saw %{count} from %{max} reviews - Show more", "productDetail.review.pagination.copy": "You have viewed %{count} of %{max} ratings", "productDetail.review.pagination.loaded.accessibility": "%{reviewsPerPage} More reviews loaded", "productDetail.review.plural.title": "Reviews", "productDetail.review.rating.accessibility": "Product assessment %{rating} stars - written on %{date}", "productDetail.review.reviewerName.accessibility": "From %{reviewerName}", "productDetail.review.showMoreReview.button.title": "Show more", "productDetail.review.singular.title": "Evaluation", "productDetail.review.summery.accessibility": "The average product rating is %{rating} of 5 stars coordinated by %{count}", "productDetail.review.title": "Customer reviews", "productDetail.share.button.accessibility": "Share product", "productDetail.shopUsps.title.accessibility": "Your advantages", "productDetail.shopUsps.usps": "Free shipping from 50 €\nFree return\nInstallment", "productDetail.size.accessibility": "Selected size %{variantName} - Change size", "productDetail.size.advisor.button.title": "<PERSON>ze Advisor", "productDetail.size.title": "Size:", "productDetail.unavailable.overlay.accessibility": "Unavailable", "productDetail.variant.priceDisclaimer": "All prices are exclusive of VAT and shipping costs", "productDetail.variant.priceDisclaimer.LAS": "All prices are including VAT and exclusive of shipping costs", "productDetail.variant.title": "Choose a size", "productDetail.variant.voucher.details": "", "productDetail.variant.voucher.recipient": "For", "productDetail.variant.voucher.textFieldPlaceholder": "Enter names", "productDetail.voucher.textField.count.accessibility": "%{count} from %{max} characters", "productDetail.wishlist.added.accessibility": "Added to the notepad", "productDetail.wishlist.button.title.isNotWishlisted.accessibility": "Add to the notepad", "productDetail.wishlist.button.title.isWishlisted.accessibility": "Remove", "productDetail.wishlist.removed.accessibility": "Removed from the notepad", "productReview.Action.writeReview": "Write review", "productReview.Card.ShowLessReview.Button.title": "Less indicate", "productReview.Card.authorSeparator": "•", "productReview.Card.showMoreReview.Button.title": "Show more", "productReview.Filter.stars": "%{count} stars", "productReview.Pagination.ShowMore": "Show more", "productReview.Rating.disclaimer": "We cannot currently understand for all ratings whether the product that has been rated is really owned by the reviewers. Reviews from March 1, 2024 are all based on verified purchases.", "productReview.Rating.reviewCount": "%{count} reviews", "productReview.Rating.sortByLabel": "Sort", "productReview.Rating.sortByLabel.accessibility": "Sorted by %{sorting} - Change sorting", "productReview.Rating.stars": "%{count} stars", "productReview.Sort.highestRated": "Highest rating", "productReview.Sort.lowestRated": "Lowest rating", "productReview.Sort.mostRecent": "Latest", "productReview.Sort.oldest": "Oldest", "productReview.Sort.title": "Sorting", "productReview.Sort.updated.accessibility": "Sorting updated", "productReview.title": "Reviews", "push.deviceInfo.shopCountry": "shopCountryCode", "pushNotifications.pushOptInAfterCheckout.benefit.confirmation": "Confirmation of Delivery", "pushNotifications.pushOptInAfterCheckout.benefit.offers": "Special Offers", "pushNotifications.pushOptInAfterCheckout.benefit.style": "Latest Styles", "pushNotifications.pushOptinReavailability.benefit.discounts": "Exclusive discounts", "pushNotifications.pushOptinReavailability.benefit.reavailability": "Reavailable products", "pushNotifications.pushOptinReavailability.benefit.shipping": "Shipping confirmation", "pushNotifications.pushOptinReavailability.benefit.trends": "Latest trends", "pushNotifications.settings.checkbox.BON.android": "Receive Notifications", "pushNotifications.settings.checkbox.BPX.android": "Receive Notifications", "pushNotifications.settings.checkbox.CRE.android": "Receive Notifications", "pushNotifications.settings.checkbox.HEI.android": "Receive Notifications", "pushNotifications.settings.checkbox.LAS.android": "Receive Notifications", "pushNotifications.settings.checkbox.MAN.android": "Receive Notifications", "pushNotifications.settings.checkbox.SAN.android": "Receive Notifications", "pushNotifications.settings.checkbox.SHE.android": "Receive Notifications", "pushNotifications.settings.checkbox.WIT.android": "Receive Notifications", "pushNotifications.settings.checkbox.YLFLNL.android": "Receive Notifications", "pushNotifications.settings.checkbox.YLFLSE.android": "Receive Notifications", "pushNotifications.settings.checkbox.android": "Receive Notifications", "pushNotifications.settings.description.BON.android": "Stay up to date and receive notifications with exclusive reductions, inspirations and the latest trend!", "pushNotifications.settings.description.BPX.android": "Stay up to date and receive notifications with exclusive reductions, inspirations and the latest trend!", "pushNotifications.settings.description.CRE.android": "Stay up to date and receive notifications with exclusive reductions, inspirations and the latest trend!", "pushNotifications.settings.description.HEI.android": "Stay up to date and receive notifications with exclusive reductions, inspirations and the latest trend!", "pushNotifications.settings.description.LAS.android": "Stay up to date and receive notifications with exclusive reductions, inspirations and the latest trend!", "pushNotifications.settings.description.MAN.android": "Stay up to date and receive notifications with exclusive reductions, inspirations and the latest trend!", "pushNotifications.settings.description.SAN.android": "Stay up to date and receive notifications with exclusive reductions, inspirations and the latest trend!", "pushNotifications.settings.description.SHE.android": "Stay up to date and receive notifications with exclusive reductions, inspirations and the latest trend!", "pushNotifications.settings.description.WIT.android": "Stay up to date and receive notifications with exclusive reductions, inspirations and the latest trend!", "pushNotifications.settings.description.YLFLNL.android": "Stay up to date and receive notifications with exclusive reductions, inspirations and the latest trend!", "pushNotifications.settings.description.YLFLSE.android": "Stay up to date and receive notifications with exclusive reductions, inspirations and the latest trend!", "pushNotifications.settings.description.android": "Stay up to date and receive notifications with exclusive reductions, inspirations and the latest trend!", "pushNotifications.settings.dialogMissingSystemPermission.settings.button.BON.android": "Settings", "pushNotifications.settings.dialogMissingSystemPermission.settings.button.BPX.android": "Settings", "pushNotifications.settings.dialogMissingSystemPermission.settings.button.CRE.android": "Settings", "pushNotifications.settings.dialogMissingSystemPermission.settings.button.HEI.android": "Settings", "pushNotifications.settings.dialogMissingSystemPermission.settings.button.LAS.android": "Settings", "pushNotifications.settings.dialogMissingSystemPermission.settings.button.MAN.android": "Settings", "pushNotifications.settings.dialogMissingSystemPermission.settings.button.SAN.android": "Settings", "pushNotifications.settings.dialogMissingSystemPermission.settings.button.SHE.android": "Settings", "pushNotifications.settings.dialogMissingSystemPermission.settings.button.WIT.android": "Settings", "pushNotifications.settings.dialogMissingSystemPermission.settings.button.YLFLNL.android": "Settings", "pushNotifications.settings.dialogMissingSystemPermission.settings.button.YLFLSE.android": "Settings", "pushNotifications.settings.dialogMissingSystemPermission.settings.button.android": "Settings", "pushNotifications.settings.dialogMissingSystemPermission.title.BON.android": "Missing permission", "pushNotifications.settings.dialogMissingSystemPermission.title.BPX.android": "Missing permission", "pushNotifications.settings.dialogMissingSystemPermission.title.CRE.android": "Missing permission", "pushNotifications.settings.dialogMissingSystemPermission.title.HEI.android": "Missing permission", "pushNotifications.settings.dialogMissingSystemPermission.title.LAS.android": "Missing permission", "pushNotifications.settings.dialogMissingSystemPermission.title.MAN.android": "Missing permission", "pushNotifications.settings.dialogMissingSystemPermission.title.SAN.android": "Missing permission", "pushNotifications.settings.dialogMissingSystemPermission.title.SHE.android": "Missing permission", "pushNotifications.settings.dialogMissingSystemPermission.title.WIT.android": "Missing permission", "pushNotifications.settings.dialogMissingSystemPermission.title.YLFLNL.android": "Missing permission", "pushNotifications.settings.dialogMissingSystemPermission.title.YLFLSE.android": "Missing permission", "pushNotifications.settings.dialogMissingSystemPermission.title.android": "Missing permission", "pushNotifications.settings.moreInformation.BON.android": "More information", "pushNotifications.settings.moreInformation.BPX.android": "More information", "pushNotifications.settings.moreInformation.CRE.android": "More information", "pushNotifications.settings.moreInformation.HEI.android": "More information", "pushNotifications.settings.moreInformation.LAS.android": "More information", "pushNotifications.settings.moreInformation.MAN.android": "More information", "pushNotifications.settings.moreInformation.SAN.android": "More information", "pushNotifications.settings.moreInformation.SHE.android": "More information", "pushNotifications.settings.moreInformation.WIT.android": "More information", "pushNotifications.settings.moreInformation.YLFLNL.android": "More information", "pushNotifications.settings.moreInformation.YLFLSE.android": "More information", "pushNotifications.settings.moreInformation.android": "More information", "pushNotifications.settings.moreInformation.openedText.legalLink.BON.android": "privacy policy", "pushNotifications.settings.moreInformation.openedText.legalLink.BPX.android": "privacy policy", "pushNotifications.settings.moreInformation.openedText.legalLink.CRE.android": "privacy policy", "pushNotifications.settings.moreInformation.openedText.legalLink.HEI.android": "privacy policy", "pushNotifications.settings.moreInformation.openedText.legalLink.LAS.android": "privacy policy", "pushNotifications.settings.moreInformation.openedText.legalLink.MAN.android": "privacy policy", "pushNotifications.settings.moreInformation.openedText.legalLink.SAN.android": "privacy policy", "pushNotifications.settings.moreInformation.openedText.legalLink.SHE.android": "privacy policy", "pushNotifications.settings.moreInformation.openedText.legalLink.WIT.android": "privacy policy", "pushNotifications.settings.moreInformation.openedText.legalLink.YLFLNL.android": "privacy policy", "pushNotifications.settings.moreInformation.openedText.legalLink.YLFLSE.android": "privacy policy", "pushNotifications.settings.moreInformation.openedText.legalLink.android": "privacy policy", "pushNotifications.settings.title.BON.android": "Notifications", "pushNotifications.settings.title.BPX.android": "Notifications", "pushNotifications.settings.title.CRE.android": "Notifications", "pushNotifications.settings.title.HEI.android": "Notifications", "pushNotifications.settings.title.LAS.android": "Notifications", "pushNotifications.settings.title.MAN.android": "Notifications", "pushNotifications.settings.title.SAN.android": "Notifications", "pushNotifications.settings.title.SHE.android": "Notifications", "pushNotifications.settings.title.WIT.android": "Notifications", "pushNotifications.settings.title.YLFLNL.android": "Notifications", "pushNotifications.settings.title.YLFLSE.android": "Notifications", "pushNotifications.settings.title.android": "Notifications", "pushPromotion.banner.confirm": "Yes", "pushPromotion.banner.copy": "Do you want to be informed about price reductions immediately?", "pushPromotion.banner.description.accessibility": "Banner to enable push notifications", "pushPromotion.banner.heading.accessibility": "heading", "pushPromotion.banner.title": "Never miss a discount again", "pushPromotion.footnote.labelLegalLink": "privacy policy", "pushPromotion.optIn.button": "Sounds great!", "pushPromotion.optIn.button.BON.ios": "Ok, activate", "pushPromotion.optIn.button.BPX.ios": "Ok, activate", "pushPromotion.optIn.button.HEI.ios": "prepush button Klingt super!", "pushPromotion.optIn.button.LAS.ios": "Klingt super!", "pushPromotion.optIn.button.MAN.ios": "Sounds good!", "pushPromotion.optIn.button.SAN.ios": "prepush button Klingt super!", "pushPromotion.optIn.button.SHE.ios": "Klingt super!", "pushPromotion.optIn.button.WIT.ios": "prepush button", "pushPromotion.optIn.button.YLFLNL.android": "Enable Notifications", "pushPromotion.optIn.button.YLFLNL.ios": "prePush.pushOptInButton.title", "pushPromotion.optIn.button.YLFLSE.android": "Enable Notifications", "pushPromotion.optIn.button.YLFLSE.ios": "prePush.pushOptInButton.title", "pushPromotion.optIn.firstArgument": "Exclusive discounts", "pushPromotion.optIn.footNote.text": "\n    You can revoke your consent at any time.\nClick here for the     \n    <u>data protection declaration</u>\n      \n", "pushPromotion.optIn.footNote.text.BON.android": "You can revoke your consent at any time.\nClick here for the <u>data protection declaration</u>", "pushPromotion.optIn.footNote.text.BON.ios": "You can revoke your consent at any time. Click here for the privacy policy.", "pushPromotion.optIn.footNote.text.BPX.android": "You can revoke your consent at any time.\nClick here for the <u>data protection declaration</u>", "pushPromotion.optIn.footNote.text.BPX.ios": "You can revoke your consent at any time. Click here for the privacy policy.", "pushPromotion.optIn.footNote.text.HEI.android": "\n    You can revoke your consent at any time.\nClick here for the     \n    <u>data protection declaration</u>\n      \n  ", "pushPromotion.optIn.footNote.text.HEI.ios": "prepush footnote Sie können Ihre Einwilligung jederzeit widerufen. Hier gehts zur <u>Datenschutzerklärung</u>", "pushPromotion.optIn.footNote.text.LAS.android": "\n    You can revoke your consent at any time.\nClick here for the     \n    <u>data protection declaration</u>\n      \n  ", "pushPromotion.optIn.footNote.text.LAS.ios": "Sie können Ihre Zustimmung jederzeit widerrufen.\nKlicken Sie hier für die <u>Datenschutzerklärung</u>", "pushPromotion.optIn.footNote.text.MAN.android": "\n                You can revoke your consent at any time.\nClick here for the \n        \n        <u>data protection declaration</u>\n            \n    ", "pushPromotion.optIn.footNote.text.MAN.ios": "You can withdraw your consent at any time.\nClick here for the <u>Privacy Policy</u>", "pushPromotion.optIn.footNote.text.SAN.android": "\n        You can revoke your consent at any time.\nClick here for the \n        <u>data protection declaration</u>\n    ", "pushPromotion.optIn.footNote.text.SAN.ios": "prepush footnote Sie können Ihre Einwilligung jederzeit widerufen. Hier gehts zur <u>Datenschutzerklärung</u>", "pushPromotion.optIn.footNote.text.SHE.android": "\n    You can revoke your consent at any time.\nClick here for the     \n    <u>data protection declaration</u>\n      \n  ", "pushPromotion.optIn.footNote.text.SHE.ios": "Sie können Ihre Zustimmung jederzeit widerrufen.\nKlicken Sie hier für die <u>Datenschutzerklärung</u>", "pushPromotion.optIn.footNote.text.WIT.android": "\n        You can revoke your consent at any time.\nClick here for the \n        <u>data protection declaration</u>\n    ", "pushPromotion.optIn.footNote.text.WIT.ios": "prepush footnote", "pushPromotion.optIn.footNote.text.YLFLNL.android": "\n        You can revoke your consent at any time.\nClick here for the \n        <u>data protection declaration</u>\n    ", "pushPromotion.optIn.footNote.text.YLFLNL.ios": "prePush.pushOptInFootnote.text", "pushPromotion.optIn.footNote.text.YLFLSE.android": "\n        You can revoke your consent at any time.\nClick here for the \n        <u>data protection declaration</u>\n    ", "pushPromotion.optIn.footNote.text.YLFLSE.ios": "prePush.pushOptInFootnote.text", "pushPromotion.optIn.footnote": "You can revoke your consent at any time.", "pushPromotion.optIn.pageTitle.accessibility": "Overlay to accept push notifications", "pushPromotion.optIn.title": "Activate notifications\nto stay up to date", "pushPromotion.optIn.title.BON.ios": "Activate notifications to stay up to date!", "pushPromotion.optIn.title.BPX.ios": "Activate notifications to stay up to date!", "pushPromotion.optIn.title.HEI.ios": "prepush caption Aktivieren Sie Benachrichtigungen, um Up to Date zu bleiben", "pushPromotion.optIn.title.LAS.ios": "Aktivieren Sie die Benachrichtigungen, um zu<PERSON>t von unseren Aktionen zu erfahren.", "pushPromotion.optIn.title.MAN.android": "Activate notifications\n to stay up to date", "pushPromotion.optIn.title.MAN.ios": "Activate the notifications to be the first to know about news and special offers.", "pushPromotion.optIn.title.SAN.ios": "prepush caption Aktivieren Sie Benachrichtigungen, um Up to Date zu bleiben", "pushPromotion.optIn.title.SHE.ios": "Aktivieren Sie die Benachrichtigungen, um zu<PERSON>t von unseren Aktionen zu erfahren.", "pushPromotion.optIn.title.WIT.ios": "prepush caption", "pushPromotion.optIn.title.YLFLNL.ios": "prePush.pushOptInCaption.text", "pushPromotion.optIn.title.YLFLSE.ios": "prePush.pushOptInCaption.text", "pushPromotion.optIn.usp.list": "- Exclusive\nDiscounts\n- Latest\nTrends\n- Delivery\nStatus\n", "pushPromotion.optIn.usp.list.accessibility": "list with advantages", "pushPromotion.optIn.uspListTitle.accessibility": "List with advantages", "pushPromotion.pushOptIn.caption": "Enable Notifications and stay up-to-date", "pushPromotion.pushOptInAfterCheckout.caption": "Stay up-to-date", "pushPromotion.pushOptinReavailability.title": "Thank you very much! You will be updated by e-mail", "pushPromotion.uspList": "- Exclusive\nDiscounts\n- Latest\nTrends\n- Delivery\nStatus", "pushPromotion.uspList.HEI.android": "- Exclusive\nDiscounts\n- Latest\nTrends\n- Delivery\nStatus\n", "pushPromotion.uspList.HEI.ios": "- Exklusive Rabatte\n- Neuste Trends", "pushPromotion.uspList.LAS.android": "- Exclusive\nDiscounts\n- Latest\nTrends\n- Delivery\nStatus\n", "pushPromotion.uspList.LAS.ios": "- Exclusive\nDiscounts\n- Latest\nTrends\n- Delivery\nStatus", "pushPromotion.uspList.SAN.android": "- Exclusive\nDiscounts\n- Latest\nTrends\n- Delivery\nStatus\n", "pushPromotion.uspList.SAN.ios": "- First text\n- Second text", "pushPromotion.uspList.SHE.android": "- Exclusive\nDiscounts\n- Latest\nTrends\n- Delivery\nStatus\n", "pushPromotion.uspList.SHE.ios": "- Exclusive\nDiscounts\n- Latest\nTrends\n- Delivery\nStatus", "pushPromotion.uspList.WIT.android": "- Exclusive\nDiscounts\n- Latest\nTrends\n- Delivery\nStatus\n", "pushPromotion.uspList.WIT.ios": "- Exclusive\nDiscounts\n- Latest\nTrends\n- Delivery\nStatus", "pushPromotion.uspList.YLFLNL.android": "- Exclusive\nDiscounts\n- Latest\nTrends\n- Delivery\nStatus\n", "pushPromotion.uspList.YLFLNL.ios": "- exclusive\ndiscounts\n- latest\ntrends", "pushPromotion.uspList.YLFLSE.android": "- Exclusive\nDiscounts\n- Latest\nTrends\n- Delivery\nStatus\n", "pushPromotion.uspList.YLFLSE.ios": "- exclusive\ndiscounts\n- latest\ntrends", "recommendation.dialog.title": "Recommend app", "recommendation.message.text": "Hey, you might like this app. Get it now on the Apple App Store or Google Play Store:", "salutation.loggedIn": "Welcome, %{firstname} %{lastname}!", "salutation.loggedIn.HEI": "Welcome, %{firstname}!", "salutation.loggedIn.LAS.android": "Welcome!", "salutation.loggedIn.LAS.ios": "Welcome, %{firstname} %{lastname}!", "salutation.loggedIn.SAN": "Welcome, %{firstname}!", "salutation.loggedIn.SHE.android": "Welcome, %{firstname} %{lastname}!", "salutation.loggedIn.SHE.ios": "Welcome, %{firstname}!", "salutation.loggedIn.WIT": "Welcome, %{firstname}!", "salutation.loggedIn.YLFLNL": "Welcome, %{firstname}!", "salutation.loggedIn.YLFLSE": "Welcome, %{firstname}!", "salutation.loggedOut": "Welcome!", "salutation.login": "Welcome, %{loginLink}", "salutation.loginLink": "login?", "search.account.title": "your account", "search.account.title.BON": "Your account", "search.account.title.BPX": "Your account", "search.account.title.HEI.android": "Your account", "search.account.title.HEI.ios": "My account", "search.account.title.LAS.android": "Your account", "search.account.title.MAN.android": "Your account", "search.account.title.SAN.android": "My account", "search.account.title.SHE.android": "Your account", "search.account.title.WIT.android": "My account", "search.account.title.YLFLNL.android": "My account", "search.account.title.YLFLSE.android": "My account", "search.button.accessibility": "Search", "search.clear.description": "Clear current search", "search.empty.description": "Search for unique lingerie, beachwear and lots more.", "search.empty.description.BON.android": "Search for clothing and other great items", "search.empty.description.BON.ios": "Search for clothes, accessories, \nHousehold items and other great items.", "search.empty.description.BPX.android": "Search for clothing and other great items", "search.empty.description.BPX.ios": "Search for clothes, accessories, \nHousehold items and other great items.", "search.empty.description.HEI.android": "Search for clothing, furniture and other great items.", "search.empty.description.HEI.ios": "Suchen Sie nach Bekleidung, Wäsche und weiteren tollen Artikeln.", "search.empty.description.MAN.android": "Search for clothing, furniture and other good things", "search.empty.description.MAN.ios": "Find clothes, furniture and more.", "search.empty.description.SAN.android": "Search for clothing, underwear and other great items.", "search.empty.description.SAN.ios": "Suchen Sie nach Bekleidung, Wäsche und weiteren tollen Artikeln.", "search.empty.description.SHE.android": "Search for dresses, jackets and many other fashion highlights.", "search.empty.description.SHE.ios": "Suchen Sie nach Bekleidung, Wäsche und weiteren tollen Artikeln.", "search.empty.description.WIT.android": "Search for clothing, shoes, accessories and other cool stuff.", "search.empty.description.WIT.ios": "Search for clothing, accessories, and other great items.", "search.empty.description.YLFLNL.android": "Search for clothing, furniture and other great items.", "search.empty.description.YLFLNL.ios": "Search for clothing, accessories, and other great items.", "search.empty.description.YLFLSE.android": "Search for clothing, furniture and other great items.", "search.empty.description.YLFLSE.ios": "Search for clothing, accessories, and other great items.", "search.empty.title": "Find your favourite items", "search.empty.title.BON.android": "Find your favourite article", "search.empty.title.BON.ios": "Find favorite items", "search.empty.title.BPX.android": "Find your favourite article", "search.empty.title.BPX.ios": "Find favorite items", "search.empty.title.HEI.ios": "Lieblingsar<PERSON><PERSON> finden", "search.empty.title.MAN.ios": "Find your favorite items", "search.empty.title.SAN.ios": "Lieblingsar<PERSON><PERSON> finden", "search.empty.title.SHE.ios": "Lieblingsar<PERSON><PERSON> finden", "search.empty.title.WIT.android": "Find your favourites", "search.empty.title.WIT.ios": "Find favorite items", "search.empty.title.YLFLNL.ios": "Find favorite items", "search.empty.title.YLFLSE.ios": "Find favorite items", "search.history.clear": "Delete", "search.history.delete": "Delete", "search.history.delete.contentDescription": "search history delete image description", "search.history.deleted.accessibility": "Search history deleted", "search.history.title": "Your latest searches", "search.history.title.BON.ios": "SEARCH HISTORY", "search.history.title.BPX.ios": "SEARCH HISTORY", "search.history.title.HEI.ios": "ZULETZT GESUCHT", "search.history.title.LAS.ios": "ZULETZT GESUCHT", "search.history.title.MAN.ios": "LAST SEARCHED", "search.history.title.SAN.ios": "SEARCH HISTORY", "search.history.title.SHE.ios": "ZULETZT GESUCHT", "search.history.title.WIT.ios": "SEARCH HISTORY", "search.history.title.YLFLNL.ios": "SEARCH HISTORY", "search.history.title.YLFLSE.ios": "SEARCH HISTORY", "search.input.hint": "What are you searching for?", "search.input.hint.BON": "What are you looking for?", "search.input.hint.BPX": "What are you looking for?", "search.input.hint.HEI.ios": "Wonach suchen <PERSON>?", "search.input.hint.LAS.ios": "Wonach suchen <PERSON>?", "search.input.hint.MAN.ios": "What are you looking for?", "search.input.hint.SAN": "What are you looking for?", "search.input.hint.SHE.ios": "Wonach suchen <PERSON>?", "search.input.hint.WIT": "What are you looking for?", "search.input.hint.YLFLNL": "What are you looking for?", "search.input.hint.YLFLSE": "What are you looking for?", "search.keyboard.close.accessibility": "Close keyboard", "search.removeHistory.button": "Clear search history", "search.removeHistory.button.BON.ios": "clear", "search.removeHistory.button.BPX.ios": "clear", "search.removeHistory.button.LAS.ios": "LEEREN", "search.removeHistory.button.MAN.ios": "EMPTY", "search.removeHistory.button.SAN.ios": "EMPTY", "search.removeHistory.button.SHE.ios": "LEEREN", "search.removeHistory.button.WIT.ios": "EMPTY", "search.removeHistory.button.YLFLNL.ios": "EMPTY", "search.removeHistory.button.YLFLSE.ios": "EMPTY", "search.searchBar.clear.button.accessibility": "Delete Text", "search.shortcuts.accountAddress": "My addresses", "search.shortcuts.accountBalance": "My account balance", "search.shortcuts.accountBalance.BON": "My Orders", "search.shortcuts.accountBalance.BPX": "My Orders", "search.shortcuts.accountBalance.CRE": "My Orders", "search.shortcuts.accountBalance.HEI": "My Orders", "search.shortcuts.accountBalance.LAS": "My Orders", "search.shortcuts.accountBalance.MAN": "My Orders", "search.shortcuts.accountBalance.SAN": "My Orders", "search.shortcuts.accountBalance.SHE": "My Orders", "search.shortcuts.accountBalance.WIT": "My Orders", "search.shortcuts.accountBalance.YLFLNL": "My Orders", "search.shortcuts.accountBalance.YLFLSE": "My Orders", "search.shortcuts.accountNewIn": "New In", "search.shortcuts.accountOrders": "My Orders", "search.shortcuts.accountOverview": "Account overview", "search.shortcuts.listTitle.accessibility": "Account shortcuts", "search.shortcuts.sale": "Sale", "search.shortcuts.service": "Service", "search.slot.placeholder": "What are you looking for?", "search.suggestion.category.description": "category image description", "search.suggestion.keyword.description": "keyword image description", "search.suggestion.product.image.description.accesibility": "product image description", "search.suggestions.category.accessibility": "Category suggestion", "search.suggestions.category.prefix": "in %{category}", "search.suggestions.category.title": "Categories", "search.suggestions.category.title.SHE.ios": "<PERSON><PERSON><PERSON>", "search.suggestions.category.title.YLFLNL.ios": "<PERSON><PERSON><PERSON>", "search.suggestions.category.title.YLFLSE.ios": "<PERSON><PERSON><PERSON>", "search.suggestions.emptyText": "Try a different search term or check your spelling", "search.suggestions.emptyTitle": "No Results for \"%{text}\"", "search.suggestions.header.accessibility": "Suggestions", "search.suggestions.keyword.accessibility": "Search suggestion", "search.suggestions.listTitle.accessibility": "Search suggestions", "search.suggestions.noResults.accessibility": "No results available", "search.suggestions.price.prefix": "from", "search.suggestions.price.prefix.SHE.ios": "ab", "search.suggestions.price.prefix.YLFLNL.ios": "ab", "search.suggestions.price.prefix.YLFLSE.ios": "ab", "search.suggestions.product.accessibility": "Product suggestion", "search.suggestions.products.title": "Products", "search.suggestions.products.title.SHE.ios": "Produkte", "search.suggestions.products.title.YLFLNL.ios": "Produkte", "search.suggestions.products.title.YLFLSE.ios": "Produkte", "search.suggestions.results.accessibility": "%{value} results available", "settings.history.deleted": "Search history cleared", "settings.info": "Information", "settings.open.source.licences": "Open Source Licenses", "settings.push.notification.summary": "Push notifications for Sale, Promotions, etc", "settings.push.notification.title": "Notifications", "settings.search.history.summary": "Search history saves your previous searches for quick access", "settings.version": "Copied into the clipboard", "settings.version.copied": "Copied into the clipboard", "shakeADeal.deal.button.text": "That's my deal", "shakeADeal.deal.discountPrice": "Now", "shakeADeal.deal.discountPrice.HEI": "now", "shakeADeal.deal.discountPrice.LAS.android": "now", "shakeADeal.deal.discountPrice.MAN": "now", "shakeADeal.deal.discountPrice.SAN": "now", "shakeADeal.deal.discountPrice.SHE": "now", "shakeADeal.deal.discountPrice.WIT": "now", "shakeADeal.deal.discountPrice.YLFLNL": "now", "shakeADeal.deal.discountPrice.YLFLSE": "now", "shakeADeal.deal.promoCodeButton.text": "Accept & shop", "shakeADeal.deal.promoCodeButton.text.HEI.android": "Activate & shop", "shakeADeal.deal.promoCodeButton.text.HEI.ios": "Activate & Shop", "shakeADeal.deal.promoCodeButton.text.MAN.android": "Activate & shop", "shakeADeal.deal.promoCodeButton.text.MAN.ios": "Activate & Shop", "shakeADeal.deal.promoCodeButton.text.SAN.ios": "Activate & Shop", "shakeADeal.deal.promoCodeButton.text.SHE.ios": "Activate & Shop", "shakeADeal.deal.promoCodeButton.text.WIT.android": "Activate & shop", "shakeADeal.deal.promoCodeButton.text.WIT.ios": "Activate & Shop", "shakeADeal.deal.promoCodeButton.text.YLFLNL.android": "Activate & shop", "shakeADeal.deal.promoCodeButton.text.YLFLNL.ios": "Activate & Shop", "shakeADeal.deal.promoCodeButton.text.YLFLSE.android": "Activate & shop", "shakeADeal.deal.promoCodeButton.text.YLFLSE.ios": "Activate & Shop", "shakeADeal.inAppMessage.invalidCode": "Ups something went wrong, the promocode has\nnot been activated.", "shakeADeal.inAppMessage.invalidCode.HEI.android": "Ups something went wrong, the promocode has not been activated.", "shakeADeal.inAppMessage.invalidCode.HEI.ios": "Ups something went wrong, the promocode has\nnot been activated.", "shakeADeal.inAppMessage.invalidCode.LAS.android": "Ups something went wrong, the promocode has not been activated.", "shakeADeal.inAppMessage.invalidCode.LAS.ios": "Ups something went wrong, the promocode has\nnot been activated.", "shakeADeal.inAppMessage.invalidCode.MAN.android": "Ups something went wrong, the promocode has not been activated.", "shakeADeal.inAppMessage.invalidCode.MAN.ios": "Ups something went wrong, the promocode has\nnot been activated.", "shakeADeal.inAppMessage.invalidCode.SAN.android": "Ups something went wrong, the promocode has not been activated.", "shakeADeal.inAppMessage.invalidCode.SAN.ios": "Ups something went wrong, the promocode has\nnot been activated.", "shakeADeal.inAppMessage.invalidCode.SHE.android": "Ups something went wrong, the promocode has not been activated.", "shakeADeal.inAppMessage.invalidCode.SHE.ios": "Ups something went wrong, the promocode has\nnot been activated.", "shakeADeal.inAppMessage.invalidCode.WIT.android": "Ups something went wrong, the promocode has not been activated.", "shakeADeal.inAppMessage.invalidCode.WIT.ios": "Ups something went wrong, the promocode has\nnot been activated.", "shakeADeal.inAppMessage.invalidCode.YLFLNL.android": "Ups something went wrong, the promocode has not been activated.", "shakeADeal.inAppMessage.invalidCode.YLFLNL.ios": "Ups something went wrong, the promocode has\nnot been activated.", "shakeADeal.inAppMessage.invalidCode.YLFLSE.android": "Ups something went wrong, the promocode has not been activated.", "shakeADeal.inAppMessage.invalidCode.YLFLSE.ios": "Ups something went wrong, the promocode has\nnot been activated.", "shakeADeal.inAppMessage.validCode": "Promotion is activated and applied to your basket, start to shop and enjoy the discount.", "shakeADeal.offBoarding.description": "Come back tomorrow for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.description.BON.android": "Come back tomorrow for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.description.BON.ios": "Come back tomorrow for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.description.BPX.android": "Come back tomorrow for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.description.BPX.ios": "Come back tomorrow for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.description.HEI.android": "Come back tomorrow for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.description.HEI.ios": "Come back for brand new deals!", "shakeADeal.offBoarding.description.LAS.android": "Come back tomorrow for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.description.MAN.android": "Come back tomorrow for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.description.MAN.ios": "Come back tomorrow for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.description.SAN.android": "Come back for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.description.SAN.ios": "Come back for brand new deals!", "shakeADeal.offBoarding.description.SHE.android": "Come back tomorrow for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.description.SHE.ios": "Come back tomorrow for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.description.WIT.android": "Come back tomorrow for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.description.WIT.ios": "Come back tomorrow for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.description.YLFLNL.android": "Come back tomorrow for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.description.YLFLNL.ios": "Come back tomorrow for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.description.YLFLSE.android": "Come back tomorrow for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.description.YLFLSE.ios": "Come back tomorrow for %{numberOfDeals} brand new deals!", "shakeADeal.offBoarding.title": "That’s it for today", "shakeADeal.offBoarding.title.BON.android": "That's it for today", "shakeADeal.offBoarding.title.BON.ios": "That’s it for today", "shakeADeal.offBoarding.title.BPX.android": "That's it for today", "shakeADeal.offBoarding.title.BPX.ios": "That’s it for today", "shakeADeal.offBoarding.title.HEI.android": "That's it for today", "shakeADeal.offBoarding.title.HEI.ios": "That’s it for today", "shakeADeal.offBoarding.title.LAS.android": "That's it for today", "shakeADeal.offBoarding.title.LAS.ios": "That’s it for today", "shakeADeal.offBoarding.title.MAN.android": "That's it for today", "shakeADeal.offBoarding.title.MAN.ios": "That’s it for today", "shakeADeal.offBoarding.title.SAN.android": "That's it for today", "shakeADeal.offBoarding.title.SAN.ios": "That’s it for today", "shakeADeal.offBoarding.title.SHE.android": "That's it for today", "shakeADeal.offBoarding.title.SHE.ios": "That’s it for today", "shakeADeal.offBoarding.title.WIT.android": "That's it for today", "shakeADeal.offBoarding.title.WIT.ios": "That’s it for today", "shakeADeal.offBoarding.title.YLFLNL.android": "That's it for today", "shakeADeal.offBoarding.title.YLFLNL.ios": "That’s it for today", "shakeADeal.offBoarding.title.YLFLSE.android": "That's it for today", "shakeADeal.offBoarding.title.YLFLSE.ios": "That’s it for today", "shakeADeal.onBoarding.button": "Yes got it!", "shakeADeal.onBoarding.button.BON.ios": "Yes, i got it!", "shakeADeal.onBoarding.button.BPX.ios": "Yes, i got it!", "shakeADeal.onBoarding.button.HEI.ios": "I Got it!", "shakeADeal.onBoarding.button.SAN.ios": "I Got it!", "shakeADeal.onBoarding.description": "1. Shake your phone to see a deal.\n2. Decide\n within %{seconds} seconds if you want to see more details.\n3. Not interested? Shake your\n phone again.\n4. You’ll get %{numberOfOffers} deals per day. Every day.\n", "shakeADeal.onBoarding.description.BON.android": "1. Shake your phone to see a deal.\n2. Decide\n        within %{seconds} seconds if you want to see more details.\n3. Not interested? Shake your\n        phone again.\n4. You’ll get %{numberOfOffers} deals per day. Every day.\n    ", "shakeADeal.onBoarding.description.BON.ios": "•   Shake your phone to see a deal.\n•   Decide within 10 seconds if you want to see more details. \n•   Not interested? Shake your phone \nagain.•   You’ll get 5 deals per day. Every day.", "shakeADeal.onBoarding.description.BPX.android": "1. Shake your phone to see a deal.\n2. Decide\n        within %{seconds} seconds if you want to see more details.\n3. Not interested? Shake your\n        phone again.\n4. You’ll get %{numberOfOffers} deals per day. Every day.\n    ", "shakeADeal.onBoarding.description.BPX.ios": "•   Shake your phone to see a deal.\n•   Decide within 10 seconds if you want to see more details. \n•   Not interested? Shake your phone \nagain.•   You’ll get 5 deals per day. Every day.", "shakeADeal.onBoarding.description.HEI.android": "1. Shake your phone to see a deal.\n2. Decide\n        within %{seconds} seconds if you want to see more details.\n3. Not interested? Shake your\n        phone again.\n4. You’ll get %{numberOfOffers} deals per day. Every day.\n    ", "shakeADeal.onBoarding.description.HEI.ios": "•   Shake your phone to see a deal.\n•   Decide within %{seconds} seconds if you want to see more details. \n•   Not interested? Shake your phone again.\n•   You’ll get %{numberOfOffers} deals per day. Every day.", "shakeADeal.onBoarding.description.LAS.android": "1. Shake your phone to see a deal.\n2. Decide\n        within %{seconds} seconds if you want to see more details.\n3. Not interested? Shake your\n        phone again.\n4. You’ll get %{numberOfOffers} deals per day. Every day.\n    ", "shakeADeal.onBoarding.description.MAN.android": "1. Shake your phone to see a deal.\n2. Decide\n        within %{seconds} seconds if you want to see more details.\n3. Not interested? Shake your\n        phone again.\n4. You’ll get %{numberOfOffers} deals per day. Every day.\n    ", "shakeADeal.onBoarding.description.MAN.ios": "1. Shake your phone to see a deal.\n2. Decide within %{seconds} seconds if you want to see more details.\n3. Not interested? Shake your phone again.\n4. You’ll get %{numberOfOffers} deals per day. Every day.", "shakeADeal.onBoarding.description.SAN.android": "1. Shake your phone to see your deal of the day.\n2. Decide within %{seconds} seconds if you want to use the deal.\n3. Not interested? Just let the remaining time pass.\n4. Look forward to more deals here in your Sieh an! app.", "shakeADeal.onBoarding.description.SAN.ios": "1. Shake your phone to see your deal of the day.\n2. Decide within %{seconds} seconds if you want to use the deal.\n3. Not interested? Just let the remaining time pass.\n4. Look forward to more deals here in your Sieh an! app.", "shakeADeal.onBoarding.description.SHE.android": "1. Shake your phone to see a deal.\n2. Decide\n        within %{seconds} seconds if you want to see more details.\n3. Not interested? Shake your\n        phone again.\n4. You’ll get %{numberOfOffers} deals per day. Every day.\n    ", "shakeADeal.onBoarding.description.SHE.ios": "1. Shake your phone to see a deal.\n2. Decide within %{seconds} seconds if you want to see more details.\n3. Not interested? Shake your phone again.\n4. You’ll get %{numberOfOffers} deals per day. Every day.", "shakeADeal.onBoarding.description.WIT.android": "1. Shake your phone to see a deal.\n2. Decide\n        within %{seconds} seconds if you want to see more details.\n3. Not interested? Shake your\n        phone again.\n4. You’ll get %{numberOfOffers} deals per day. Every day.\n    ", "shakeADeal.onBoarding.description.WIT.ios": "1. Shake your phone to see a deal.\n2. Decide within %{seconds} seconds if you want to see more details.\n3. Not interested? Shake your phone again.\n4. You’ll get %{numberOfOffers} deals per day. Every day.", "shakeADeal.onBoarding.description.YLFLNL.android": "1. Shake your phone to see a deal.\n2. Decide\n        within %{seconds} seconds if you want to see more details.\n3. Not interested? Shake your\n        phone again.\n4. You’ll get %{numberOfOffers} deals per day. Every day.\n    ", "shakeADeal.onBoarding.description.YLFLNL.ios": "1. Shake your phone to see a deal.\n2. Decide within %{seconds} seconds if you want to see more details.\n3. Not interested? Shake your phone again.\n4. You’ll get %{numberOfOffers} deals per day. Every day.", "shakeADeal.onBoarding.description.YLFLSE.android": "1. Shake your phone to see a deal.\n2. Decide\n        within %{seconds} seconds if you want to see more details.\n3. Not interested? Shake your\n        phone again.\n4. You’ll get %{numberOfOffers} deals per day. Every day.\n    ", "shakeADeal.onBoarding.description.YLFLSE.ios": "1. Shake your phone to see a deal.\n2. Decide within %{seconds} seconds if you want to see more details.\n3. Not interested? Shake your phone again.\n4. You’ll get %{numberOfOffers} deals per day. Every day.", "shakeADeal.onBoarding.title": "How it works", "shakeADeal.overlay.title": "Shake \n for your Deal", "shakeADeal.overlay.title.BON.android": "Potrząśnij telefonem by <PERSON><PERSON><PERSON><PERSON><PERSON> ofertę!", "shakeADeal.overlay.title.BON.ios": "Shake \n for your Deal", "shakeADeal.overlay.title.BPX.android": "Potrząśnij telefonem by <PERSON><PERSON><PERSON><PERSON><PERSON> ofertę!", "shakeADeal.overlay.title.BPX.ios": "Shake \n for your Deal", "shakeADeal.overlay.title.LAS.android": "Shake\nfor your next Deal", "shakeADeal.overlay.title.LAS.ios": "Shake \n for your Deal", "shakeADeal.overlay.title.SHE.android": "Shake\nfor your next Deal", "shakeADeal.overlay.title.SHE.ios": "Shake \n for your Deal", "shopFinder.alert.locationNotAuthorize.cancel": "No, thanks", "shopFinder.alert.locationNotAuthorize.default": "Go to settings", "shopFinder.alert.locationNotAuthorize.message": "%{name} would like to access the location. The location is required to simplify the store search. You can reactivate it via the settings.", "shopFinder.alert.locationNotAuthorize.title": "Deactivate location", "shopFinder.call.shop": "Call", "shopFinder.cellDetailInfo.button.title": "SHOW ROUTE", "shopFinder.cellDetailInfo.header.text": "ADDRESS AND CONTACT", "shopFinder.closed": "Closed", "shopFinder.detail.title.accessibility": "Store information", "shopFinder.details.storeType.company": "%{company} Store", "shopFinder.details.storeType.partner": "Partner store", "shopFinder.friday": "Friday", "shopFinder.listView.accessibility": "List view", "shopFinder.loading.accessibility": "Is loading", "shopFinder.map.accessibility": "Map with store locations", "shopFinder.map.detailsHint.accessibility": "Double tap to see more details", "shopFinder.map.locationIcon.accessibility": "Your current location", "shopFinder.map.zoomIn.accessibility": "Zoom in map", "shopFinder.map.zoomLevel.accessibility": "Zoom level: %{amount}", "shopFinder.map.zoomOut.accessibility": "Zoom out map", "shopFinder.mapView.accessibility": "Map view", "shopFinder.monday": "Monday", "shopFinder.more.information": "Additional Information", "shopFinder.openHours.accessibility": "%{from} to %{to} o'clock", "shopFinder.opening.hours": "Opening Hours", "shopFinder.openingHours.today": "Today", "shopFinder.overview.distance.accessibility": "away", "shopFinder.overview.floatingButtonList.accessibility": "Sort stores by user location", "shopFinder.overview.floatingButtonMap.accessibility": "Focus user on map", "shopFinder.overview.storesList.accessibility": "Stores list", "shopFinder.overview.storesSuggestionsItem.accessibility": "Store suggestion", "shopFinder.overview.storesSuggestionsList.accessibility": "Store suggestions list", "shopFinder.saturday": "Saturday", "shopFinder.search.hint": "Zip code / town or address", "shopFinder.searchHint.accessibility": "Postleitzahl / Ort oder Adresse", "shopFinder.shopLocationOnMap": "Show route", "shopFinder.showMapDialog.actionGoogleMaps": "Open Google Maps", "shopFinder.showMapDialog.actionOk": "Show", "shopFinder.showMapDialog.actionOk.ios": "Open cards", "shopFinder.showMapDialog.message": "Open Maps to show directions", "shopFinder.showMapDialog.message.ios": "With which card app do you want to display the route?", "shopFinder.showMore.button": "Show more", "shopFinder.showMore.countInfo": "%{current} of %{total} stores", "shopFinder.sunday": "Sunday", "shopFinder.switchToList": "List", "shopFinder.switchToMap": "Map", "shopFinder.thursday": "Thursday", "shopFinder.title": "Filialfinder", "shopFinder.tuesday": "Tuesday", "shopFinder.wednesday": "Wednesday", "tabBar.badge.accessibility": "Article", "tabBar.loggedIn.accessibility": "Logged in", "tabBar.tabItem.accessibility.plural": "articles", "tabBar.tabItem.accessibility.singular": "article", "toast.hint.accessibility.BON.android": "Toast message will be shown", "toast.hint.accessibility.BPX.android": "Toast message will be shown", "toast.hint.accessibility.CRE.android": "Toast message will be shown", "toast.hint.accessibility.HEI.android": "Toast message will be shown", "toast.hint.accessibility.LAS.android": "Toast message will be shown", "toast.hint.accessibility.MAN.android": "Toast message will be shown", "toast.hint.accessibility.SAN.android": "Toast message will be shown", "toast.hint.accessibility.SHE.android": "Toast message will be shown", "toast.hint.accessibility.WIT.android": "Toast message will be shown", "toast.hint.accessibility.YLFLNL.android": "Toast message will be shown", "toast.hint.accessibility.YLFLSE.android": "Toast message will be shown", "toast.hint.accessibility.android": "Toast message will be shown", "toolbar.catalogScanner": "Catalog Scanner", "toolbar.inbox": "Inbox", "toolbar.search": "Search", "toolbar.sharing": "Share", "videoPlayer.playButton.accessibility": "Tap to start the video"}