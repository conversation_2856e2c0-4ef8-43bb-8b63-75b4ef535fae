# OGNative

How to use the native SDK.

## Obtain the `OGNative` instance

```kotlin
// Android
val ogNative = OGAppKitSdk.init(androidApplication()).native()
```

```swift
// Swift
let ogNative = OGAppKitSdk.shared.native()
```

## Configure it

In order to configure the Native SDK to use the correct APIs, the client app needs to set some common values as well as
some company-specific ones. This is done by calling the `configure` method on the `OGNative` instance at app startup.
Right now the supported companies are Lascana and Witt (this includes all sub-brands of the Witt Group).

```kotlin
ogNative.configure(
    when {
        featureConfig.lascana != null -> OGNativeConfig.Lascana(
            graphQLBackend = OGNativeConfig.Backend(
                url = this.graphQLApiUrl,
                basicAuthUser = basicAuthUser,
                basicAuthPassword = basicAuthPassword
            ),
            restBackend = if (basicAuthUser != null && basicAuthPassword != null) {
                OGNativeConfig.Backend(
                    url = this.lascana.restApiUrl,
                    basicAuthUser = basicAuthUser,
                    basicAuthPassword = basicAuthPassword
                )
            } else {
                OGNativeConfig.Backend(url = this.lascana.restApiUrl)
            },
            productIdRegex = this.productIdRegex,
            dynamicYield = OGNativeConfig.DynamicYield(
                trackPageViewUrl = "https://direct.dy-api.eu/v2/serve/user/choose",
                apiKey = "dy_api_key",
                cookiesUrl = "https://www.lascana.de",
            ),
            cookiesBridge = cookiesBridge,
            debug = buildConfiguration.isBetaVersion,
        )

        featureConfig.witt != null -> OGNativeConfig.Witt(
            graphQLBackend = OGNativeConfig.Backend(
                url = this.graphQLApiUrl,
                basicAuthUser = basicAuthUser,
                basicAuthPassword = basicAuthPassword
            ),
            productIdRegex = this.productIdRegex,
            cookiesBridge = cookiesBridge,
            debug = buildConfiguration.isBetaVersion,
            locale = this.witt.locale,
            cookies = this.witt.cookies.let { cookies ->
                OGNativeConfig.Witt.Cookies(
                    url = cookies.cookiesUrl,
                    tokenCookieName = cookies.cookieName,
                )
            },
            webShopBaseUrl = baseUrlConfig.web,
            displayPaybackPoints = witt.displayPaybackPoints,
        )

        else -> OGNativeConfig.None()
    }
)
```

Where:

- Common fields:
  - `graphQLBackend` is the backend, containing the full URL to the GraphQL endpoint. Typically, this includes the
    `/graphql` path. Optional arguments are a map of `headers` or, as a convenience, a username and password for Basic
    authentication.
  - `productIdRegex` is a regex used to parse product ID(s) from a web shop URL. This regex MUST contain a capture group
      named `productId` and MAY contain a capture group named `variantId`.
  - `cookiesBridge` is an optional implementation of the `CookiesBridge` interface providing bidirectional access to the
      app's web cookies.
  - `debug` is a boolean indicating whether the SDK should run in debug mode, which enables additional logging.

- Lascana-specific fields:
  - `restBackend` is an optional additional backend for company APIs. The exact URL with the precise path to include
    depends on company-specific integration details.
  - `dynamicYield` is configuration for the Dynamic Yield tracking service. Must provide an API key and URL for cookie
    synchronization and may override the backend URLs.
- Witt-specific fields:
  - `locale` is the locale used by the Witt API to return data for the correct tenant., e.g. `de_DE`.
  - `cookies` provides information about how to retrieve specific cookies used to authenticate for various APIs, such as
     basket, wishlist and recommendations.
  - `webShopBaseUrl` is the base URL of the Witt web shop.
  - `displayPaybackPoints` is a boolean indicating whether the app should display Payback points in the UI.

If no company is configured, the `OGNativeConfig.None()` is used, which serves as a default value before configuration
as well as on tests.

## Request data

The SDK typically does not return plain data. Data that might be unavailable is wrapped in a `Result` type, which has
two possible outcomes: `Success`, which offers the data under its `value` property, or `Failure`, which offers the error
under its `failure` property.

> 🍏 Swift note: To work well with the `Result` type, it should be converted to a true Swift enum by using
> `onEnum(of: result)` (see examples below).

Some data is not available as single values but as a sequence of values over time. This data is exposed as a `Flow` in
Kotlin, which is translated to an `AsyncSequence` in Swift. Within these flows, each value is wrapped in a `Result`, as
it might be the case that an initial request fails, but a later retry or an update of cached data offers a value.

Functions that return a Flow/AsyncSequence are not asynchronous and return immediately, because the returned data
structure itself handles the asynchronicity. Functions that return only a single value typically suspend (are
async).

### A screen (on the example of the product detail screen)

An app can request a complete screen that will be constructed and filled with the required data, ready for the UI to
display.

A `Screen` is made up of a list of `Component`s. A `Component` is configured using a corresponding `ComponentConfig`.
When requesting a screen, an app must pass configuration for each component it wants to display in the correct order so
that the SDK can create the screen appropriately. Components are associated with specific screens they may appear on.
E.g. it would probably not make sense to display the size selection component on the product list screen. Configuration
of a screen is passed when requesting the screen itself, not during the global configuration step described above. It
may be passed either as typed data using the Kotlin data models or as a JSON representation, in which case the native
SDK handles deserialization.

Each screen's components are a sealed type, so it is possible to exhaustively handle them with compiler support using
Kotlin's `when` and Swift's `switch` (again: requiring the `onEnum(of:)` wrapper).

> 🍏 Swift note: the option to pass raw JSON mainly exists because the Kotlin data models cannot implement the Swift
> protocol `Codable`, which makes type-safe deserialization in Swift a bit complicated.

> ℹ️ Passing invalid JSON data is handled as gracefully as possible. Invalid JSON will result in a `Failure`. Invalid
> data within an individual component configuration will result in that component being dropped and an error being
> logged. Incomplete data will result in defaults being used, if available.

```json
{
  "components": [
    {
      "name": "ProductGallery"
    },
    {
      "name": "ProductTitle"
    },
    {
      "name": "ProductRating"
    },
    {
      "name": "ProductPrice"
    }
  ]
}
```

```kotlin
// Kotlin
val screenResultFlow = ogNative.getProductDetailScreen(
    id = "123123",
    componentConfigsJson = json
)
screenResultFlow.collect { screen ->
    when (screen) {
        is Result.Success -> {
            for (c in screen.value.components) {
                when (c) {
                    is ProductGallery -> TODO()
                    is ProductPrice -> TODO()
                    is ProductRating -> TODO()
                    is ProductTitle -> TODO()
                }
            }
        }

        is Result.Failure -> TODO()
    }
}
```

```swift
// Swift
let screen = native.getProductDetailScreen(
    id: "123123",
    componentConfigsJson: json
)
for await screenResult in screen {
    switch onEnum(of: screenResult) {
    case .failure(_): break
    case .success(let screen):
        for c in screen.value.components {
            switch onEnum(of: c) {
            case .productGallery(_):
                doSomething()
            case .productPrice(_):
                doSomething()
            case .productRating(_):
                doSomething()
            case .productTitle(_):
                doSomething()
            }
        }
    }
}
```

Components may implement the interface `LoadingComponent` to indicate that they have an individual loading state (beside
the overall loading of the whole screen). In this case, they hold a `state` property that can be one of `Loading` or
`Done`. The state itself contains the relevant component content data object. While the component is in `Loading` state,
this data might be incomplete or mere placeholder data.

All components can be found in the `com.ottogroup.appkit.nativeui.model.ui` package.

## Product Detail Screen

### Product Detail Screen Components

This is a list of the available component configurations as they may be passed to the `getProductDetailScreen` methods.
This list is work in progress and component definitions may change over time.

Each component at least has a `name` property and may possess additional properties to configure it. Typically, the SDK
returns a component object for each configuration object passed in. Each component object contains all required data to
display it as well as the config object that was used to create it, for convenience.

Every component of the product detail screen is a `LoadingComponent`. When requested, the screen is immediately returned
with the configured components all in `Loading` state and filled with placeholder data, so that the clients can display
a skeleton loading UI resembling the final screen layout.

For simplicity, the list below displays only the respective content types of the components without the common wrapper
into a `LoadingComponent`.

---

```json
{
  "name": "ProductHeader"
}
```

```kotlin
class ProductHeader {
    class Content(
        val title: String,
        val brandName: String?,
        val sharingData: SharingData?,
        val isWishlisted: Boolean,
        val productIdForWishlisting: String,
        val productId: String,
    )
    class SharingData(
        val url: String,
    )
}
```

The `ProductHeader` component differs from most other components in that it is not intended to be displayed within the
usual list of components but rather outside of it, in the top app bar. It contains the `productId` of the displayed
product, as a way to pass that info to the client alongside the product name, since there is no other straightforward
way for the client to obtain the ID.

---

```json
{
  "name": "ProductGallery"
}
```

```kotlin
class ProductGallery {
    class Content(
        val images: List<Image>,
        val flags: List<Flag>,
        val isWishlisted: Boolean,
        val productIdForWishlisting: String,
    )
}
```

---

```json
{
  "name": "ProductColorDimension"
}
```

```kotlin
class ProductColorDimension {
    class Content(
        val colorName: String,
        val colors: List<ColorLink>,
    )
}
```

If the requested product only comes in a single color, then this component is transparently left out of the screen
response. It is meant as an either/or with the `ProductColor` component.

---

```json5
{
  "name": "ProductColor"
}
```

```kotlin
class ProductColor {
    class Content(
        val colorName: String
    )
}
```

If the requested product comes in multiple colors, then this component is transparently left out of the screen response.
It is meant as an either/or with the `ProductColorDimension` component.

---

```json5
{
  "name": "ProductDimensions",
  // Required. Valid values are "flat", "flatChips", "nested", "categorized".
  "style": "nested",
  // Optional. Style will be applied if the text value of a dimension is longer than the characterLimit.
  "longTitleStyle": {
    "characterLimit": 12,
    "style": "categorized"
  }
}
```

```kotlin
class ProductDimensions {
    // ...
}
```

The returned `content` is a sealed type. It can either be a `FlatDimension`, which wraps a single dimension to be
displayed in a simple list, or a `NestedDimensions`, in which an outer dimension contains multiple entries that each
contain a dimension. If the result is a `NestedDimensions`, then the input config (which is also returned again)
determines how the UI is displayed. All the content types come with the wishlist state of the product and the ID to
use for wishlisting. Wishlist functionality comes into play when selecting an unavailable variant.

⚠️ Even if the config requested `nested` or `categorized` style, the result might still be a flat dimension, if the
requested product only has a single size dimension.

⚠️ If the requested product is a gift voucher, then the returned `content` will be a `VoucherDimension`, containing the
voucher values and an indication if the voucher allows customizing the recipient's name + the maximum allowed length of
the name.

If the requested product has no non-color dimension at all, then this component is transparently left out of the screen
response. If the product has only a single variant, then this component is also left out and `ProductVariant` is
returned (if requested).

All dimension contents can optionally contain a URL to link to a size advisor web page.

Example of `NestedDimensions`:

```
NestedDimensions(
    name=Cup,
    entries=[
        Entry(
            name=Cup B,
            dimension=ProductDimension(
                name=Unterbrustweite,
                variants=[
                    VariantLink(
                        name=70,
                        productId=1357715681,
                        availability=Availability(
                            state = IN_STOCK,
                            quantity = 100,
                        ),
                        price=Price(currency=EUR, value=2999, oldValue=null, isStartPrice=false),
                        isSelected=false
                    ),
                    VariantLink(
                        name=75,
                        productId=1357715683,
                        availability=Availability(
                            state = IN_STOCK,
                            quantity = 100,
                        ),
                        price=Price(currency=EUR, value=2999, oldValue=null, isStartPrice=false),
                        isSelected=false
                    ),
                    ...
                ]
            )
        ),
        Entry(
            name=Cup C,
            dimension=ProductDimension(
                name=Unterbrustweite,
                variants=[
                    ...
                ]
            )
        )
    ],
    isWishlisted=false,
    productIdForWishlisting="123123",
    sizeAdvisorUrl="https://shop.de/size-advisor"
)
```

---

```json5
{
  "name": "ProductVariant"
}
```

```kotlin
class ProductVariant {
    class Content(
        val dimensionName: String,
        val variant: VariantLink,
    )
}
```

If the requested product has multiple variants, then this component is transparently left out of the screen response.
It is meant as an either/or with the `ProductDimensions` component.


---

```json5
{
  "name": "ProductInformation",
  // optional URL to navigate to the sustainability info page. Defaults to null.
  "sustainabilityUrl": "https://www.sheego.de/content/verantwortung",
  // optional URL to navigate to the returns info page. Defaults to null.
  "returnUrl": "https://www.sheego.de/content/service/ruecksendung"
}
```

```kotlin
class ProductInformation {
    class Content(
        val description: Description,
        val details: Details,
        val brand: Brand?,
        val importantInformation: ImportantInformation?,
        val articleStandards: ArticleStandards?,
    )
    class Description(
        val articleNumber: String,
        val bulletPoints: List<String>,
        val text: String,
    )
    class Details(
        val attributesTable: Information.AttributesTable,
    )
    class Brand(
        val name: String,
        val description: String?,
    )
    class ImportantInformation(
        val distributingCompanies: List<DistributingCompany>,
    ) {
        class DistributingCompany(
            val name: String,
            val data: String,
        )
    }
}
```

The product information consists of several static section, of which some may be optional. As of now, there is always a
description that contains a customer-facing article number (that may be different from the internal product ID), a list
and a full text. Details are given in form of a table structure into multiple sections. Brand information is optional.
The important information section contains the distributing companies for displaying required legal information.
Products may have optional "article standards", which is a list of (sustainability) seals to be displayed on the PDP.
Seals are gives as enum values, to be mapped to logos and texts in the client app.

---

```json
{
  "name": "ProductTitle"
}
```

```kotlin
class ProductTitle {
    class Content(
        val title: String
    )
}
```

---

```json5
{
  "name": "ProductPrice",
  // optional URL to link to next to the price. Defaults to null.
  "conditionsUrl": "https://shop.de/shipping"
}
```

```kotlin
class ProductPrice {
    class Content(
        val price: Price,
    )
}
```

---

```json5
{
  "name": "ShopUsps",
  // defaults to Int.MAX_VALUE, i.e. bottom of the list
  "paybackPointsIndex": 0
}
```

```kotlin
class ShopUsps {
    class Content(
        val paybackPoints: Int?
    )
}
```

The `ShopUsps` component contains only the Payback Points, if present. The remaining USP content comes from static
assets contained in the client apps. The position at which the Payback points should be inserted into the list of USPs
is configurable and defaults to the end of the list.

---

```json5
{
  "name": "ProductRating",
  // default to true
  "showCount": true,
  // Permitted values: "NONE", "HALF", "WHOLE". Defaults to "HALF".
  "rounding": "HALF",
  // mandatory URL to see all reviews. If it contains the placeholder "{productId}", it will be replaced with the actual product ID.
  "allReviewsUrl": "app://reviews/{productId}",
}
```

```kotlin
class ProductRating {
    class Content(
        val rating: Rating,
        val allReviewsUrl: String,
    )
}
```

The returned config (which is identical to the passed-in one) should be checked to determine if the total count of
ratings given should be displayed and whether the average rating should be rounded, either to a whole or half-step
value, or not at all. Rounding refers only to the display (via e.g. star icons), the returned average rating number is
never actually rounded.

This component is omitted if the product has no ratings.

---

```json5
{
  "name": "ProductAvailability"
}
```

```kotlin
class ProductAvailability {
    class Content(
        val availability: Availability,
    )
}
```

---

```json5
{
  "name": "AddToBasketButton",
  // URL to sign up for notifications when the product is unavailable
  "notifyMeUrl": "/notify"
}
```

```kotlin
class AddToBasketButton {
    sealed interface Content {
        class AddToBasket(val productId: String) : Content
        class NotifyMe(val url: String) : Content
    }
}
```

The button comes with a context of either `AddToBasket` containing the product ID, or, when the product is not
available, as `NotifyMe` with the URL to sign up for notifications.

---

```json5
{
  "name": "ProductReviews",
  // mandatory URL to see all reviews. If it contains the placeholder "{productId}", it will be replaced with the actual product ID.
  "allReviewsUrl": "app://reviews/{productId}",
  // Permitted values: "NONE", "HALF", "WHOLE". Defaults to "HALF".
  "rounding": "HALF",
  // how many reviews to show on the PDP itself. Defaults to 6.
  "reviewCount": 6,
  // optional URL override for writing a review. If it contains the placeholder "{productId}", it will be replaced with the actual product ID. Defaults to data-mapping-based URL.
  "writeReviewUrl": "https://shop.de/reviews/{productId}/write",
  // toggle to show or hide the "Write Review" button in the empty state. Defaults to true.
  "showWriteReviewButton": false
}
```

```kotlin
class ProductReviews {
    sealed interface Content {
        data class Empty(
            val writeReviewUrl: String?,
        ) : Content

        data class Reviews(
            val totalCount: Int,
            val rating: Rating,
            val reviews: List<Review>,
            val allReviewsUrl: String,
        ) : Content
    }
}
```

The `ProductReviews` components has two possible content states. In case there are no reviews available, an empty state
should be displayed that potentially links to the option to write a review. If there are reviews available, the
component displays some initial ones, alongside the overall rating of the product and allows to navigate to see all
reviews.

---

```json5
{
  "name": "StaticProductRecommendations",
  // optional, defaults to hard-coded list of products for UX test
  "productIds": [
    "123",
    "456"
  ],
  // optional, defaults to no limit
  "maxEntries": 5,
  // optional
  "titleL10n": "title_l10n_key",
  // optional
  "subtitleL10n": "subtitle_l10n_key"
}
```

```kotlin
class StaticProductRecommendations(
    override val state: LoadingComponent.State<Content>,
    override val config: Config,
)

class Content(
    val products: List<RecommendedProduct>,
    val image: Image? = null,
)

class RecommendedProduct(
    val productId: String,
    val brandName: String?,
    val title: String,
    val price: Price?,
    val image: Image,
    val isWishlisted: Boolean,
    val productIdForWishlisting: String,
)
```

Contrary to most of the other `LoadingComponents`, the recommendation components might be actually loading data
asynchronously and not just for the purpose of displaying a skeleton loading state.

---

```json5
{
  "name": "RecentlyViewedRecommendations",
  // optional, defaults to no limit
  "maxEntries": 5,
  // optional
  "titleL10n": "title_l10n_key",
  // optional
  "subtitleL10n": "subtitle_l10n_key"
}
```

This component is structurally identical to `StaticProductRecommendations`.

---

```json5
{
  "name": "DynamicYieldRecommendations",
  // optional, defaults to no limit
  "maxEntries": 5,
  // List of Dynamic Yield campaign selectors. Optional, defaults to empty. Set either this or "selectorGroups".
  "selectorNames": [
    "name"
  ],
  // List of Dynamic Yield campaign selector groups. Optional, defaults to empty. Set either this or "selectorNames".
  "selectorGroups": [
    "App"
  ],
  // optional
  "titleL10n": "title_l10n_key",
  // optional
  "subtitleL10n": "subtitle_l10n_key"
}
```

This component is structurally identical to `StaticProductRecommendations`.

---

```json5
{
  "name": "ShopTheLookRecommendations",
  // optional, defaults to no limit
  "maxEntries": 5,
  // optional
  "titleL10n": "title_l10n_key",
  // optional
  "subtitleL10n": "subtitle_l10n_key"
}
```

This component is structurally identical to the other recommendations but has the optional `image` filled out to display
a header image in front of the product entries.

---

```json5
{
  "name": "MoreFromTheSeriesRecommendations",
  // optional, defaults to no limit
  "maxEntries": 5,
  // optional
  "titleL10n": "title_l10n_key",
  // optional
  "subtitleL10n": "subtitle_l10n_key"
}
```

This component is structurally identical to the other recommendations.

---

```json5
{
  "name": "DynamicYieldBanner",
  // List of Dynamic Yield campaign selectors. Optional, defaults to empty. Set either this or "selectorGroups".
  "selectorNames": [
    "name"
  ],
  // List of Dynamic Yield campaign selector groups. Optional, defaults to empty. Set either this or "selectorNames".
  "selectorGroups": [
    "App"
  ],
  // optional ID for the "slot"/position in which the banner is shown. Relevant for tracking.
  "slotId": "App_PDP_Top"
}
```

```kotlin
class DynamicYieldBanner(
    override val state: LoadingComponent.State<Content>,
    override val config: Config,
) : ProductDetailComponent<DynamicYieldBanner.Content> {

    class Content(
        val text: String,
        val infoText: String?,
        val promoCode: String?,
        val trackingEvents: TrackingEvents,
    )

    class TrackingEvents(
        val view: View.ProductDetailPromotion,
        val click: Interaction.ProductDetailSelectPromotion,
    )
}
```

The banner always has a text. Info text to contain more details is optional, as is a promo code that the user can
apply. The banner has a loading state for consistency purposes with other components but will never actually be in an
in-progress state. The banner will only be shown on the screen once it is fully loaded.

The banner content contains the relevant tracking events that the client should trigger when the banner is viewed and
interacted with. Since these events are relatively complex they are fully assembled by the SDK, however they must be
triggered the appropriate times by the client app.

## Modify data

### Basket

Products can be added to the basket like this:

```kotlin
ogNative.getBasket()
ogNative.addProductToBasket(id = "123123")
```

The method is suspending/async and will return a `Result` indicating the success of the operation and returning the
modified basket object that can be used to update a badge count. The ID to be passed is contained in the
`AddToBasketButton` component.

Adding a voucher to the basket might allow passing an optional custom name, depending on the data returned in the
`ProductDimensions.VoucherDimension`:

```kotlin
ogNative.addVoucherToBasket(id = "123123", customName = "Tim Apple")
```

### Wishlist

Products can be added to or removed from the wishlist:

```kotlin
ogNative.getWishlist()
ogNative.addProductToWishlist(id = "123123")
ogNative.removeProductFromWishlist(id = "123123")
```

Each method is suspending/async and will return a `Result` indicating the success of the operation and returning the
modified wishlist object that can be used to update a badge count. Upon a successful modification of the wishlist state,
components that depend on it will be updated automatically. The ID to be passed to these functions is contained in the
components that offer wishlist modification.

## Product Reviews Screen

The Product Reviews screen will contain a set of configurable components to display a specific product's rating data and
user reviews. Each time a reviews screen is created it will have a unique `screenId` that can be used in sorting and
filtering operations. It will be returned in the required components.
To get this screen, add a new `reviewComponents` array with the desired components to the config:

```json5
{
  "reviewComponents": [
    {
      "name": "ReviewsInformation"
    },
    {
      "name": "ReviewsSortingOptions"
    },
    {
      "name": "ReviewsList"
    },
    {
      "name": "WriteReviewButton",
      // optional URL override for writing a review. If it contains the placeholder "{productId}", it will be replaced with the actual product ID. Defaults to data-mapping-based URL.
      "writeReviewUrl": "https://shop.de/reviews/{productId}/write",
    }
  ]
}
```

### Product Reviews Screen Components

This is a list of the available component configurations as they may be passed to the `getProductReviewsScreen` methods.
This list is work in progress and component definitions may change over time.

Each component at least has a `name` property and may possess additional properties to configure it. Typically, the SDK
returns a component object for each configuration object passed in. Each component object contains all required data to
display it as well as the config object that was used to create it, for convenience.

---

```json
{
  "name": "ReviewsInformation"
}
```

```kotlin
class ReviewsInformation(
    val brandName: String?,
    val title: String,
    val rating: Rating?,
    val screenId: String
)
```

---

```json
{
  "name": "ReviewsSortingOptions"
}
```

```kotlin
class ReviewsSortingOptions(
    val options: List<SortingOption>,
    val screenId: String
) {
    public enum class SortingOption {
        RECENT,
        OLDEST,
        HIGHEST,
        LOWEST
    }
}
```

---

```json5
{
  "name": "ReviewsList",
  // optional int value to set the amount of reviews displayed per page. Defaults to 15
  "reviewsPerPage": 15
}
```

```kotlin
class ReviewsList(
    val reviews: List<Review>,
    val totalReviewsCount: Int,
    val screenId: String
)
```

---

```json5
{
  "name": "WriteReviewButton",
  // mandatory URL for writing a review. If it contains the placeholder "{productId}", it will be replaced with the actual product ID.
  "writeReviewUrl": "https://shop.de/reviews/{productId}/write",
}
```

```kotlin
class WriteReviewButton(
    val writeReviewUrl: String
)
```

---

### Data display options

#### Filtering

Reviews can be filtered by rating like this:

```kotlin
ogNative.filterProductReviews(screenId = "9c84e65b-7e4c-48de-87c9-0022eb9ed253", filterRating = "3")
```

To remove the current filter just set `filterRating` as `null`.

#### Sorting

Reviews can be sorted by calling this function:

```kotlin
ogNative.sortProductReviews(screenId = "9c84e65b-7e4c-48de-87c9-0022eb9ed253", sortingOption = { sortingOption })
```

The available sorting options are: `RECENT`, `OLDEST`, `HIGHEST`, `LOWEST`.

#### Pagination

On first load, the number of reviews shown will be the one set in the `reviewsPerPage` field set in the `ReviewsList`
component (or the default 15 if it's not set). More reviews can be loaded by calling this function:

```kotlin
ogNative.showMoreReviews(screenId = "9c84e65b-7e4c-48de-87c9-0022eb9ed253")
```

## Add to basket success screen

The screen to display after successfully adding a product to the basket. The intended flow is that the client app
performs the add-to-basket operation, checks the result and, in case of success, performs a navigation to this success
screen, requesting its components the same way as for any other screen.

Since the product that was just added to basket must have already been requested before and is going to be in the cache,
this screen will return the filled `AddedProduct` component very quickly, without perceptible loading times.

### Add to basket success screen Components

```json5
{
  "name": "AddedProduct"
}
```

```kotlin
class AddedProduct {
    class Content(
        val productId: String,
        val brandName: String?,
        val title: String,
        val selectedDimensionValues: List<SelectedDimensionValue>,
        val price: Price,
        val image: Image,
    ) {
        class SelectedDimensionValue(
            val dimensionName: String,
            val value: String,
        )
    }
}
```

The basic information of the product that was just successfully added to the basket.

---

```json5
{
  "name": "DynamicYieldRecommendations"
  // StaticProductRecommendations, RecentlyViewedRecommendations
  // ...
}
```

The added to basket screen supports the same recommendation components as the product detail screen.

---

```json5
{
  "name": "ContinueShoppingButton",
  // Optional URL to load when clicking the button.
  // If null, the expectation is to simply close the AddToBasketSuccess screen.
  // Defaults to null.
  "continueShoppingUrl": "https://shop.de"
}
```

```kotlin
class ContinueShoppingButton {
    class Content(
        val continueShoppingUrl: String?
    )
}
```

---

```json5
{
  "name": "ShowBasketButton",
  // URL of the basket to load on click
  "basketUrl": "app://basket"
}
```

```kotlin
class ShowBasketButton {
    class Content(
        val basketUrl: String
    )
}
```

