package com.ottogroup.appkit.nativeui.model.domain

public data class Basket(
    val items: List<Item>
) {
    public data class Item(
        val id: String,
        val amount: Int
    )
}

public sealed class BasketError(message: String? = null) : Throwable(message) {
    public class ProductUnavailable() : BasketError()
    public class ItemCountExceeded(message: String) : BasketError(message)
    public class Generic(message: String) : BasketError(message)

    public class GiftCardNameTooLong : BasketError()
    public class GiftCardSameValueAlreadyInBasket : BasketError()
    public class GiftCardAlreadyInBasket : BasketError()
    public class GiftCardProductAlreadyInBasket : BasketError()
}
