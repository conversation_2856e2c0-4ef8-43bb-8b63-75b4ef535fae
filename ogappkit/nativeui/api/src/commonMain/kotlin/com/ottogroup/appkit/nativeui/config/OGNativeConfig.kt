package com.ottogroup.appkit.nativeui.config

import com.ottogroup.appkit.base.http.CookiesBridge
import com.ottogroup.appkit.base.http.NoOpCookiesBridge
import io.ktor.util.encodeBase64
import kotlinx.serialization.Serializable

/**
 * The configuration required to be passed to the
 * [OGNative.configure][com.ottogroup.appkit.nativeui.OGNative.configure]
 * method before using the native SDK
 */
public sealed interface OGNativeConfig {
    /**
     * The backend configuration for GraphQL queries. Its URL typically
     * includes the `/graphql` path.
     */
    public val graphQLBackend: Backend

    /**
     * Regex used to parse product ID(s) from a web shop URL. This regex MUST
     * contain a capture group named `productId` and MAY contain a capture
     * group named `variantId`.
     */
    public val productIdRegex: String

    /**
     * A bridge for interfacing with the app's cookies. The SDK will read
     * values as required for API requests and update cookies that come as part
     * of API responses.
     */
    public val cookiesBridge: CookiesBridge

    /**
     * When set enables additional logging.
     */
    public val debug: Boolean

    /**
     * A dummy configuration to serve as a default value before configuration
     * as well as on tests.
     */
    public data class None(
        override val graphQLBackend: Backend = Backend(""),
        override val productIdRegex: String = "",
        override val cookiesBridge: CookiesBridge = NoOpCookiesBridge,
        override val debug: Boolean = false
    ) : OGNativeConfig

    /**
     * Configuration for the Lascana tenant.
     */
    @Serializable
    public data class Lascana(
        override val graphQLBackend: Backend,
        override val productIdRegex: String,
        override val cookiesBridge: CookiesBridge,
        /**
         * The additional REST backend used by Lascana for wishlist and basket.
         */
        val restBackend: Backend,
        /**
         * The Dynamic Yield service configuration used by Lascana for
         * recommendations.
         */
        val dynamicYield: DynamicYield,
        override val debug: Boolean = false,
    ) : OGNativeConfig {
        /**
         * Configuration options for the Dynamic Yield feature components.
         *
         * @param trackPageViewUrl The API endpoint used for tracking page views
         *    and selecting campaigns.
         * @param apiKey The client-side API key for Dynamic Yield APIs.
         * @param cookiesUrl The URL to use for cookies set by Dynamic Yield.
         *    Typically, this would be the web shop's base URL, including the https
         *    scheme.
         */
        @Serializable
        public data class DynamicYield(
            val trackPageViewUrl: String = "https://direct.dy-api.eu/v2/serve/user/choose",
            val apiKey: String,
            val cookiesUrl: String,
        )
    }

    /**
     * Configuration for the Witt group of tenants.
     */
    @Serializable
    public data class Witt(
        override val graphQLBackend: Backend,
        override val productIdRegex: String,
        override val cookiesBridge: CookiesBridge,
        /**
         * The current app locale, used by the Witt API to return data for the
         * correct tenant.
         */
        val locale: String?,
        /**
         * Information about how to retrieve specific cookies used to authenticate
         * for various APIs, such as basket, wishlist and recommendations.
         */
        val cookies: Cookies,
        val webShopBaseUrl: String,
        val displayPaybackPoints: Boolean,
        override val debug: Boolean = false,
    ) : OGNativeConfig {
        @Serializable
        public data class Cookies(
            val url: String,
            val tokenCookieName: String,
            val recoSessionIdCookieName: String = "recoSessionId",
        )
    }

    @Serializable
    public data class Backend(
        val url: String,
        val headers: Map<String, String> = mapOf(),
    ) {
        public constructor(url: String, basicAuthUser: String, basicAuthPassword: String) : this(
            url,
            mapOf(
                "Authorization" to "Basic " + "$basicAuthUser:$basicAuthPassword".encodeBase64()
            )
        )
    }
}

public val OGNativeConfig.Witt.safeLocale: String get() = locale ?: "de-DE"
