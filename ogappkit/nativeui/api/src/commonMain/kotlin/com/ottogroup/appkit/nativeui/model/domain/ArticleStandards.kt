package com.ottogroup.appkit.nativeui.model.domain

/**
 * Article standards (formerly called sustainability) are seals that
 * certify that a product has certain properties. Not every product has
 * them. A [Seal] is always associated with exactly one [Category]. A
 * product can have multiple seals, theoretically also belonging to the
 * same category.
 *
 * The SDK only contains an enumeration of all supported seals. Matching a
 * seal with its drawable asset and user-facing text is the responsibility
 * of the client apps, making use of the design and l10n systems.
 *
 * For Witt apps, the API returns a list of urls that contain the seal images.
 * Two variants are provided: [StructuredSeals] for apps with structured seal
 * information, and [SealUrls] for apps that only have image URLs.
 */
public sealed interface ArticleStandards {
    /**
     * Article standards with structured seal information.
     * Used when the app has access to detailed seal metadata, i.e. Lascana.
     */
    public data class StructuredSeals(
        val seals: List<Seal>,
    ) : ArticleStandards

    /**
     * Article standards with only seal image URLs.
     * Used by Witt apps where the API only provides image URLs.
     */
    public data class SealUrls(
        val urls: List<String>,
        val informationLink: String? = null,
    ) : ArticleStandards

    public enum class Category {
        OrganicMaterials,
        RecycledMaterials,
        AnimalWelfare,
        ResponsiblySourcedMaterials,
        ImprovedProduction,
        SupportingSocialInitiatives,
    }

    public enum class Seal(
        public val category: Category,
        /**
         * German official name of the seal. For internal use, not to be displayed
         * to the user.
         */
        public val nameDE: String,
    ) {
        GotsOrganic(Category.OrganicMaterials, "Global Organic Textile Standard organic"),
        Ocs100(Category.OrganicMaterials, "Organic Content Standard (OCS) 100"),
        OcsBlended(Category.OrganicMaterials, "Organic Content Standard (OCS) blended"),
        Ivn(Category.OrganicMaterials, "NATURTEXTIL IVN zertifiziert BEST"),
        BioRe(Category.OrganicMaterials, "bioRe Sustainable Textiles"),
        OekoTex(Category.OrganicMaterials, "OEKO-TEX® ORGANIC COTTON"),
        OrganicCotton(Category.OrganicMaterials, "Bio-Baumwolle"),
        GotsMadeWithOrganic(Category.OrganicMaterials, "Global Organic Textile Standard made with organic materials"),

        Grs(Category.RecycledMaterials, "Global Recycled Standard"),
        Rcs100(Category.RecycledMaterials, "Recycled Claim Standard (RCS) 100"),
        RcsBlended(Category.RecycledMaterials, "Recycled Claim Standard (RCS) blended"),
        Repreve(Category.RecycledMaterials, "REPREVE® Unifi, Inc."),
        Econyl(Category.RecycledMaterials, "ECONYL®"),
        Seaqual(Category.RecycledMaterials, "SEAQUAL™"),
        Refibra(Category.RecycledMaterials, "TENCEL™ mit REFIBRA™ Technologie"),
        RecycledMaterial(Category.RecycledMaterials, "Recyceltes Material"),

        ResponsibleDown(Category.AnimalWelfare, "Responsible Down Standard"),
        ResponsibleWool(Category.AnimalWelfare, "Responsible Wool Standard"),
        Downpass(Category.AnimalWelfare, "DOWNPASS"),
        GoodCashmere(Category.AnimalWelfare, "Good Cashmere Standard®"),

        Ecovero(Category.ResponsiblySourcedMaterials, "LENZING™ ECOVERO™"),
        Lyocell(Category.ResponsiblySourcedMaterials, "TENCEL™ Lyocell"),
        Spinnova(Category.ResponsiblySourcedMaterials, "Spinnova"),
        Modal(Category.ResponsiblySourcedMaterials, "TENCEL™ Modal"),
        SustainableViscose(Category.ResponsiblySourcedMaterials, "Viskose aus verbesserter Rohstoffbeschaffung"),

        MadeInGreen(Category.ImprovedProduction, "MADE IN GREEN by OEKO-TEX®"),
        BluesignProduct(Category.ImprovedProduction, "bluesign® PRODUCT"),
        GruenerKnopf(Category.ImprovedProduction, "Grüner Knopf"),
        LeatherWorkingGroup(Category.ImprovedProduction, "Leather Working Group"),
        EUEcolabel(Category.ImprovedProduction, "EU Ecolabel"),
        BlauerEngel(Category.ImprovedProduction, "Der Blaue Engel"),
        NordicSwan(Category.ImprovedProduction, "Nordic Swan Ecolabel"),

        CottonMadeInAfrica(Category.SupportingSocialInitiatives, "Unterstützt Cotton made in Africa"),
        FairtradeCotton(Category.SupportingSocialInitiatives, "Fairtrade Cotton");

        public companion object {
            public val byNameDE: Map<String, Seal> = Seal.entries.associateBy { it.nameDE }
        }
    }
}
