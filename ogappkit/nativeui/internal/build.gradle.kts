plugins {
    id("ogAppKit.libraryModule")
    alias(libs.plugins.apollo)
}

kotlin {
    sourceSets {
        commonMain.dependencies {
            api(projects.ogappkit.nativeui.api)

            implementation(projects.ogappkit.tracking.api)

            implementation(libs.kotlinx.datetime)
            implementation(libs.ktor.client.core)
            implementation(libs.ktor.client.content.negotiation)
            implementation(libs.ktor.client.logging)
            implementation(libs.ktor.serialization.kotlin.json)
            implementation(libs.stately.concurrentCollections)
            implementation(libs.apollo.runtime)
            implementation(libs.apollo.normalizedCache)
            implementation(libs.apollo.adapters.kotlinx.datetime)
            implementation(libs.ksoup)
        }
        commonTest.dependencies {
            implementation(libs.stately.concurrency)
            implementation(libs.apollo.mockserver)
            implementation(libs.ktor.client.mock)
        }
    }
}

apollo {
    data class Service(
        val name: String,
        val endpointUrl: String,
        val extraConfig: com.apollographql.apollo.gradle.api.Service.() -> Unit = {},
    )

    val services = listOf(
        Service("lascana", "https://stage-shop.lsc1.ber.basefarm.net/graphql/", extraConfig = {
            mapScalar(
                "DateTime",
                "kotlinx.datetime.Instant",
                "com.apollographql.adapter.datetime.KotlinxInstantAdapter"
            )
        }),
        Service("witt", "https://cdn.wcc.witt-weiden.de/graphql"),
        Service("wittContentful", "https://graphql.contentful.com/content/v1/spaces/joape0egh3h4"),
    )
    // pull schema: ./gradlew download${service.name.uppercaseFirstLetter}ApolloSchemaFromIntrospection
    services.forEach { service ->
        service(service.name) {
            packageName.set("com.ottogroup.appkit.nativeui.${service.name}")
            generateAsInternal.set(true)
            generateDataBuilders.set(true)
            sourceFolder.set("com/ottogroup/appkit/nativeui/${service.name}")
            introspection {
                endpointUrl.set(service.endpointUrl)
                schemaFile.set(file("src/commonMain/graphql/com/ottogroup/appkit/nativeui/${service.name}/schema.graphqls"))
            }
            addTypename.set("always") // required for caching
            service.extraConfig(this)
        }
    }
}
