package com.ottogroup.appkit.nativeui.api.witt.utils

import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.api.Mutation
import com.apollographql.apollo.api.Query
import com.apollographql.apollo.cache.normalized.FetchPolicy
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.api.ApolloRepository
import com.ottogroup.appkit.nativeui.witt.AddItemToBasketMutation
import com.ottogroup.appkit.nativeui.witt.AddItemToWishlistMutation
import com.ottogroup.appkit.nativeui.witt.GetBasketQuery
import com.ottogroup.appkit.nativeui.witt.GetEuImportersQuery
import com.ottogroup.appkit.nativeui.witt.GetWishlistQuery
import com.ottogroup.appkit.nativeui.witt.ProductDetailQuery
import com.ottogroup.appkit.nativeui.witt.ProductReviewsQuery
import com.ottogroup.appkit.nativeui.witt.RemoveItemFromWishlistMutation
import com.ottogroup.appkit.test.TestCallVerifiable
import com.ottogroup.appkit.test.TestCallVerifier
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow

internal class FakeApolloRepository : ApolloRepository, TestCallVerifiable by TestCallVerifier() {
    var queryResult: Result<Query.Data>? = null
    private var mutationResult: Result<Mutation.Data>? = null
    private var capturedQuery: Query<*>? = null
    private var capturedMutation: Mutation<*>? = null
    private var capturedHeaders: Map<String, String>? = null

    private var queryResponses: Map<String, ProductDetailQuery.Data> = emptyMap()
    private var euImporterResponses: Map<String, GetEuImportersQuery.Data> = emptyMap()
    private var reviewResponses: Map<String, ProductReviewsQuery.Data> = emptyMap()

    fun setupGetBasketResponse(basketItems: List<BasketItemData>) {
        val basketData = GetBasketQuery.Basket(
            __typename = "Basket",
            items = basketItems.map { item ->
                GetBasketQuery.Item(
                    __typename = "BasketItem",
                    id = item.id,
                    quantity = item.quantity
                )
            }
        )
        val queryData = GetBasketQuery.Data(basket = basketData)
        queryResult = Result.Success(queryData)
    }

    fun setupAddToBasketResponse(basketItems: List<BasketItemData>) {
        val basketData = AddItemToBasketMutation.AddItemsToBasket(
            __typename = "AddItemsToBasket",
            items = basketItems.map { item ->
                AddItemToBasketMutation.Item(
                    __typename = "BasketItem",
                    id = item.id,
                    quantity = item.quantity
                )
            }
        )
        val mutationData = AddItemToBasketMutation.Data(addItemsToBasket = basketData)
        mutationResult = Result.Success(mutationData)
    }

    fun setupGetWishlistResponse(wishlistItems: List<WishlistItemData>) {
        val queryData = GetWishlistQuery.Data(
            wishlist = wishlistItems.map { item ->
                GetWishlistQuery.Wishlist(
                    __typename = "WishlistItem",
                    id = item.id,
                    product = GetWishlistQuery.Product(
                        __typename = "Product",
                        id = item.productId
                    )
                )
            }
        )
        queryResult = Result.Success(queryData)
    }

    fun setupAddToWishlistResponse() {
        val mutationData = AddItemToWishlistMutation.Data(
            addWishlistItem = AddItemToWishlistMutation.AddWishlistItem(
                __typename = "AddWishlistItem",
                id = "new-wishlist-item-id"
            )
        )
        mutationResult = Result.Success(mutationData)
    }

    fun setupRemoveFromWishlistResponse() {
        val mutationData = RemoveItemFromWishlistMutation.Data(
            removeWishlistItem = "success"
        )
        mutationResult = Result.Success(mutationData)
    }

    fun setupWishlistAddSequence(
        updatedWishlist: List<WishlistItemData>
    ) {
        setupAddToWishlistResponse()
        setupGetWishlistResponse(updatedWishlist)
    }

    fun setupWishlistRemoveSequence(
        updatedWishlist: List<WishlistItemData>
    ) {
        setupRemoveFromWishlistResponse()
        setupGetWishlistResponse(updatedWishlist)
    }

    fun setupQueryFailure(error: Throwable) {
        queryResult = Result.Failure<Query.Data>(error)
    }

    fun setupMutationFailure(error: Throwable) {
        mutationResult = Result.Failure<Mutation.Data>(error)
    }

    fun setupProductResponses(
        queryResponses: Map<String, ProductDetailQuery.Data>,
        euImporterResponses: Map<String, GetEuImportersQuery.Data> = emptyMap(),
        reviewResponses: Map<String, ProductReviewsQuery.Data> = emptyMap()
    ) {
        this.queryResponses = queryResponses
        this.euImporterResponses = euImporterResponses
        this.reviewResponses = reviewResponses
    }

    override suspend fun <T : Query.Data> query(
        query: Query<T>,
        httpHeaders: Map<String, String>
    ): Result<T> {
        recordCall("query", query)
        capturedQuery = query
        capturedHeaders = httpHeaders

        @Suppress("UNCHECKED_CAST")
        return queryResult as Result<T>
    }

    override suspend fun <T : Mutation.Data> mutate(
        mutation: Mutation<T>,
        httpHeaders: Map<String, String>
    ): Result<T> {
        recordCall("mutate", mutation)
        capturedMutation = mutation
        capturedHeaders = httpHeaders
        @Suppress("UNCHECKED_CAST")
        return mutationResult as Result<T>
    }

    override suspend fun <T : Query.Data> updateQuery(
        query: Query<T>,
        httpHeaders: Map<String, String>
    ) = error("Not implemented")

    override fun <T : Query.Data> queryWithCache(
        query: Query<T>,
        fetchPolicy: FetchPolicy,
        httpHeaders: Map<String, String>
    ): Flow<Result<T>> {
        recordCall("queryWithCache", query, fetchPolicy)

        val data = when (query) {
            is ProductDetailQuery -> {
                queryResponses[query.productID] ?: error("Unsupported product id: ${query.productID}")
            }
            is GetEuImportersQuery -> {
                euImporterResponses.values.firstOrNull() ?: error("No EU importer responses configured")
            }
            is ProductReviewsQuery -> {
                reviewResponses[query.productID] ?: error("Unsupported product id: ${query.productID}")
            }
            else -> throw IllegalArgumentException("Unsupported query: $query")
        }

        @Suppress("UNCHECKED_CAST")
        return MutableStateFlow(Result.Success(data as T))
    }

    override fun <T : Query.Data> queryWithCacheUsingClient(
        query: Query<T>,
        apolloClient: ApolloClient,
        fetchPolicy: FetchPolicy,
        httpHeaders: Map<String, String>
    ): Flow<Result<T>> {
        return queryWithCache(query, fetchPolicy, httpHeaders)
    }
}

internal data class BasketItemData(
    val id: String,
    val quantity: Int
)

internal data class WishlistItemData(
    val id: String,
    val productId: String
)
