package com.ottogroup.appkit.test

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.nativeui.api.PromoBannerRepository
import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldPromoBanner
import com.ottogroup.appkit.nativeui.model.ui.PromoBanner
import com.ottogroup.appkit.tracking.event.ECommerceItem
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow

internal class FakePromoBannerRepository : PromoBannerRepository {
    val dynamicYieldBanner: MutableSharedFlow<Result<PromoBanner.Content>> =
        MutableSharedFlow(replay = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)

    override fun getDynamicYieldBanner(
        productSku: String,
        eCommerceItem: ECommerceItem,
        config: DynamicYieldPromoBanner.Config
    ): Flow<Operation<PromoBanner.Content>> {
        return dynamicYieldBanner.asOperation()
    }

    val contentfulBanner: MutableSharedFlow<Result<PromoBanner.Content>> =
        MutableSharedFlow(replay = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)

    override fun getContentfulBanner(): Flow<Operation<PromoBanner.Content>> {
        return contentfulBanner.asOperation()
    }
}
