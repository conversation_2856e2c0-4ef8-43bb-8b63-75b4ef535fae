package com.ottogroup.appkit.nativeui.creator.productdetail

import app.cash.turbine.test
import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping
import com.ottogroup.appkit.nativeui.api.lascana.data.VariantData
import com.ottogroup.appkit.nativeui.creator.Placeholders
import com.ottogroup.appkit.nativeui.model.domain.Rating
import com.ottogroup.appkit.nativeui.model.domain.Review
import com.ottogroup.appkit.nativeui.model.domain.Reviews
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.LoadingComponent
import com.ottogroup.appkit.nativeui.model.ui.ProductDetailComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.ProductRating
import com.ottogroup.appkit.nativeui.model.ui.ProductReviews
import com.ottogroup.appkit.nativeui.util.safeParentId
import com.ottogroup.appkit.test.createTestProduct
import kotlin.test.Test
import kotlin.test.assertEquals

class ProductRatingsReviewsTests {

    @Test
    fun `ProductRating component is omitted for products without ratings or reviews`() = runProductDetailScreenCreatorTest { testObjects ->
        val config = ProductRating.Config(
            allReviewsUrl = "https://example.com/reviews/{productId}/"
        )
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(config)
        )

        val product = createTestProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL),
            variantData = listOf(VariantData(listOf("Gold")))
        ).copy(
            reviews = Reviews(
                rating = null,
                reviews = emptyList(),
                writeReviewUrl = "",
            )
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(product),
            configs,
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingStateRating(config),
                awaitItem()
            )

            assertEquals(
                successfulDetailScreenOf(),
                awaitItem()
            )
        }
    }

    @Test
    fun `ProductRating returns rating content when there are ratings`() = runProductDetailScreenCreatorTest { testObjects ->
        val config = ProductRating.Config(
            allReviewsUrl = "https://example.com/reviews/{productId}/"
        )
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(config)
        )

        val product = createTestProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL),
            variantData = listOf(VariantData(listOf("Gold")))
        ).copy(
            reviews = Reviews(
                rating = Rating(
                    averageRating = 3f,
                    count = 3,
                    ratingDistribution = emptyMap(),
                ),
                reviews = listOf(
                    Review(
                        text = "Bad product",
                        rating = 2,
                    ),
                    Review(
                        text = "Good product",
                        rating = 4,
                    ),
                    Review(
                        text = "Mid product",
                        rating = 3,
                    ),
                ),
                writeReviewUrl = "",
            )
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(product),
            configs,
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingStateRating(config),
                awaitItem()
            )

            assertEquals(
                successfulDetailScreenOf(
                    ProductRating(
                        state = LoadingComponent.State.Done(
                            ProductRating.Content(
                                rating = Rating(
                                    averageRating = 3f,
                                    count = 3,
                                    ratingDistribution = emptyMap(),
                                ),
                                allReviewsUrl = "https://example.com/reviews/Gold/",
                            ),
                        ),
                        config = config
                    )
                ),
                awaitItem()
            )
        }
    }

    @Test
    fun `ProductReviews returns empty content when there are no reviews`() = runProductDetailScreenCreatorTest { testObjects ->
        val config = ProductReviews.Config(
            allReviewsUrl = "https://example.com/reviews/{productId}/",
        )
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(config)
        )

        val product = createTestProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL),
            variantData = listOf(VariantData(listOf("Gold")))
        ).copy(
            reviews = Reviews(
                rating = null,
                reviews = emptyList(),
                writeReviewUrl = "/write-review",
            )
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(product),
            configs,
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingStateReviews(config),
                awaitItem()
            )

            assertEquals(
                successfulDetailScreenOf(
                    ProductReviews(
                        state = LoadingComponent.State.Done(
                            content = ProductReviews.Content.Empty(
                                writeReviewUrl = "/write-review",
                            )
                        ),
                        config = config,
                    )
                ),
                awaitItem()
            )
        }
    }

    @Test
    fun `ProductReviews empty content write review URL is overridden by config`() = runProductDetailScreenCreatorTest { testObjects ->
        val config = ProductReviews.Config(
            allReviewsUrl = "https://example.com/reviews/{productId}/",
            writeReviewUrl = "/write-review-v2/{productId}",
        )
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(config)
        )

        val product = createTestProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL),
            variantData = listOf(VariantData(listOf("Gold")))
        ).copy(
            reviews = Reviews(
                rating = null,
                reviews = emptyList(),
                writeReviewUrl = "/write-review",
            )
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(product),
            configs,
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                successfulDetailScreenOf(
                    ProductReviews(
                        state = LoadingComponent.State.Done(
                            content = ProductReviews.Content.Empty(
                                writeReviewUrl = "/write-review-v2/${product.safeParentId}",
                            )
                        ),
                        config = config,
                    )
                ),
                expectMostRecentItem()
            )
        }
    }

    @Test
    fun `ProductReviews empty content write review URL is removed via config`() = runProductDetailScreenCreatorTest { testObjects ->
        val config = ProductReviews.Config(
            allReviewsUrl = "https://example.com/reviews/{productId}/",
            showWriteReviewButton = false,
        )
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(config)
        )

        val product = createTestProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL),
            variantData = listOf(VariantData(listOf("Gold")))
        ).copy(
            reviews = Reviews(
                rating = null,
                reviews = emptyList(),
                writeReviewUrl = "/write-review",
            )
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(product),
            configs,
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                successfulDetailScreenOf(
                    ProductReviews(
                        state = LoadingComponent.State.Done(
                            content = ProductReviews.Content.Empty(
                                writeReviewUrl = null,
                            )
                        ),
                        config = config,
                    )
                ),
                expectMostRecentItem()
            )
        }
    }

    @Test
    fun `ProductReviews returns empty content when there are ratings but no reviews`() = runProductDetailScreenCreatorTest { testObjects ->
        val config = ProductReviews.Config(
            allReviewsUrl = "https://example.com/reviews/{productId}/",
        )
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(config)
        )

        val product = createTestProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL),
            variantData = listOf(VariantData(listOf("Gold")))
        ).copy(
            reviews = Reviews(
                rating = Rating(
                    averageRating = 3.5f,
                    count = 2,
                    ratingDistribution = emptyMap(),
                ),
                reviews = emptyList(),
                writeReviewUrl = "/write-review",
            )
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(product),
            configs,
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingStateReviews(config),
                awaitItem()
            )

            assertEquals(
                successfulDetailScreenOf(
                    ProductReviews(
                        state = LoadingComponent.State.Done(
                            content = ProductReviews.Content.Empty(
                                writeReviewUrl = "/write-review",
                            )
                        ),
                        config = config,
                    )
                ),
                awaitItem()
            )
        }
    }

    @Test
    fun `ProductReviews returns review content when there are reviews`() = runProductDetailScreenCreatorTest { testObjects ->
        val config = ProductReviews.Config(
            allReviewsUrl = "https://example.com/reviews/{productId}/",
            reviewCount = 2,
        )
        val configs: ComponentConfigs<ProductDetailComponentConfig> = ComponentConfigs(
            listOf(config)
        )

        val product = createTestProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL),
            variantData = listOf(VariantData(listOf("Gold")))
        ).copy(
            reviews = Reviews(
                rating = Rating(
                    averageRating = 3f,
                    count = 3,
                    ratingDistribution = emptyMap(),
                ),
                reviews = listOf(
                    Review(
                        text = "Bad product",
                        rating = 2,
                    ),
                    Review(
                        text = "Good product",
                        rating = 4,
                    ),
                    Review(
                        text = "Mid product",
                        rating = 3,
                    ),
                ),
                writeReviewUrl = "",
            )
        )

        testObjects.creator.createScreen(
            testObjects.productFlow(product),
            configs,
            screenId = testObjects.screenId,
        ).test {
            assertEquals(
                loadingStateReviews(config),
                awaitItem()
            )

            assertEquals(
                successfulDetailScreenOf(
                    ProductReviews(
                        state = LoadingComponent.State.Done(
                            content = ProductReviews.Content.Reviews(
                                totalCount = 3,
                                rating = Rating(
                                    averageRating = 3f,
                                    count = 3,
                                    ratingDistribution = emptyMap(),
                                ),
                                reviews = listOf(
                                    Review(
                                        text = "Bad product",
                                        rating = 2,
                                    ),
                                    Review(
                                        text = "Good product",
                                        rating = 4,
                                    ),
                                ),
                                allReviewsUrl = "https://example.com/reviews/parentId/",
                            )
                        ),
                        config = config,
                    )
                ),
                awaitItem()
            )
        }
    }

    private fun loadingStateRating(config: ProductRating.Config) = successfulDetailScreenOf(
        ProductRating(
            state = LoadingComponent.State.Loading(
                Placeholders.productRating
            ),
            config,
        )
    )

    private fun loadingStateReviews(config: ProductReviews.Config) = successfulDetailScreenOf(
        ProductReviews(
            state = LoadingComponent.State.Loading(
                Placeholders.productReviews
            ),
            config,
        )
    )
}
