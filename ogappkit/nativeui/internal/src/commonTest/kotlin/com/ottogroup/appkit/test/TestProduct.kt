package com.ottogroup.appkit.test

import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping
import com.ottogroup.appkit.nativeui.api.lascana.data.VariantData
import com.ottogroup.appkit.nativeui.api.lascana.data.buildProductDetailQueryData
import com.ottogroup.appkit.nativeui.api.lascana.data.createQueryProduct
import com.ottogroup.appkit.nativeui.lascana.type.buildFlyout
import com.ottogroup.appkit.nativeui.lascana.type.buildProduct
import com.ottogroup.appkit.nativeui.model.domain.ArticleStandards
import com.ottogroup.appkit.nativeui.model.domain.Availability
import com.ottogroup.appkit.nativeui.model.domain.Brand
import com.ottogroup.appkit.nativeui.model.domain.Dimension
import com.ottogroup.appkit.nativeui.model.domain.Flag
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.Information
import com.ottogroup.appkit.nativeui.model.domain.Link
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.ProductReviews
import com.ottogroup.appkit.nativeui.model.domain.Rating
import com.ottogroup.appkit.nativeui.model.domain.Review
import com.ottogroup.appkit.nativeui.model.domain.Reviews
import kotlinx.datetime.Instant

internal val testProduct: Product
    get() {
        val price = Price(
            currency = "EUR",
            value = 1299,
            oldValue = 1599,
        )
        val colorDimension = Dimension(
            name = "Color",
            type = Dimension.DimensionType.COLOR,
            values = listOf(
                createDimensionValue(
                    text = "Black",
                    productId = "01",
                    hasThumb = true,
                    availabilityState = Availability.State.IN_STOCK,
                    availabilityMessage = null,
                    price = price,
                ),
                createDimensionValue(
                    text = "White",
                    productId = "02",
                    hasThumb = true,
                    availabilityState = Availability.State.IN_STOCK,
                    availabilityMessage = null,
                    price = price,
                ),
                createDimensionValue(
                    text = "Pink",
                    productId = "03",
                    hasThumb = true,
                    availabilityState = Availability.State.IN_STOCK,
                    availabilityMessage = null,
                    price = price,
                )
            )
        )

        return Product(
            id = "02",
            title = "Product title",
            shortTitle = "Product",
            webShopUrl = "https://web.shop/product/02",
            parentId = "0",
            images = listOf(Image(url = "https://img.url/product", thumbnailUrl = THUMB_URL)),
            flags = listOf(
                Flag.Sale,
                Flag.SalesUnit(salesUnitCount = 3),
            ),
            brand = Brand(name = "Lascana", description = "This is a cool brand"),
            price = price,
            fusedDimensions = listOf(colorDimension),
            individualDimensions = listOf(colorDimension),
            sizeMatrix = null,
            availability = Availability(
                message = "In stock",
                state = Availability.State.IN_STOCK,
                quantity = 999,
                deliveryTime = null
            ),
            information = Information(
                articleNumber = "1234556",
                description = "Long text",
                bulletPoints = listOf("one", "two"),
                attributesTable = Information.AttributesTable(listOf()),
                distributingCompanies = listOf(
                    Information.DistributingCompany(
                        name = "ACME Corp",
                        address = "Fakestreet 123\n12345 Townsville, XY",
                        email = null,
                        phone = "+1234567890",
                        url = "https://www.example.com",
                    ),
                    Information.DistributingCompany(
                        name = "Second Inc.",
                        address = "Streetlane 1\n54321 Secondtown, XY",
                        email = null,
                        phone = "+1234567890",
                        url = "https://www.example.com",
                    )
                ),
                documents = listOf(
                    Link("Manual", "https://www.example.com/files/manual.pdf"),
                ),
                articleStandards = ArticleStandards.StructuredSeals(listOf(ArticleStandards.Seal.CottonMadeInAfrica))
            ),
            reviews = Reviews(
                rating = Rating(
                    averageRating = 3.5f,
                    count = 2,
                    ratingDistribution = emptyMap(),
                ),
                reviews = listOf(
                    Review(
                        text = "Good product",
                        rating = 4,
                    ),
                    Review(
                        text = "Mid product",
                        rating = 3,
                    )
                ),
                writeReviewUrl = "/write-review",
            ),
            shopTheLook = null,
            moreFromTheSeries = null,
            breadcrumbs = listOf("fashion", "tops", "shirts"),
        )
    }

internal fun createDimensionValue(
    text: String,
    productId: String,
    hasThumb: Boolean,
    thumbnailUrl: String = POSITIONED_URL,
    availabilityState: Availability.State = Availability.State.IN_STOCK,
    availabilityMessage: String? = "deliveryInformation",
    price: Price = Price(
        currency = "EUR",
        value = 0,
    )
): Dimension.Value {
    return Dimension.Value(
        text = text,
        productId = productId,
        thumbnailUrl = thumbnailUrl.takeIf { hasThumb },
        availability = Availability(
            state = availabilityState,
            quantity = when (availabilityState) {
                Availability.State.IN_STOCK -> 100
                Availability.State.LOW_STOCK -> 5
                Availability.State.PRE_ORDERABLE -> 0
                Availability.State.TEMPORARILY_OUT_OF_STOCK -> 0
                Availability.State.PERMANENTLY_OUT_OF_STOCK -> 0
                Availability.State.UNKNOWN -> -1
            },
            message = availabilityMessage
        ),
        price = price
    )
}

internal fun createTestProduct(
    variantLabels: List<String>,
    variantData: List<VariantData>,
    fuseNonColorDimensionsInto: String? = null,
): Product {
    val queryProduct = if (variantData.isEmpty()) {
        buildProductDetailQueryData {
            product = buildProduct {
                id = "parent"
                this.variantLabels = variantLabels
                variantValues = emptyList()
                variants = emptyList()
                flyouts = buildFlyout {
                    sizeGuide = null
                }
            }
        }.product
    } else {
        createQueryProduct(
            variantLabels = variantLabels,
            variantData = variantData
        )
    }

    /* This is using the LAS query model and mapping to generate a domain Product, under the assumption that tests
     * independently verify that part to be working correctly. */
    return ModelMapping(fuseNonColorDimensionsInto = fuseNonColorDimensionsInto).toProduct(queryProduct)
}

internal const val THUMB_URL = "https://img.url/thumb"
internal const val POSITIONED_URL = "https://img.url/positioned"

internal val testProductReviews: ProductReviews
    get() {
        return ProductReviews(
            id = "1",
            title = "Product",
            brandName = "Manufacturer",
            reviews = Reviews(
                rating = Rating(
                    averageRating = 3.5f,
                    count = 2,
                    ratingDistribution = mapOf(4 to Pair(first = 1, second = 50.0), 3 to Pair(1, 50.0))
                ),
                reviews = listOf(
                    Review(
                        text = "Good product",
                        rating = 4,
                        reviewerName = "John",
                        dateTime = Instant.fromEpochSeconds(1732192729),
                    ),
                    Review(
                        text = "Mid product",
                        rating = 3,
                        reviewerName = "Anna",
                        dateTime = Instant.fromEpochSeconds(1732106329),
                    )
                ),
                writeReviewUrl = "/write-review",
            )
        )
    }
