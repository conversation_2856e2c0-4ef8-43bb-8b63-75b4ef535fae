package com.ottogroup.appkit.test

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.api.NativeApi
import com.ottogroup.appkit.nativeui.api.NativeApi.CachePolicy
import com.ottogroup.appkit.nativeui.api.NativeApiProvider
import com.ottogroup.appkit.nativeui.model.domain.Basket
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.ProductReviews
import com.ottogroup.appkit.nativeui.model.domain.Wishlist
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update

internal class FakeNativeApiProvider : NativeApiProvider, TestCallVerifiable by TestCallVerifier() {
    private val cache = mutableMapOf<String, Product>()
    var products: Map<String, Product> = emptyMap()
    val wishlist = MutableStateFlow(Wishlist(emptyList()))
    val basket = MutableStateFlow(Basket(emptyList()))

    override val nativeApi: NativeApi = object : NativeApi {
        override fun getProduct(
            id: String,
            resolveVariant: Boolean,
            includeShopTheLook: Boolean,
            cachePolicy: CachePolicy,
            overrideProductId: String?,
        ): Flow<Result<Product>> {
            recordCall(::getProduct.name, id, cachePolicy)
            return flow {
                val cachedProduct = cache[id]?.let {
                    if (overrideProductId != null) {
                        it.copy(id = overrideProductId)
                    } else {
                        it
                    }
                }
                when (cachePolicy) {
                    CachePolicy.CacheOnly -> {
                        cachedProduct?.let { emit(Result.Success(it)) }
                        return@flow
                    }
                    CachePolicy.CacheFirst -> cachedProduct?.let {
                        emit(Result.Success(it))
                        return@flow
                    }
                    CachePolicy.CacheAndNetwork -> {
                        cachedProduct?.let { emit(Result.Success(it)) }
                        emit(fakeNetworkRequest(id))
                    }
                }
            }
        }

        private suspend fun fakeNetworkRequest(id: String): Result<Product> {
            delay(1000)
            return products[id]?.let {
                cache[id] = it
                Result.Success(it)
            } ?: Result.Failure(RuntimeException("Product not found"))
        }

        override fun getMinimalProduct(id: String): Flow<Result<Product>> {
            products[id]?.let { return MutableStateFlow(Result.Success(it)) }
            return flowOf(Result.Failure(RuntimeException("Not implemented")))
        }

        override fun getReviews(id: String): Flow<Result<ProductReviews>> {
            return flowOf(Result.Failure(RuntimeException("Not implemented")))
        }

        override fun getWishlist(): Flow<Result<Wishlist>> {
            return wishlist.map { Result.Success<Wishlist>(it) }
        }

        override fun getBasket(): Flow<Result<Basket>> {
            return basket.map { Result.Success<Basket>(it) }
        }

        override fun isProductOnWishlist(id: String): Flow<Result<Boolean>> {
            return wishlist.map { wishlist ->
                Result.Success(wishlist.items.any { it.id == id })
            }
        }

        override suspend fun addProductToWishlist(id: String): Result<Wishlist> {
            wishlist.update { it.copy(items = it.items + Wishlist.Item(id)) }
            return Result.Success(wishlist.value)
        }

        override suspend fun removeProductFromWishlist(id: String): Result<Wishlist> {
            wishlist.update { it.copy(items = it.items.filter { it.id != id }) }
            return Result.Success(wishlist.value)
        }

        override suspend fun addProductToBasket(id: String): Result<Basket> {
            basket.update { it ->
                val existing = it.items.find { it.id == id }
                val new = existing?.let { it.copy(amount = it.amount + 1) } ?: Basket.Item(id, 1)
                it.copy(items = it.items.filter { it.id != id } + new)
            }
            return Result.Success(basket.value)
        }

        override suspend fun addVoucherToBasket(
            id: String,
            customName: String?
        ): Result<Basket> {
            return addProductToBasket(id)
        }
    }
}
