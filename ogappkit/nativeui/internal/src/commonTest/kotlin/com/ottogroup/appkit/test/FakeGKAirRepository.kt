package com.ottogroup.appkit.test

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.nativeui.api.gkair.GkAirRepository
import com.ottogroup.appkit.nativeui.model.domain.Dimension
import com.ottogroup.appkit.nativeui.model.ui.GkAirRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendations
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow

internal class FakeGKAirRepository : GkAirRepository {
    val recommendations: MutableSharedFlow<Result<ProductRecommendations.Content>> = MutableSharedFlow()

    override fun getGkAirRecommendations(
        productId: String,
        categoryId: String,
        fusedDimensions: List<Dimension>,
        isAddToBasketSuccessScreen: <PERSON><PERSON><PERSON>,
        config: GkAirRecommendations.Config,
    ): Flow<Operation<ProductRecommendations.Content>> {
        return recommendations.asOperation()
    }
}
