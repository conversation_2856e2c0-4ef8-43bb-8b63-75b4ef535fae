package com.ottogroup.appkit.test

import com.ottogroup.appkit.tracking.ContextParameter
import com.ottogroup.appkit.tracking.OGTracking
import com.ottogroup.appkit.tracking.OGTrackingConfig
import com.ottogroup.appkit.tracking.consent.OGTrackingConsent
import com.ottogroup.appkit.tracking.event.OGEvent
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import com.ottogroup.appkit.tracking.services.ServiceConfig
import com.ottogroup.appkit.tracking.services.adjust.AdjustAnalytics
import com.ottogroup.appkit.tracking.services.airship.AirshipAnalytics
import com.ottogroup.appkit.tracking.services.firebase.FirebaseAnalytics
import com.ottogroup.appkit.tracking.services.snowplow.SnowplowAnalytics
import com.ottogroup.appkit.tracking.userproperty.UserProperty
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine

internal class FakeOGTracking : OGTracking, TestCallVerifiable by TestCallVerifier() {
    override fun configure(config: OGTrackingConfig) = Unit

    override fun onDidLoad(url: String) = Unit

    override fun track(event: OGEvent) {
        recordCall(::track.name, event)
    }

    override fun setConsent(
        serviceId: OGTrackingServiceId,
        consent: Boolean
    ) = Unit

    override fun setGlobalConsent(globalConsent: Boolean) = Unit

    override fun configureAirshipAnalytics(airshipAnalytics: AirshipAnalytics) = Unit

    override fun configureAdjustAnalytics(adjustAnalytics: AdjustAnalytics) = Unit

    override fun configureFirebaseAnalytics(firebaseAnalytics: FirebaseAnalytics) = Unit

    override fun configureSnowplow(snowplowAnalytics: SnowplowAnalytics) = Unit

    override fun onUpdateConfig(
        id: OGTrackingServiceId,
        config: ServiceConfig
    ) = Unit

    override fun updateGlobalContext(
        id: OGTrackingServiceId,
        contextParameters: ContextParameter
    ) = Unit

    override fun setUserProperty(userProperty: UserProperty) = Unit
}

internal class FakeOGTrackingConsent : OGTrackingConsent {
    private val globalConsent = MutableStateFlow<Boolean>(true)
    private val consents = MutableStateFlow<Map<OGTrackingServiceId, Boolean>>(emptyMap())

    override fun consentForService(serviceId: OGTrackingServiceId): Flow<Boolean> {
        return combine(globalConsent, consents) { global, serviceConsents ->
            serviceConsents[serviceId] ?: global
        }
    }

    override fun setGlobalConsent(consent: Boolean) {
        globalConsent.value = consent
    }

    override fun setConsentsForServices(consents: Map<OGTrackingServiceId, Boolean>, replacePrevious: Boolean) {
        this.consents.value = if (replacePrevious) {
            consents
        } else {
            this.consents.value + consents
        }
    }
}
