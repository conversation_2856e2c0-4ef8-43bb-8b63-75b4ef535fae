package com.ottogroup.appkit.test

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.api.WishlistRepository
import com.ottogroup.appkit.nativeui.model.domain.Wishlist
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update

class FakeWishlistRepository : WishlistRepository {
    private val _wishlist = MutableStateFlow(Wishlist(emptyList()))

    override suspend fun addProductToWishlist(id: String): Result<Wishlist> {
        _wishlist.update { it.copy(items = it.items + Wishlist.Item(id)) }
        return Result.Success(_wishlist.value)
    }

    override suspend fun removeProductFromWishlist(id: String): Result<Wishlist> {
        _wishlist.update { it.copy(items = it.items.filter { it.id != id }) }
        return Result.Success(_wishlist.value)
    }

    override fun isProductOnWishlist(id: String): Flow<Result<Boolean>> {
        return _wishlist.map { wishlist ->
            Result.Success(wishlist.items.any { it.id == id })
        }.distinctUntilChanged()
    }
}
