package com.ottogroup.appkit.nativeui.api.witt.modelmapping

import com.ottogroup.appkit.nativeui.api.witt.data.MAIN_PRODUCT_ID
import com.ottogroup.appkit.nativeui.api.witt.data.TestProductDefinition
import com.ottogroup.appkit.nativeui.api.witt.data.createQueryProduct
import com.ottogroup.appkit.nativeui.model.domain.Information
import com.ottogroup.appkit.nativeui.model.domain.Link
import com.ottogroup.appkit.nativeui.witt.GetEuImportersQuery
import kotlin.test.Test
import kotlin.test.assertEquals

class InformationParsingTest {

    @Test
    fun `information object is mapped correctly with all components`() {
        val mapping = TestConfig.createStandardMapping()

        val queryData = createQueryProduct(
            TestProductDefinition(
                id = MAIN_PRODUCT_ID,
                name = "Test Product",
                withAttributes = true,
            )
        )

        val productBy = queryData.productBy.copy(
            dimensions = listOf(
                queryData.productBy.dimensions.first().copy(
                    id = "*********",
                    promotion = "ABC"
                )
            ),
            description = "This is a comprehensive test product description",
            disposalNote = true
        )

        val euImporters = listOf(
            GetEuImportersQuery.EuImporterByProduct(
                __typename = "EuImporter",
                id = "importer1",
                companyName = "Test Company GmbH",
                street = "Teststraße",
                houseNumber = "123",
                additionalAddress = "2. OG",
                zipCode = "12345",
                city = "Berlin",
                countryCode = "DE",
                email = "<EMAIL>",
                url = "https://test-company.com"
            )
        )

        val domainProduct = mapping.toProduct(
            queryProduct = productBy,
            siblings = emptyList(),
            reviews = emptyList(),
            rating = null,
            requestedId = "${MAIN_PRODUCT_ID}--${MAIN_PRODUCT_ID}_variant--123--",
            euImporters = euImporters
        )

        val expectedInformation = Information(
            articleNumber = "123.456.789.ABC",
            description = "This is a comprehensive test product description",
            bulletPoints = emptyList(),
            attributesTable = Information.AttributesTable(
                sections = listOf(
                    Information.AttributesTable.Section(
                        title = "",
                        entries = listOf(
                            Information.AttributesTable.Section.Entry(
                                key = domainProduct.information.attributesTable.sections.first().entries.find {
                                    it.values.contains(
                                        "Cotton"
                                    )
                                }?.key ?: "",
                                values = listOf("Cotton")
                            ),
                            Information.AttributesTable.Section.Entry(
                                key = domainProduct.information.attributesTable.sections.first().entries.find {
                                    it.values.contains(
                                        "Machine wash"
                                    )
                                }?.key ?: "",
                                values = listOf("Machine wash")
                            )
                        )
                    )
                )
            ),
            distributingCompanies = listOf(
                Information.DistributingCompany(
                    name = "Test Company GmbH",
                    address = "Teststraße 123\n 2. OG\n12345 Berlin, DE",
                    email = "<EMAIL>",
                    phone = null,
                    url = "https://test-company.com"
                )
            ),
            documents = listOf(
                Link(
                    text = "Entsorgungshinweis",
                    url = "https://www.witt-weiden.de/content/entsorgung"
                )
            ),
            articleStandards = null
        )

        assertEquals(expectedInformation, domainProduct.information)
    }

    @Test
    fun `information handles empty state correctly`() {
        val mapping = TestConfig.createStandardMapping()

        val queryData = createQueryProduct(
            TestProductDefinition(
                id = MAIN_PRODUCT_ID,
                name = "Test Product"
            )
        )

        val productBy = queryData.productBy.copy(
            description = null,
            disposalNote = false
        )

        val domainProduct = mapping.toProduct(
            queryProduct = productBy,
            siblings = emptyList(),
            reviews = emptyList(),
            rating = null,
            requestedId = MAIN_PRODUCT_ID,
            euImporters = emptyList()
        )

        val expectedInformation = Information(
            articleNumber = "000.001.001",
            description = "",
            bulletPoints = emptyList(),
            attributesTable = Information.AttributesTable(
                sections = listOf(
                    Information.AttributesTable.Section(
                        title = "",
                        entries = emptyList()
                    )
                )
            ),
            distributingCompanies = emptyList(),
            documents = emptyList(),
            articleStandards = null
        )

        assertEquals(expectedInformation, domainProduct.information)
    }
}
