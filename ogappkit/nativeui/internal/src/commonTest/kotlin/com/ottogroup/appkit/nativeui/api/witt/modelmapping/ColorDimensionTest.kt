package com.ottogroup.appkit.nativeui.api.witt.modelmapping

import com.ottogroup.appkit.nativeui.api.witt.data.TestProductDefinition
import com.ottogroup.appkit.nativeui.api.witt.data.createEuImportersData
import com.ottogroup.appkit.nativeui.api.witt.data.createQueryProducts
import com.ottogroup.appkit.nativeui.model.domain.Availability
import com.ottogroup.appkit.nativeui.model.domain.Dimension
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.domain.singleColorDimension
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class ColorDimensionTest {

    @Test
    fun `color dimension is correctly mapped from siblings with all properties`() {
        val mapping = TestConfig.createStandardMapping()

        val products = createQueryProducts(
            listOf(
                TestProductDefinition(
                    id = "sibling1",
                    name = "Red",
                    price = 3999,
                    oldPrice = 4999
                ),
                TestProductDefinition(
                    id = "sibling2",
                    name = "Blue",
                    price = 2999,
                    oldPrice = 0,
                    availability = com.ottogroup.appkit.nativeui.api.witt.data.Availability(quantity = 5)
                ),
            )
        )

        val mainProduct = products.first()
        val siblings = products.drop(1)

        val domainProduct = mapping.toProduct(
            queryProduct = mainProduct,
            siblings = siblings,
            reviews = emptyList(),
            rating = null,
            requestedId = mainProduct.id,
            euImporters = createEuImportersData().euImporterByProduct ?: emptyList()
        )

        val expectedColorDimension = Dimension(
            name = "Farbe", // Use the constant value directly
            type = Dimension.DimensionType.COLOR,
            values = listOf(
                Dimension.Value(
                    text = "Blue",
                    productId = "sibling2--sibling2_variant0--000001001--001",
                    thumbnailUrl = "https://cdn.witt.info/sibling2?brightness=0.97&width=256",
                    availability = Availability(
                        state = Availability.State.LOW_STOCK,
                        quantity = 5,
                        message = domainProduct.singleColorDimension()?.values?.find { it.text == "Blue" }?.availability?.message
                            ?: ""
                    ),
                    price = Price(value = 2999, currency = "EUR", oldValue = null, isStartPrice = false)
                ),
                Dimension.Value(
                    text = "Red",
                    productId = "sibling1--sibling1_variant0--000001001--001",
                    thumbnailUrl = "https://cdn.witt.info/sibling1?brightness=0.97&width=256",
                    availability = Availability(
                        state = Availability.State.IN_STOCK,
                        quantity = 100,
                        message = domainProduct.singleColorDimension()?.values?.find { it.text == "Red" }?.availability?.message
                            ?: ""
                    ),
                    price = Price(value = 3999, currency = "EUR", oldValue = 4999, isStartPrice = false)
                )
            )
        )

        val actualColorDimension = domainProduct.singleColorDimension()
        assertNotNull(actualColorDimension)
        assertEquals(expectedColorDimension, actualColorDimension)
    }

    @Test
    fun `color dimension handles edge cases correctly`() {
        val mapping = TestConfig.createStandardMapping()

        val products = createQueryProducts(
            listOf(
                TestProductDefinition(
                    id = "sibling1",
                    name = "Red",
                    availability = null,
                    price = null,
                    withImages = false,
                )
            )
        )
        val mainProduct = products.first()
        val siblings = products.drop(1)

        val domainProduct = mapping.toProduct(
            queryProduct = mainProduct,
            siblings = siblings,
            reviews = emptyList(),
            rating = null,
            requestedId = mainProduct.id,
            euImporters = createEuImportersData().euImporterByProduct ?: emptyList()
        )

        val expectedColorDimension = Dimension(
            name = "Farbe", // Use the constant value directly
            type = Dimension.DimensionType.COLOR,
            values = listOf(
                Dimension.Value(
                    text = "Red",
                    productId = "sibling1--sibling1_variant0--000001001--001",
                    thumbnailUrl = null,
                    availability = Availability.Unknown,
                    price = Price.None
                )
            )
        )

        val actualColorDimension = domainProduct.singleColorDimension()
        assertNotNull(actualColorDimension)
        assertEquals(expectedColorDimension, actualColorDimension)
    }
}
