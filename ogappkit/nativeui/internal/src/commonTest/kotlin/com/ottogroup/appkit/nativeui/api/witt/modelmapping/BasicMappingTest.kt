package com.ottogroup.appkit.nativeui.api.witt.modelmapping

import com.ottogroup.appkit.nativeui.api.witt.data.DimensionData
import com.ottogroup.appkit.nativeui.api.witt.data.MAIN_PRODUCT_ID
import com.ottogroup.appkit.nativeui.api.witt.data.TestProductDefinition
import com.ottogroup.appkit.nativeui.api.witt.data.VariantData
import com.ottogroup.appkit.nativeui.api.witt.data.createEuImportersData
import com.ottogroup.appkit.nativeui.api.witt.data.createQueryProduct
import com.ottogroup.appkit.nativeui.model.domain.Availability
import com.ottogroup.appkit.nativeui.model.domain.Brand
import com.ottogroup.appkit.nativeui.model.domain.CompanyOfOrigin
import com.ottogroup.appkit.nativeui.model.domain.Flag
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.Reviews
import com.ottogroup.appkit.nativeui.witt.ProductDetailQuery
import com.ottogroup.appkit.nativeui.witt.fragment.ImageFields
import com.ottogroup.appkit.nativeui.witt.type.AvailabilityState
import com.ottogroup.appkit.nativeui.witt.type.ProductFlag
import com.ottogroup.appkit.nativeui.witt.type.ProductImageType
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull

class BasicMappingTest {

    @Test
    fun `basic product mapping works correctly with all core fields`() {
        val mapping = TestConfig.createStandardMapping()

        val queryData = createQueryProduct(
            TestProductDefinition(
                id = MAIN_PRODUCT_ID,
                name = "Basic Shirt"
            )
        )

        val productBy = queryData.productBy.copy(
            mainTitle = "Premium Basic Shirt",
            flag = ProductFlag.sale,
            salesUnit = "2-Pack",
            companyOfOrigin = "WITT",
            images = listOf(
                ProductDetailQuery.Image1(
                    __typename = "Image",
                    imageFields = ImageFields(
                        __typename = "Image",
                        hash = "test-image-hash",
                        alt = "Test image",
                        type = ProductImageType.product
                    )
                )
            )
        )

        val domainProduct = mapping.toProduct(
            queryProduct = productBy,
            siblings = emptyList(),
            reviews = emptyList(),
            rating = null,
            requestedId = "${MAIN_PRODUCT_ID}--${MAIN_PRODUCT_ID}_variant----",
            euImporters = createEuImportersData().euImporterByProduct ?: emptyList()
        )

        val expectedProduct = Product(
            id = "${MAIN_PRODUCT_ID}--${MAIN_PRODUCT_ID}_variant0--*********--001",
            mkz = "mkz",
            title = "Premium Basic Shirt",
            shortTitle = "Basic Shirt",
            webShopUrl = "https://www.witt-weiden.de/p/$MAIN_PRODUCT_ID",
            parentId = MAIN_PRODUCT_ID,
            images = listOf(
                Image(
                    url = "https://cdn.witt.info/test-image-hash?brightness=0.97&width=1240",
                    thumbnailUrl = "https://cdn.witt.info/test-image-hash?brightness=0.97&width=256"
                )
            ),
            flags = listOf(Flag.Sale, Flag.SalesUnit(salesUnitCount = 2)),
            brand = Brand(name = "TestBrand", description = null),
            price = Price(value = 2999, currency = "EUR", oldValue = null, isStartPrice = false),
            fusedDimensions = domainProduct.fusedDimensions,
            individualDimensions = domainProduct.individualDimensions,
            sizeMatrix = null,
            availability = domainProduct.availability,
            information = domainProduct.information,
            reviews = Reviews(
                rating = null,
                reviews = emptyList(),
                writeReviewUrl = "customer#customerLoginModal"
            ),
            sku = "",
            shopTheLook = null,
            moreFromTheSeries = null,
            breadcrumbs = emptyList(),
            categoryId = null,
            paybackPoints = 14,
            sizeAdvisorUrl = "app://productDetail/sizeAdvisor",
            companyOfOrigin = CompanyOfOrigin.DEFAULT
        )

        assertEquals(expectedProduct, domainProduct)
    }

    @Test
    fun `title fallback and null handling works correctly`() {
        val mapping = TestConfig.createStandardMapping()

        val queryData = createQueryProduct(
            TestProductDefinition(
                id = MAIN_PRODUCT_ID,
                name = "Basic Shirt"
            )
        )

        val productBy = queryData.productBy.copy(
            mainTitle = null,
            brand = null,
            companyOfOrigin = "UNKNOWN"
        )

        val domainProduct = mapping.toProduct(
            queryProduct = productBy,
            siblings = emptyList(),
            reviews = emptyList(),
            rating = null,
            requestedId = MAIN_PRODUCT_ID,
            euImporters = createEuImportersData().euImporterByProduct ?: emptyList()
        )

        assertEquals("Basic Shirt", domainProduct.title)
        assertEquals("Basic Shirt", domainProduct.shortTitle)
        assertNull(domainProduct.brand)
        assertEquals(CompanyOfOrigin.DEFAULT, domainProduct.companyOfOrigin)
    }

    @Test
    fun `availability states are mapped correctly`() {
        val inStockProduct = createTestProductWithAvailability(
            availabilityQuantity = 100,
            state = AvailabilityState.available
        )
        assertEquals(Availability.State.IN_STOCK, inStockProduct.availability.state)
        assertEquals(100, inStockProduct.availability.quantity)

        val lowStockProduct = createTestProductWithAvailability(
            availabilityQuantity = 5,
            state = AvailabilityState.available
        )
        assertEquals(Availability.State.LOW_STOCK, lowStockProduct.availability.state)
        assertEquals(5, lowStockProduct.availability.quantity)

        val preOrderableProduct = createTestProductWithAvailability(
            availabilityQuantity = 0,
            state = AvailabilityState.delayed
        )
        assertEquals(Availability.State.PRE_ORDERABLE, preOrderableProduct.availability.state)
    }

    @Test
    fun `flag mapping works for different flag types`() {
        val testCases = listOf(
            Triple(ProductFlag.sale, null, listOf(Flag.Sale)),
            Triple(ProductFlag.new, null, listOf(Flag.New)),
            Triple(ProductFlag.sale, "3-Pack", listOf(Flag.Sale, Flag.SalesUnit(salesUnitCount = 3))),
            Triple(ProductFlag.exclusiveOnline, null, listOf(Flag.ExclusiveOnline))
        )

        testCases.forEach { (flag, salesUnit, expectedFlags) ->
            val product = createTestProductWithFlag(flag, salesUnit)
            assertEquals(expectedFlags, product.flags)
        }
    }

    @Test
    fun `payback points are null when displayPaybackPoints is false`() {
        val mapping = TestConfig.createMappingWithoutPaybackPoints()

        val queryData = createQueryProduct(
            TestProductDefinition(
                id = MAIN_PRODUCT_ID,
                name = "Test Product"
            )
        )

        val domainProduct = mapping.toProduct(
            queryProduct = queryData.productBy,
            siblings = emptyList(),
            reviews = emptyList(),
            rating = null,
            requestedId = MAIN_PRODUCT_ID,
            euImporters = createEuImportersData().euImporterByProduct ?: emptyList()
        )

        assertNull(domainProduct.paybackPoints)
    }

    private fun createTestProductWithAvailability(
        availabilityQuantity: Int,
        state: AvailabilityState
    ): Product {
        val mapping = TestConfig.createStandardMapping()
        val productBy = createQueryProduct(
            TestProductDefinition(
                id = MAIN_PRODUCT_ID,
                name = "Test Product",
                dimensions = listOf(
                    DimensionData(
                        label = "Size",
                        variants = listOf(
                            VariantData(
                                size = "M",
                                availability = com.ottogroup.appkit.nativeui.api.witt.data.Availability(
                                    state,
                                    availabilityQuantity
                                )
                            )
                        )
                    )
                ),
            ),
        ).productBy

        return mapping.toProduct(
            queryProduct = productBy,
            siblings = emptyList(),
            reviews = emptyList(),
            rating = null,
            requestedId = "${MAIN_PRODUCT_ID}--${MAIN_PRODUCT_ID}_variant--123--",
            euImporters = createEuImportersData().euImporterByProduct ?: emptyList()
        )
    }

    private fun createTestProductWithFlag(
        flag: ProductFlag,
        salesUnit: String?
    ): Product {
        val mapping = TestConfig.createStandardMapping()
        val queryData = createQueryProduct(
            TestProductDefinition(
                id = MAIN_PRODUCT_ID,
                name = "Test Product"
            )
        )

        val productBy = queryData.productBy.copy(
            flag = flag,
            salesUnit = salesUnit
        )

        return mapping.toProduct(
            queryProduct = productBy,
            siblings = emptyList(),
            reviews = emptyList(),
            rating = null,
            requestedId = MAIN_PRODUCT_ID,
            euImporters = createEuImportersData().euImporterByProduct ?: emptyList()
        )
    }
}
