package com.ottogroup.appkit.nativeui.creator.reviews

import app.cash.turbine.test
import com.ottogroup.appkit.nativeui.api.ProductReviewsRepository
import com.ottogroup.appkit.nativeui.creator.reviews.ProductReviewsScreenCreatorTestObjects.creator
import com.ottogroup.appkit.nativeui.creator.reviews.ProductReviewsScreenCreatorTestObjects.reviewsFlow
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.ProductReviewsComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.WriteReviewButton
import com.ottogroup.appkit.test.testProductReviews
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.test.runTest

class WriteReviewButtonTests {

    @Test
    fun `WriteReviewButton returns button content`() = runTest {
        val config = WriteReviewButton.Config()
        val configs: ComponentConfigs<ProductReviewsComponentConfig> = ComponentConfigs(
            listOf(config)
        )
        creator.createScreen(
            reviewsFlow(testProductReviews),
            configs,
            ProductReviewsRepository(reviewsPerPage = 15),
            "12345"
        ).test {
            assertEquals(
                successfulDetailScreenOf(
                    WriteReviewButton(
                        writeReviewUrl = testProductReviews.reviews.writeReviewUrl,
                        config = config
                    )
                ),
                awaitItem()
            )
        }
    }

    @Test
    fun `WriteReviewButton URL is overridden by config`() = runTest {
        val config = WriteReviewButton.Config(
            writeReviewUrl = "/write-review-v2/{productId}"
        )
        val configs: ComponentConfigs<ProductReviewsComponentConfig> = ComponentConfigs(
            listOf(config)
        )
        creator.createScreen(
            reviewsFlow(testProductReviews),
            configs,
            ProductReviewsRepository(reviewsPerPage = 15),
            "12345"
        ).test {
            assertEquals(
                successfulDetailScreenOf(
                    WriteReviewButton(
                        writeReviewUrl = "/write-review-v2/${testProductReviews.id}",
                        config = config
                    )
                ),
                awaitItem()
            )
        }
    }
}
