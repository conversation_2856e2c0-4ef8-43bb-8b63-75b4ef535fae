package com.ottogroup.appkit.nativeui.api.witt

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.api.witt.utils.BasketItemData
import com.ottogroup.appkit.nativeui.api.witt.utils.FakeApolloRepository
import com.ottogroup.appkit.nativeui.api.witt.utils.TestWittCookiesBridge
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.model.domain.Basket
import com.ottogroup.appkit.nativeui.witt.AddItemToBasketMutation
import com.ottogroup.appkit.nativeui.witt.GetBasketQuery
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.test.runTest

class WittBasketApiTest {
    private val apolloRepository = FakeApolloRepository()
    private val cookiesBridge = TestWittCookiesBridge()

    private val config = OGNativeConfig.Witt(
        graphQLBackend = OGNativeConfig.Backend(
            url = "https://test.api",
            headers = mapOf("Authorization" to "Bearer test-token")
        ),
        productIdRegex = ".*",
        cookiesBridge = cookiesBridge,
        locale = "de-DE",
        cookies = OGNativeConfig.Witt.Cookies("https://test.api", "sessionToken"),
        webShopBaseUrl = "https://test.shop",
        displayPaybackPoints = true
    )

    private val configProvider = OGNativeConfigProvider().apply {
        update(config)
    }

    private fun createApi() = WittBasketApiImpl(
        apolloRepository = apolloRepository,
        configProvider = configProvider
    )

    @Test
    fun `getBasket returns basket with items`() = runTest {
        apolloRepository.setupGetBasketResponse(
            basketItems = listOf(
                BasketItemData("item1", 2),
                BasketItemData("item2", 1)
            )
        )

        val api = createApi()
        val result = api.getBasket()

        assertTrue(result is Result.Success)
        val basket = result.value
        assertEquals(2, basket.items.size)
        assertEquals(Basket.Item("item1", 2), basket.items[0])
        assertEquals(Basket.Item("item2", 1), basket.items[1])

        apolloRepository.verify(
            "query" to listOf(GetBasketQuery("de-DE"))
        )
    }

    @Test
    fun `getBasket returns empty basket when no items`() = runTest {
        apolloRepository.setupGetBasketResponse(basketItems = emptyList())

        val api = createApi()
        val result = api.getBasket()

        assertTrue(result is Result.Success)
        assertEquals(0, result.value.items.size)

        apolloRepository.verify(
            "query" to listOf(GetBasketQuery("de-DE"))
        )
    }

    @Test
    fun `getBasket returns failure when repository fails`() = runTest {
        val error = RuntimeException("Network error")
        apolloRepository.setupQueryFailure(error)

        val api = createApi()
        val result = api.getBasket()

        assertTrue(result is Result.Failure)
        assertEquals(error, result.failure)

        apolloRepository.verify(
            "query" to listOf(GetBasketQuery("de-DE"))
        )
    }

    @Test
    fun `addToBasket with complete product ID returns basket from mutation response`() = runTest {
        val productId = "123--variant1--display1--promo1"
        apolloRepository.setupAddToBasketResponse(
            basketItems = listOf(
                BasketItemData("123--variant1--display1--promo1", 1),
                BasketItemData("existing-item", 2)
            )
        )

        val api = createApi()
        val result = api.addToBasket(productId)

        assertTrue(result is Result.Success)
        val basket = result.value
        assertEquals(2, basket.items.size)
        assertEquals(Basket.Item("123--variant1--display1--promo1", 1), basket.items[0])
        assertEquals(Basket.Item("existing-item", 2), basket.items[1])

        apolloRepository.verify(
            "mutate" to listOf(
                AddItemToBasketMutation(
                    productId = "123",
                    variantId = "variant1",
                    displayNumber = "display1",
                    promotion = "promo1",
                    locale = "de-DE"
                )
            )
        )
    }

    @Test
    fun `addToBasket with incomplete product ID returns failure`() = runTest {
        val incompleteProductId = "123--variant1----"

        val api = createApi()
        val result = api.addToBasket(incompleteProductId)

        assertTrue(result is Result.Failure)
        assertTrue(result.failure is IllegalArgumentException)
        assertEquals("Invalid product ID format: $incompleteProductId", result.failure.message)

        apolloRepository.verifyNone("mutate")
    }

    @Test
    fun `addToBasket with simple product ID returns failure`() = runTest {
        val simpleProductId = "123"

        val api = createApi()
        val result = api.addToBasket(simpleProductId)

        assertTrue(result is Result.Failure)
        assertTrue(result.failure is IllegalArgumentException)
        assertEquals("Invalid product ID format: $simpleProductId", result.failure.message)

        apolloRepository.verifyNone("mutate")
    }

    @Test
    fun `addToBasket returns failure when repository fails`() = runTest {
        val productId = "123--variant1--display1--promo1"
        val error = RuntimeException("Network error")
        apolloRepository.setupMutationFailure(error)

        val api = createApi()
        val result = api.addToBasket(productId)

        assertTrue(result is Result.Failure)
        assertEquals(error, result.failure)

        apolloRepository.verify(
            "mutate" to listOf(
                AddItemToBasketMutation(
                    productId = "123",
                    variantId = "variant1",
                    displayNumber = "display1",
                    promotion = "promo1",
                    locale = "de-DE"
                )
            )
        )
    }
}
