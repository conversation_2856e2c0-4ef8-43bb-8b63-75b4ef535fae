package com.ottogroup.appkit.nativeui.api.witt.modelmapping

import com.ottogroup.appkit.base.http.CookiesBridge
import com.ottogroup.appkit.nativeui.api.witt.WittModelMapping
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.test.TestCookiesBridge

internal object TestConfig {

    internal fun createStandardConfig(cookiesBridge: CookiesBridge = TestCookiesBridge()): OGNativeConfig.Witt = OGNativeConfig.Witt(
        graphQLBackend = OGNativeConfig.Backend("https://test.api"),
        productIdRegex = ".*",
        cookiesBridge = cookiesBridge,
        locale = "de-DE",
        cookies = OGNativeConfig.Witt.Cookies(
            url = "https://test.api",
            tokenCookieName = "sessionToken",
            recoSessionIdCookieName = "recoSessionId"
        ),
        webShopBaseUrl = "https://www.witt-weiden.de",
        displayPaybackPoints = true
    )

    private fun createConfigWithoutPaybackPoints(): OGNativeConfig.Witt = createStandardConfig().copy(
        displayPaybackPoints = false
    )

    fun createStandardConfigProvider(): OGNativeConfigProvider = OGNativeConfigProvider().apply {
        update(createStandardConfig())
    }

    private fun createConfigProviderWithoutPaybackPoints(): OGNativeConfigProvider = OGNativeConfigProvider().apply {
        update(createConfigWithoutPaybackPoints())
    }

    fun createStandardMapping(): WittModelMapping = WittModelMapping(createStandardConfigProvider())

    fun createMappingWithoutPaybackPoints(): WittModelMapping = WittModelMapping(createConfigProviderWithoutPaybackPoints())
}
