package com.ottogroup.appkit.nativeui.api

import app.cash.turbine.test
import com.apollographql.apollo.api.Query
import com.apollographql.apollo.cache.normalized.FetchPolicy
import com.apollographql.mockserver.MockResponse
import com.apollographql.mockserver.MockServer
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.lascana.AnotherTestQuery
import com.ottogroup.appkit.nativeui.lascana.TestQuery
import kotlin.test.AfterTest
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertIs
import kotlin.time.Duration.Companion.milliseconds
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking

/**
 * This test works with a dummy introspection query simply because it has a
 * short response body. The same principles apply to all other queries as
 * well. Note that the caching aspect of these tests works because the same
 * query is performed and Apollo caches complete queries.
 *
 * [runBlocking] is used instead of [runTest] because real network requests
 * are performed here (to a local mock server) that do not respect the
 * time-control of the test coroutine dispatcher, causing issues when
 * observing the flows.
 */
class ApolloRepositoryImplTest {

    private lateinit var mockServer: MockServer
    private lateinit var apolloRepository: ApolloRepositoryImpl

    @BeforeTest
    fun setUp() = runBlocking {
        mockServer = MockServer()

        val config = OGNativeConfig.None(
            graphQLBackend = OGNativeConfig.Backend(mockServer.url()),
        )

        val configProvider = OGNativeConfigProvider().apply {
            update(config)
        }

        /* We want to create a real ApolloClient, as configured in createApolloClient, so we use a
         * real ApolloClientFactory for that. */
        val apolloFactory = ApolloClientFactory(configProvider)
        val apolloClient = apolloFactory.createApolloClient()

        val mockApolloProvider = object : ApolloProvider {
            override val apolloClient = apolloClient
        }

        // using a longer rate-limit duration because the MockServer is pretty slow
        val rateLimiter = RateLimiter<Query<*>>(500.milliseconds)

        apolloRepository = ApolloRepositoryImpl(mockApolloProvider, rateLimiter)
    }

    @AfterTest
    fun tearDown() = runBlocking {
        mockServer.close()
    }

    @Test
    fun `query always returns data from network and not from cache`() {
        runBlocking {
            // load response into cache
            mockServer.enqueue(successResponse())
            apolloRepository.query(TestQuery())

            // make server return error
            mockServer.enqueue(errorResponse())
            val response = apolloRepository.query(TestQuery())

            // response should be the error, not the cached success
            assertIs<Result.Failure<TestQuery.Data>>(response)
        }
    }

    @Test
    fun `queryWithCache first returns cached response then performs network request`() =
        runBlocking {
            // load response into cache
            mockServer.enqueue(successResponse())
            apolloRepository.query(TestQuery())

            mockServer.enqueue(successResponse())

            apolloRepository.queryWithCache(TestQuery()).test {
                assertEquals(expectedData(), awaitItem())
                assertEquals(expectedData(), awaitItem())
                assertEquals(
                    emptyList(),
                    cancelAndConsumeRemainingEvents()
                )
            }
        }

    @Test
    fun `queryWithCache with CacheFirst policy returns cached response and skips network request`() =
        runBlocking {
            // load response into cache
            mockServer.enqueue(successResponse())
            apolloRepository.query(TestQuery())

            mockServer.enqueue(successResponse())

            apolloRepository.queryWithCache(TestQuery(), FetchPolicy.CacheFirst).test {
                assertEquals(expectedData(), awaitItem())
                assertEquals(
                    emptyList(),
                    cancelAndConsumeRemainingEvents()
                )
            }
            assertEquals(1, mockServer.requestCount())
        }

    @Test
    fun `queryWithCache returns no error when cached data exists`() = runBlocking {
        // load response into cache
        mockServer.enqueue(successResponse())
        apolloRepository.query(TestQuery())

        // no result from network
        mockServer.enqueue(errorResponse())

        apolloRepository.queryWithCache(TestQuery()).test {
            assertEquals(expectedData(), awaitItem())
            assertEquals(
                emptyList(),
                cancelAndConsumeRemainingEvents()
            )
        }
    }

    @Test
    fun `queryWithCache returns data from network when cache is empty`() =
        runBlocking {
            mockServer.enqueue(successResponse())

            apolloRepository.queryWithCache(TestQuery()).test {
                assertEquals(expectedData(), awaitItem())
                assertEquals(
                    emptyList(),
                    cancelAndConsumeRemainingEvents()
                )
            }
        }

    @Test
    fun `queryWithCache returns error when first request fails`() = runBlocking {
        // no result from network
        mockServer.enqueue(errorResponse())

        apolloRepository.queryWithCache(TestQuery()).test {
            assertIs<Result.Failure<TestQuery.Data>>(awaitItem())
            assertEquals(
                emptyList(),
                cancelAndConsumeRemainingEvents()
            )
        }
    }

    @Test
    fun `queryWithCache remains open to emit new data when requested from elsewhere after error`() =
        runBlocking {
            // no result from network
            mockServer.enqueue(errorResponse())

            apolloRepository.queryWithCache(TestQuery()).test {
                assertIs<Result.Failure<TestQuery.Data>>(awaitItem())
                expectNoEvents()

                // perform separate request on the same resource
                mockServer.enqueue(successResponse())
                apolloRepository.query(TestQuery())

                // original flow receives new data
                assertEquals(expectedData(), awaitItem())
                assertEquals(
                    emptyList(),
                    cancelAndConsumeRemainingEvents()
                )
            }
        }

    @Test
    fun `queryWithCache remains open to emit new data when requested from elsewhere after initial success`() =
        runBlocking {
            mockServer.enqueue(successResponse())

            apolloRepository.queryWithCache(TestQuery()).test {
                assertEquals(expectedData(), awaitItem())
                expectNoEvents()

                // perform separate request on the same resource, returning updated data
                mockServer.enqueue(successResponse(name = "Inquiry"))
                apolloRepository.query(TestQuery())

                // original flow receives new data
                assertEquals(expectedData(name = "Inquiry"), awaitItem())
                assertEquals(
                    emptyList(),
                    cancelAndConsumeRemainingEvents()
                )
            }
        }

    @Test
    fun `queryWithCache rate limits subsequent updateQuery`() =
        runBlocking {
            mockServer.enqueue(successResponse())
            mockServer.enqueue(successResponse(name = "Inquiry"))

            apolloRepository.queryWithCache(TestQuery()).test {
                assertEquals(expectedData(), awaitItem())
                expectNoEvents()

                // updateQuery is not executed because it happens too soon after queryWithCache
                apolloRepository.updateQuery(TestQuery())
                // delay for a bit to give the flow time to catch up. We do not have immediate dispatch in these tests.
                delay(50)

                // original flow receives no new data
                assertEquals(
                    emptyList(),
                    cancelAndConsumeRemainingEvents()
                )
            }
        }

    @Test
    fun `updateQuery rate limits same queries`() {
        mockServer.enqueue(successResponse())
        mockServer.enqueue(successResponse())

        // perform concurrent requests
        runBlocking {
            launch {
                apolloRepository.updateQuery(TestQuery())
            }
            launch {
                apolloRepository.updateQuery(TestQuery())
            }
        }

        assertEquals(1, mockServer.requestCount())
    }

    @Test
    fun `updateQuery does not rate limit different queries`() {
        mockServer.enqueue(successResponse())
        mockServer.enqueue(successResponse())

        // perform concurrent requests
        runBlocking {
            launch {
                apolloRepository.updateQuery(TestQuery())
            }
            launch {
                // This query does not fit the enqueued response. But we ignore the response anyway.
                apolloRepository.updateQuery(AnotherTestQuery())
            }
        }

        assertEquals(2, mockServer.requestCount())
    }

    @Test
    fun `updateQuery allows sequential queries if enough time has passed`() = runBlocking {
        mockServer.enqueue(successResponse())
        mockServer.enqueue(successResponse())

        apolloRepository.updateQuery(TestQuery())
        delay(500)
        apolloRepository.updateQuery(TestQuery())

        assertEquals(2, mockServer.requestCount())
    }

    @Test
    fun `query sends custom HTTP headers`() = runBlocking {
        mockServer.enqueue(successResponse())

        val customHeaders = mapOf(
            "Authorization" to "Bearer token123",
            "X-Custom-Header" to "custom-value"
        )

        apolloRepository.query(TestQuery(), customHeaders)

        val request = mockServer.takeRequest()
        assertEquals("Bearer token123", request.headers["Authorization"])
        assertEquals("custom-value", request.headers["X-Custom-Header"])
    }

    @Test
    fun `mutate sends custom HTTP headers`() = runBlocking {
        mockServer.enqueue(successResponse())

        val customHeaders = mapOf(
            "Authorization" to "Bearer token456",
            "X-Request-ID" to "req-123"
        )

        apolloRepository.query(TestQuery(), customHeaders)

        val request = mockServer.takeRequest()
        assertEquals("Bearer token456", request.headers["Authorization"])
        assertEquals("req-123", request.headers["X-Request-ID"])
    }

    @Test
    fun `updateQuery sends custom HTTP headers`() = runBlocking {
        val customHeaders = mapOf(
            "X-Update-Source" to "background-sync",
            "Cache-Control" to "no-cache"
        )

        mockServer.enqueue(successResponse())
        apolloRepository.updateQuery(TestQuery(), customHeaders)

        val request = mockServer.takeRequest()
        assertEquals("background-sync", request.headers["X-Update-Source"])
        assertEquals("no-cache", request.headers["Cache-Control"])
    }

    @Test
    fun `queryWithCache sends custom HTTP headers`() = runBlocking {
        val customHeaders = mapOf(
            "X-Client-Version" to "1.0.0",
            "Accept-Language" to "en-US"
        )

        mockServer.enqueue(successResponse())

        apolloRepository.queryWithCache(TestQuery(), httpHeaders = customHeaders).test {
            assertEquals(expectedData(), awaitItem())
            assertEquals(
                emptyList(),
                cancelAndConsumeRemainingEvents()
            )
        }

        val request = mockServer.takeRequest()
        assertEquals("1.0.0", request.headers["X-Client-Version"])
        assertEquals("en-US", request.headers["Accept-Language"])
    }

    @Test
    fun `queryWithCache with CacheFirst policy sends custom HTTP headers only on cache miss`() = runBlocking {
        mockServer.enqueue(successResponse())
        apolloRepository.query(TestQuery())

        val customHeaders = mapOf(
            "X-Cache-Strategy" to "cache-first",
            "Authorization" to "Bearer cache-token"
        )

        mockServer.enqueue(successResponse())

        apolloRepository.queryWithCache(TestQuery(), FetchPolicy.CacheFirst, customHeaders).test {
            assertEquals(expectedData(), awaitItem())
            assertEquals(
                emptyList(),
                cancelAndConsumeRemainingEvents()
            )
        }

        assertEquals(1, mockServer.requestCount())
    }

    @Test
    fun `default httpHeaders parameter works correctly`() = runBlocking {
        mockServer.enqueue(successResponse())

        apolloRepository.query(TestQuery())

        val request = mockServer.takeRequest()
        assertEquals("application/json", request.headers["Content-Type"])
    }

    /**
     * In reality, this type of introspection query will obviously always
     * return the same data. For testing purposes, let's pretend that the name
     * of the query type can change dynamically.
     */
    private fun successResponse(name: String = "Query"): MockResponse {
        return MockResponse.Builder()
            .statusCode(200)
            .body(
                """
                {
                  "data": {
                    "__schema": {
                      "__typename": "__Schema",
                      "queryType": {
                        "__typename": "__Type",
                        "name": "$name"
                      }
                    }
                  }
                }
                """.trimIndent()
            )
            .build()
    }

    /** @see [successResponse] */
    private fun expectedData(name: String = "Query") = Result.Success(
        TestQuery.Data(
            TestQuery.__Schema(
                __typename = "__Schema",
                TestQuery.QueryType(
                    __typename = "__Type",
                    name = name
                )
            )
        )
    )

    private fun errorResponse(): MockResponse {
        return MockResponse.Builder()
            .statusCode(500)
            .body("Internal server error")
            .build()
    }

    private fun MockServer.requestCount(): Int {
        var requestCount = 0
        try {
            while (true) {
                takeRequest()
                ++requestCount
            }
        } catch (_: Exception) {
        }
        return requestCount
    }
}
