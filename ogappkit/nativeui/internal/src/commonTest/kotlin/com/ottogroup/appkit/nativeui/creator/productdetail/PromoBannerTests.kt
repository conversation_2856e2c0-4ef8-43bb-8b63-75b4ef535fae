package com.ottogroup.appkit.nativeui.creator.productdetail

import app.cash.turbine.Event
import app.cash.turbine.test
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.ContentfulPromoBanner
import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldPromoBanner
import com.ottogroup.appkit.nativeui.model.ui.LoadingComponent
import com.ottogroup.appkit.nativeui.model.ui.PromoBanner
import com.ottogroup.appkit.test.testBannerContent
import com.ottogroup.appkit.test.testProduct
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.advanceUntilIdle

@OptIn(ExperimentalCoroutinesApi::class)
class PromoBannerTests {

    @Test
    fun `DynamicYieldPromoBanner displays successfully loaded banner content`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = DynamicYieldPromoBanner.Config()

            testObjects.creator.createScreen(
                testObjects.productFlow(testProduct),
                ComponentConfigs(listOf(config)),
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    successfulDetailScreenOf(),
                    awaitItem()
                )

                testObjects.fakePromoBannerRepository.dynamicYieldBanner.emit(
                    Result.Success(testBannerContent)
                )

                assertEquals(
                    successfulDetailScreenOf(
                        DynamicYieldPromoBanner(
                            state = LoadingComponent.State.Done(
                                content = testBannerContent,
                            ),
                            config = config,
                        )
                    ),
                    awaitItem()
                )
                cancelAndIgnoreRemainingEvents()
            }
        }

    @Test
    fun `DynamicYieldPromoBanner is omitted when banner content cannot be loaded`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = DynamicYieldPromoBanner.Config()

            testObjects.creator.createScreen(
                testObjects.productFlow(testProduct),
                ComponentConfigs(listOf(config)),
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    successfulDetailScreenOf(),
                    awaitItem()
                )

                testObjects.fakePromoBannerRepository.dynamicYieldBanner.emit(
                    Result.Failure<PromoBanner.Content>(IllegalStateException("Something went wrong"))
                )
                advanceUntilIdle()
                expectNoEvents()
            }
        }

    @Test
    fun `DynamicYieldPromoBanner is omitted for optimistic fake products`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = DynamicYieldPromoBanner.Config()

            testObjects.creator.createScreen(
                testObjects.productFlow(testProduct.copy(isOptimisticFake = true)),
                ComponentConfigs(listOf(config)),
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    successfulDetailScreenOf(),
                    awaitItem()
                )

                testObjects.fakePromoBannerRepository.dynamicYieldBanner.emit(
                    Result.Success(testBannerContent)
                )
                advanceUntilIdle()
                expectNoEvents()
            }
        }

    @Test
    fun `DynamicYieldPromoBanner is debounced`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = DynamicYieldPromoBanner.Config()

            testObjects.creator.createScreen(
                testObjects.productFlow(testProduct.copy(isOptimisticFake = true)),
                ComponentConfigs(listOf(config)),
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    successfulDetailScreenOf(),
                    awaitItem()
                )

                testObjects.fakePromoBannerRepository.dynamicYieldBanner.emit(
                    Result.Success(testBannerContent)
                )
                testObjects.updateProductFlow(testProduct.copy(id = "123123"))
                val newBannerContent = testBannerContent.copy(text = "new text")
                testObjects.fakePromoBannerRepository.dynamicYieldBanner.emit(
                    Result.Success(newBannerContent)
                )
                advanceUntilIdle()

                // expect only a single event with the latest banner content
                val events = cancelAndConsumeRemainingEvents()
                assertEquals(
                    listOf(
                        Event.Item(
                            successfulDetailScreenOf(
                                DynamicYieldPromoBanner(
                                    state = LoadingComponent.State.Done(
                                        content = newBannerContent,
                                    ),
                                    config = config,
                                )
                            )
                        )
                    ),
                    events
                )
            }
        }

    @Test
    fun `ContentfulPromoBanner displays successfully loaded banner content`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = ContentfulPromoBanner.Config

            testObjects.creator.createScreen(
                testObjects.productFlow(testProduct),
                ComponentConfigs(listOf(config)),
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    successfulDetailScreenOf(),
                    awaitItem()
                )

                testObjects.fakePromoBannerRepository.contentfulBanner.emit(
                    Result.Success(testBannerContent)
                )

                assertEquals(
                    successfulDetailScreenOf(
                        ContentfulPromoBanner(
                            state = LoadingComponent.State.Done(
                                content = testBannerContent,
                            ),
                            config = config,
                        )
                    ),
                    awaitItem()
                )
                cancelAndIgnoreRemainingEvents()
            }
        }

    @Test
    fun `ContentfulPromoBanner is omitted when banner content cannot be loaded`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = ContentfulPromoBanner.Config

            testObjects.creator.createScreen(
                testObjects.productFlow(testProduct),
                ComponentConfigs(listOf(config)),
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    successfulDetailScreenOf(),
                    awaitItem()
                )

                testObjects.fakePromoBannerRepository.contentfulBanner.emit(
                    Result.Failure<PromoBanner.Content>(IllegalStateException("Something went wrong"))
                )
                advanceUntilIdle()
                expectNoEvents()
            }
        }

    @Test
    fun `ContentfulPromoBanner is omitted for optimistic fake products`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = ContentfulPromoBanner.Config

            testObjects.creator.createScreen(
                testObjects.productFlow(testProduct.copy(isOptimisticFake = true)),
                ComponentConfigs(listOf(config)),
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    successfulDetailScreenOf(),
                    awaitItem()
                )

                testObjects.fakePromoBannerRepository.contentfulBanner.emit(
                    Result.Success(testBannerContent)
                )
                advanceUntilIdle()
                expectNoEvents()
            }
        }

    @Test
    fun `ContentfulPromoBanner is debounced`() =
        runProductDetailScreenCreatorTest { testObjects ->
            val config = ContentfulPromoBanner.Config

            testObjects.creator.createScreen(
                testObjects.productFlow(testProduct.copy(isOptimisticFake = true)),
                ComponentConfigs(listOf(config)),
                screenId = testObjects.screenId,
            ).test {
                assertEquals(
                    successfulDetailScreenOf(),
                    awaitItem()
                )

                testObjects.fakePromoBannerRepository.contentfulBanner.emit(
                    Result.Success(testBannerContent)
                )
                testObjects.updateProductFlow(testProduct.copy(id = "123123"))
                val newBannerContent = testBannerContent.copy(text = "new text")
                testObjects.fakePromoBannerRepository.contentfulBanner.emit(
                    Result.Success(newBannerContent)
                )
                advanceUntilIdle()

                val events = cancelAndConsumeRemainingEvents()
                assertEquals(
                    listOf(
                        Event.Item(
                            successfulDetailScreenOf(
                                ContentfulPromoBanner(
                                    state = LoadingComponent.State.Done(
                                        content = newBannerContent,
                                    ),
                                    config = config,
                                )
                            )
                        )
                    ),
                    events
                )
            }
        }
}
