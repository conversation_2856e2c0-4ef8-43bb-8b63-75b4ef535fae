package com.ottogroup.appkit.nativeui.api.witt.modelmapping

import com.ottogroup.appkit.nativeui.api.witt.WittProductId
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import kotlin.test.assertFailsWith

class WittProductIdTest {

    @Test
    fun `WittProductId parsing from simple product ID works correctly`() {
        val simpleId = "123456"
        val result = WittProductId.from(simpleId)
        val expectedProduct = WittProductId(
            productId = "123456",
            variantId = null,
            displayNumber = null,
            promotion = null
        )

        assertEquals(expectedProduct, result)
    }

    @Test
    fun `WittProductId parsing from complete product ID works correctly`() {
        val completeId = "123456--variant789--display123--promo456"
        val result = WittProductId.from(completeId)
        val expectedProduct = WittProductId(
            productId = "123456",
            variantId = "variant789",
            displayNumber = "display123",
            promotion = "promo456"
        )

        assertEquals(expectedProduct, result)
    }

    @Test
    fun `WittProductId toString with simple data works correctly`() {
        val simpleProduct = WittProductId(
            productId = "123456",
            variantId = null,
            displayNumber = null,
            promotion = null
        )

        assertEquals("123456------", simpleProduct.toString())
    }

    @Test
    fun `WittProductId toString with complete data works correctly`() {
        val completeProduct = WittProductId(
            productId = "123456",
            variantId = "variant789",
            displayNumber = "display123",
            promotion = "promo456"
        )

        assertEquals("123456--variant789--display123--promo456", completeProduct.toString())
    }

    @Test
    fun `WittProductId toString with partial data works correctly`() {
        val partialProduct = WittProductId(
            productId = "123456",
            variantId = "variant789",
            displayNumber = null,
            promotion = null
        )

        assertEquals("123456--variant789----", partialProduct.toString())
    }

    @Test
    fun `WittProductId isComplete returns true for complete product`() {
        val completeProduct = WittProductId(
            productId = "123456",
            variantId = "variant789",
            displayNumber = "display123",
            promotion = "promo456"
        )

        assertTrue(completeProduct.isComplete())
    }

    @Test
    fun `WittProductId isComplete returns false when missing variant`() {
        val missingVariantProduct = WittProductId(
            productId = "123456",
            variantId = null,
            displayNumber = "display123",
            promotion = "promo456"
        )

        assertFalse(missingVariantProduct.isComplete())
    }

    @Test
    fun `WittProductId isComplete returns false when missing display`() {
        val missingDisplayProduct = WittProductId(
            productId = "123456",
            variantId = "variant789",
            displayNumber = null,
            promotion = "promo456"
        )

        assertFalse(missingDisplayProduct.isComplete())
    }

    @Test
    fun `WittProductId isComplete returns false when missing promotion`() {
        val missingPromotionProduct = WittProductId(
            productId = "123456",
            variantId = "variant789",
            displayNumber = "display123",
            promotion = null
        )

        assertFalse(missingPromotionProduct.isComplete())
    }

    @Test
    fun `WittProductId parsing fails with invalid format`() {
        val invalidProductId = "123456--variant789--display123"

        assertFailsWith<IllegalArgumentException> {
            WittProductId.from(invalidProductId)
        }
    }
}
