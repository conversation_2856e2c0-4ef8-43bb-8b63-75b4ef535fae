package com.ottogroup.appkit.nativeui.api.witt.modelmapping

import com.ottogroup.appkit.nativeui.api.witt.data.MAIN_PRODUCT_ID
import com.ottogroup.appkit.nativeui.api.witt.data.TestProductDefinition
import com.ottogroup.appkit.nativeui.api.witt.data.createEuImportersData
import com.ottogroup.appkit.nativeui.api.witt.data.createQueryProduct
import com.ottogroup.appkit.nativeui.witt.ProductDetailQuery
import com.ottogroup.appkit.nativeui.witt.fragment.AvailabilityFields
import com.ottogroup.appkit.nativeui.witt.fragment.PriceFields
import com.ottogroup.appkit.nativeui.witt.type.AvailabilityState
import com.ottogroup.appkit.nativeui.witt.type.Currency
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull

class SizeMatrixTest {

    @Test
    fun `size matrix is created when product has multiple dimensions`() {
        val mapping = TestConfig.createStandardMapping()

        val queryData = createQueryProduct(
            TestProductDefinition(
                id = MAIN_PRODUCT_ID,
                name = "Multi-Size Product"
            )
        )

        val productBy = queryData.productBy.copy(
            dimensions = listOf(
                ProductDetailQuery.Dimension(
                    __typename = "Dimension",
                    id = "dimension1",
                    iid = null,
                    label = "Cup A",
                    promotion = "",
                    variants = listOf(
                        ProductDetailQuery.Variant(
                            __typename = "Variant",
                            id = "variant1",
                            iid = null,
                            hint = null,
                            promotion = "",
                            size = ProductDetailQuery.Size(
                                __typename = "Size",
                                description = null,
                                label = "36 A"
                            ),
                            availability = ProductDetailQuery.Availability1(
                                __typename = "Availability",
                                availabilityFields = AvailabilityFields(
                                    __typename = "Availability",
                                    state = AvailabilityState.available,
                                    quantity = 100,
                                    key = "available"
                                )
                            ),
                            price = ProductDetailQuery.Price1(
                                __typename = "Price",
                                priceFields = PriceFields(
                                    __typename = "Price",
                                    currency = Currency.EUR,
                                    withTax = 2999,
                                    savings = null,
                                    reductions = emptyList()
                                )
                            ),
                            attributes = emptyList()
                        )
                    )
                ),
                ProductDetailQuery.Dimension(
                    __typename = "Dimension",
                    id = "dimension2",
                    iid = null,
                    label = "Cup B",
                    promotion = "",
                    variants = listOf(
                        ProductDetailQuery.Variant(
                            __typename = "Variant",
                            id = "variant2",
                            iid = null,
                            hint = null,
                            promotion = "",
                            size = ProductDetailQuery.Size(
                                __typename = "Size",
                                description = null,
                                label = "36 B"
                            ),
                            availability = ProductDetailQuery.Availability1(
                                __typename = "Availability",
                                availabilityFields = AvailabilityFields(
                                    __typename = "Availability",
                                    state = AvailabilityState.available,
                                    quantity = 10,
                                    key = "available"
                                )
                            ),
                            price = ProductDetailQuery.Price1(
                                __typename = "Price",
                                priceFields = PriceFields(
                                    __typename = "Price",
                                    currency = Currency.EUR,
                                    withTax = 3299,
                                    savings = null,
                                    reductions = emptyList()
                                )
                            ),
                            attributes = emptyList()
                        )
                    )
                )
            )
        )

        val domainProduct = mapping.toProduct(
            queryProduct = productBy,
            siblings = listOf(productBy),
            reviews = emptyList(),
            rating = null,
            requestedId = "${MAIN_PRODUCT_ID}--variant1--123--",
            euImporters = createEuImportersData().euImporterByProduct ?: emptyList()
        )

        val sizeMatrix = domainProduct.sizeMatrix
        assertNotNull(sizeMatrix)
        assertEquals("Größe", sizeMatrix.parentDimensionName)
        assertEquals(2, sizeMatrix.entries.size)
    }

    @Test
    fun `size matrix is null when product has single dimension`() {
        val mapping = TestConfig.createStandardMapping()

        val queryData = createQueryProduct(
            TestProductDefinition(
                id = MAIN_PRODUCT_ID,
                name = "Single-Size Product"
            )
        )

        val domainProduct = mapping.toProduct(
            queryProduct = queryData.productBy,
            siblings = listOf(queryData.productBy),
            reviews = emptyList(),
            rating = null,
            requestedId = "${MAIN_PRODUCT_ID}--${MAIN_PRODUCT_ID}_variant--123--",
            euImporters = createEuImportersData().euImporterByProduct ?: emptyList()
        )

        assertNull(domainProduct.sizeMatrix)
    }
}
