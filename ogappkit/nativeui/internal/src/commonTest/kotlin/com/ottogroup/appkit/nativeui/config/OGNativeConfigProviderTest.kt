package com.ottogroup.appkit.nativeui.config

import app.cash.turbine.test
import com.ottogroup.appkit.base.http.NoOpCookiesBridge
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest

@OptIn(ExperimentalCoroutinesApi::class)
class OGNativeConfigProviderTest {

    @Test
    fun `provides current config state`() = runTest {
        val provider = OGNativeConfigProvider()

        provider.configState.test {
            assertEquals(
                OGNativeConfig.None(),
                awaitItem()
            )

            val newConfig = OGNativeConfig.Lascana(
                restBackend = OGNativeConfig.Backend(
                    url = "https://example.com/api",
                    headers = mapOf(
                        "Authorization" to "Basic dXNlcjpwYXNz"
                    ),
                ),
                cookiesBridge = NoOpCookiesBridge,
                graphQLBackend = OGNativeConfig.Backend(url = ""),
                dynamicYield = OGNativeConfig.Lascana.DynamicYield(apiKey = "", cookiesUrl = ""),
                productIdRegex = ""
            )
            provider.update(newConfig)

            assertEquals(
                newConfig,
                awaitItem()
            )
        }
    }
}
