package com.ottogroup.appkit.nativeui.api.witt.utils

import com.ottogroup.appkit.base.http.CookiesBridge

internal class TestWittCookiesBridge : CookiesBridge {
    var cookies: Map<String, String> = mapOf(
        "sessionToken" to "test-session-token-123",
        "recoSessionId" to "test-reco-session-id-456"
    )

    override suspend fun getCookies(url: String): Map<String, String> {
        return cookies
    }

    private val setCookies = mutableListOf<Pair<String, String>>()
    override suspend fun setCookie(url: String, cookie: String) {
        setCookies += url to cookie
    }
}
