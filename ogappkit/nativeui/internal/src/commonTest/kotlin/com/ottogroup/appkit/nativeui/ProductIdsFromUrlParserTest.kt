package com.ottogroup.appkit.nativeui

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertIs
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.runTest

class ProductIdsFromUrlParserTest {

    @Test
    fun `returns null if regex does not match`() = runTest {
        val parser = createParser(
            "^https:\\/\\/otto.de\\/.+-(?<productId>[0-9]+)\\.html?(?:\\?variantId=(?<variantId>[0-9]+))?\$",
        )
        val parsedIds = parser.parse(PRODUCT_VARIANT_URL)

        assertIs<Result.Failure<*>>(parsedIds)
    }

    @Test
    fun `parses product and variant IDs`() = runTest {
        val parser = createParser(
            "^https:\\/\\/stage-www-lascana-de-ottolasc\\.unbelievable-machine\\.net\\/.+-(?<productId>[0-9]+)\\.html?(?:\\?variantId=(?<variantId>[0-9]+))?\$",
        )
        val parsedIds = parser.parse(PRODUCT_VARIANT_URL)

        assertEquals(
            Result.Success(
                ProductIdsFromUrlParser.ProductIds(
                    productId = "687256409",
                    variantId = "**********",
                )
            ),
            parsedIds,
        )
    }

    @Test
    fun `parses product ID only`() = runTest {
        val parser = createParser(
            "^https:\\/\\/stage-www-lascana-de-ottolasc\\.unbelievable-machine\\.net\\/.+-(?<productId>[0-9]+)\\.html?(?:\\?variantId=(?<variantId>[0-9]+))?\$",
        )
        val parsedIds = parser.parse(PRODUCT_URL)

        assertEquals(
            Result.Success(
                ProductIdsFromUrlParser.ProductIds(
                    productId = "687256409",
                    variantId = null,
                )
            ),
            parsedIds,
        )
    }

    @Test
    fun `returns null if product ID match group has wrong name`() = runTest {
        val parser = createParser(
            "^https:\\/\\/stage-www-lascana-de-ottolasc\\.unbelievable-machine\\.net\\/.+-(?<productIdentifier>[0-9]+)\\.html?(?:\\?variantId=(?<variantId>[0-9]+))?\$",
        )
        val parsedIds = parser.parse(PRODUCT_VARIANT_URL)

        assertIs<Result.Failure<*>>(parsedIds)
    }

    @Test
    fun `returns only product ID if variant ID match group has wrong name`() = runTest {
        val parser = createParser(
            "^https:\\/\\/stage-www-lascana-de-ottolasc\\.unbelievable-machine\\.net\\/.+-(?<productId>[0-9]+)\\.html?(?:\\?variantId=(?<variantIdentifier>[0-9]+))?\$",
        )
        val parsedIds = parser.parse(PRODUCT_VARIANT_URL)

        assertEquals(
            Result.Success(
                ProductIdsFromUrlParser.ProductIds(
                    productId = "687256409",
                    variantId = null,
                )
            ),
            parsedIds,
        )
    }

    @Test
    fun `returns null if regex matches but product ID match could not be found`() = runTest {
        val parser = createParser(
            "^https:\\/\\/stage-www-lascana-de-ottolasc\\.unbelievable-machine\\.net\\/.+-(?<productId>[0-9]+)?\\.html?(?:\\?variantId=(?<variantId>[0-9]+))?\$",
        )
        val parsedIds =
            parser.parse("https://stage-www-lascana-de-ottolasc.unbelievable-machine.net/strandtop-lascana.html")

        assertIs<Result.Failure<*>>(parsedIds)
    }

    private fun TestScope.createParser(regex: String): ProductIdsFromUrlParser {
        val configProvider = OGNativeConfigProvider()

        val parser = ProductIdsFromUrlParser(
            configProvider = configProvider,
        )

        // update the config after the parser is created to reflect the possibility of SDK config coming in late
        configProvider.update(
            OGNativeConfig.None(productIdRegex = regex)
        )

        return parser
    }
}

private const val PRODUCT_VARIANT_URL =
    "https://stage-www-lascana-de-ottolasc.unbelievable-machine.net/strandtop-lascana-687256409.html?variantId=**********"
private const val PRODUCT_URL =
    "https://stage-www-lascana-de-ottolasc.unbelievable-machine.net/strandtop-lascana-687256409.html"
