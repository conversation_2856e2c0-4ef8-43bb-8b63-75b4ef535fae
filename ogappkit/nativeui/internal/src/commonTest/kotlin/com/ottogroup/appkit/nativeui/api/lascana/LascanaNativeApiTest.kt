package com.ottogroup.appkit.nativeui.api.lascana

import app.cash.turbine.test
import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.api.Mutation
import com.apollographql.apollo.api.Query
import com.apollographql.apollo.cache.normalized.FetchPolicy
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.api.ApolloRepository
import com.ottogroup.appkit.nativeui.api.NativeApi.CachePolicy
import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping.Companion.STOCK_STATUS_DELIVERABLE
import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping.Companion.STOCK_STATUS_DELIVERABLE_FEW_LEFT
import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping.Companion.STOCK_STATUS_NOT_IN_STOCK
import com.ottogroup.appkit.nativeui.api.lascana.data.buildMasterProductDetailQueryData
import com.ottogroup.appkit.nativeui.api.lascana.data.buildProductDetailQueryData
import com.ottogroup.appkit.nativeui.api.toFetchPolicy
import com.ottogroup.appkit.nativeui.lascana.MasterProductVariantsQuery
import com.ottogroup.appkit.nativeui.lascana.ProductDetailQuery
import com.ottogroup.appkit.nativeui.lascana.type.QueryBuilder
import com.ottogroup.appkit.nativeui.lascana.type.buildCurrency
import com.ottogroup.appkit.nativeui.lascana.type.buildPrice
import com.ottogroup.appkit.nativeui.lascana.type.buildProduct
import com.ottogroup.appkit.nativeui.lascana.type.buildProductStock
import com.ottogroup.appkit.nativeui.model.domain.Basket
import com.ottogroup.appkit.nativeui.model.domain.Wishlist
import com.ottogroup.appkit.test.TestCallVerifiable
import com.ottogroup.appkit.test.TestCallVerifier
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertIs
import kotlin.test.assertTrue
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.test.runTest

class LascanaNativeApiTest {

    private val apolloRepository = FakeApolloRepository()
    private val fakeWishlistApi = FakeWishlistApi()
    private val fakeBasketApi = FakeBasketApi()
    private fun createApi(coroutineScope: CoroutineScope) = LascanaNativeApiImpl(
        apolloRepository = apolloRepository,
        wishlistApi = fakeWishlistApi,
        basketApi = fakeBasketApi,
        coroutineScope = coroutineScope,
    )

    @Test
    fun `returns first cheapest available variant`() = runTest {
        setupProducts(
            TestVariantDefinition("Unavailable", STOCK_STATUS_NOT_IN_STOCK, 10.0),
            TestVariantDefinition("Expensive", STOCK_STATUS_DELIVERABLE, 100.0),
            TestVariantDefinition("Cheap 1", STOCK_STATUS_DELIVERABLE_FEW_LEFT, 10.0),
            TestVariantDefinition("Cheap 2", STOCK_STATUS_DELIVERABLE, 10.0),
            TestVariantDefinition("Cheap 3", STOCK_STATUS_DELIVERABLE, 10.0),
        )

        val api = createApi(coroutineScope = this)

        api.getProduct(parentResponse.product.id).test {
            val product = (awaitItem() as Result.Success).value
            assertEquals(
                "Cheap 1",
                product.title
            )
        }
    }

    @Test
    fun `returns first cheapest variant if none are available`() = runTest {
        setupProducts(
            TestVariantDefinition("Unavailable Expensive", STOCK_STATUS_NOT_IN_STOCK, 100.0),
            TestVariantDefinition("Unavailable Mid", STOCK_STATUS_NOT_IN_STOCK, 50.0),
            TestVariantDefinition("Unavailable Cheap 1", STOCK_STATUS_NOT_IN_STOCK, 10.0),
            TestVariantDefinition("Unavailable Cheap 2", STOCK_STATUS_NOT_IN_STOCK, 10.0),
        )

        val api = createApi(coroutineScope = this)

        api.getProduct(parentResponse.product.id).test {
            val product = (awaitItem() as Result.Success).value
            assertEquals(
                "Unavailable Cheap 1",
                product.title
            )
        }
    }

    @Test
    fun `returns parent product with no variants when explicitly requesting it`() = runTest {
        setupProducts()

        val api = createApi(coroutineScope = this)

        api.getProduct(parentResponse.product.id).test {
            val product = (awaitItem() as Result.Success).value
            assertEquals(
                parentResponse.product.id,
                product.id
            )
            // when there are no variants, we expect all dimensions to have no values
            product.individualDimensions.forEach { dimension ->
                assertTrue(dimension.values.isEmpty())
            }
        }
    }

    @Test
    fun `returns variant when explicitly requesting variant even if parent has no variants`() = runTest {
        setupProducts(
            TestVariantDefinition("Unavailable", STOCK_STATUS_NOT_IN_STOCK, 10.0),
        )
        // emulate the inconsistent backend situation where the parent does not actually report its variants
        parentMasterResponse = parentMasterResponse.copy(
            product = parentMasterResponse.product.copy(
                variants = emptyList()
            )
        )

        val api = createApi(coroutineScope = this)

        api.getProduct("0").test {
            val product = (awaitItem() as Result.Success).value
            product.individualDimensions.forEach { dimension ->
                assertTrue(dimension.values.isNotEmpty())
            }
        }
    }

    @Test
    fun `internal wishlist state is updated when requesting a product`() = runTest {
        setupProducts()
        val api = createApi(coroutineScope = this)

        val wishlist = Wishlist(listOf(Wishlist.Item("1"), Wishlist.Item("2")))
        fakeWishlistApi.wishlist = wishlist
        api.getWishlist().test {
            api.getProduct(parentResponse.product.id)
            assertEquals(
                Result.Success(wishlist),
                awaitItem()
            )

            val emptyWishlist = Wishlist(emptyList())
            fakeWishlistApi.wishlist = emptyWishlist
            api.getProduct(parentResponse.product.id)
            assertEquals(
                Result.Success(emptyWishlist),
                awaitItem()
            )
        }
    }

    @Test
    fun `internal wishlist state is not requested when requesting a product from cache only`() = runTest {
        setupProducts()
        val api = createApi(coroutineScope = this)

        val wishlist = Wishlist(listOf(Wishlist.Item("1"), Wishlist.Item("2")))
        fakeWishlistApi.wishlist = wishlist
        api.getWishlist().test {
            // initial wishlist update happens
            awaitItem()

            api.getProduct(parentResponse.product.id, cachePolicy = CachePolicy.CacheOnly)
            expectNoEvents()
        }
    }

    @Test
    fun `internal wishlist state is updated when directly modifying the wishlist`() = runTest {
        setupProducts()
        val api = createApi(coroutineScope = this)

        var latestExpectation: Wishlist
        api.getWishlist().test {
            // initially, the list is empty
            latestExpectation = Wishlist(emptyList())
            assertEquals(
                Result.Success(latestExpectation),
                awaitItem()
            )

            api.addProductToWishlist("1")

            latestExpectation = Wishlist(listOf(Wishlist.Item("1")))
            assertEquals(
                Result.Success(latestExpectation),
                awaitItem()
            )
            // verify the API is actually being used for this
            assertEquals(
                latestExpectation,
                fakeWishlistApi.wishlist
            )

            api.addProductToWishlist("2")
            latestExpectation = Wishlist(listOf(Wishlist.Item("1"), Wishlist.Item("2")))
            assertEquals(
                Result.Success(latestExpectation),
                awaitItem()
            )
            assertEquals(
                latestExpectation,
                fakeWishlistApi.wishlist
            )

            api.removeProductFromWishlist("1")
            latestExpectation = Wishlist(listOf(Wishlist.Item("2")))
            assertEquals(
                Result.Success(latestExpectation),
                awaitItem()
            )
            assertEquals(
                latestExpectation,
                fakeWishlistApi.wishlist
            )

            api.removeProductFromWishlist("2")
            latestExpectation = Wishlist(emptyList())
            assertEquals(
                Result.Success(latestExpectation),
                awaitItem()
            )
            assertEquals(
                latestExpectation,
                fakeWishlistApi.wishlist
            )
        }
    }

    @Test
    fun `internal wishlist state is updated when starting to observe it`() = runTest {
        setupProducts()
        val api = createApi(coroutineScope = this)

        fakeWishlistApi.wishlist = Wishlist(listOf(Wishlist.Item("1")))

        // access wishlist state to populate the internal cache
        api.getWishlist().test {
            cancelAndConsumeRemainingEvents()
        }

        // externally modify the wishlist
        val newWishlist = Wishlist(listOf(Wishlist.Item("1"), Wishlist.Item("2")))
        fakeWishlistApi.wishlist = newWishlist

        api.getWishlist().test {
            // re-observing the wishlist immediately delivers the externally updated state
            assertEquals(
                Result.Success(newWishlist),
                awaitItem()
            )
        }
    }

    @Test
    fun `internal basket state is updated when directly modifying the basket`() = runTest {
        setupProducts()
        val api = createApi(coroutineScope = this)

        var latestExpectation: Basket
        api.getBasket().test {
            // initially, the list is empty
            latestExpectation = Basket(emptyList())
            assertEquals(
                Result.Success(latestExpectation),
                awaitItem()
            )

            api.addProductToBasket("1")

            latestExpectation = Basket(listOf(Basket.Item("1", 1)))
            assertEquals(
                Result.Success(latestExpectation),
                awaitItem()
            )
            // verify the API is actually being used for this
            assertEquals(
                latestExpectation,
                fakeBasketApi.basket
            )

            api.addProductToBasket("2")
            latestExpectation = Basket(listOf(Basket.Item("1", 1), Basket.Item("2", 1)))
            assertEquals(
                Result.Success(latestExpectation),
                awaitItem()
            )
            assertEquals(
                latestExpectation,
                fakeBasketApi.basket
            )
        }
    }

    @Test
    fun `internal basket state is updated when directly modifying the basket by adding a voucher`() = runTest {
        setupProducts()
        val api = createApi(coroutineScope = this)

        var latestExpectation: Basket
        api.getBasket().test {
            // initially, the list is empty
            latestExpectation = Basket(emptyList())
            assertEquals(
                Result.Success(latestExpectation),
                awaitItem()
            )

            api.addVoucherToBasket("1", "Tim Apple")

            latestExpectation = Basket(listOf(Basket.Item("1", 1)))
            assertEquals(
                Result.Success(latestExpectation),
                awaitItem()
            )
            // verify the API is actually being used for this
            assertEquals(
                latestExpectation,
                fakeBasketApi.basket
            )
            assertEquals(
                "Tim Apple",
                fakeBasketApi.lastGiftCardName
            )
        }
    }

    @Test
    fun `internal basket state is updated when starting to observe it`() = runTest {
        setupProducts()
        val api = createApi(coroutineScope = this)

        fakeBasketApi.basket = Basket(listOf(Basket.Item("1", 2)))

        // access state to populate the internal cache
        api.getBasket().test {
            cancelAndConsumeRemainingEvents()
        }

        // externally modify the basket
        val newBasket = Basket(listOf(Basket.Item("1", 2), Basket.Item("2", 1)))
        fakeBasketApi.basket = newBasket

        api.getBasket().test {
            // re-observing the basket immediately delivers the externally updated state
            assertEquals(
                Result.Success(newBasket),
                awaitItem()
            )
        }
    }

    @Test
    fun `follow-up master query uses cache-only policy if initial request was cache-only`() = runTest {
        setupProducts(
            TestVariantDefinition("Cheap 1", STOCK_STATUS_DELIVERABLE_FEW_LEFT, 10.0),
        )

        createApi(coroutineScope = this).getProduct("0", cachePolicy = CachePolicy.CacheOnly).test { awaitItem() }

        assertEquals(2, apolloRepository.calls.size)
        val firstCall = apolloRepository.calls[0]
        assertIs<ProductDetailQuery>(firstCall.args[0])
        assertEquals(CachePolicy.CacheOnly.toFetchPolicy(), firstCall.args[1])

        val secondCall = apolloRepository.calls[1]
        assertIs<MasterProductVariantsQuery>(secondCall.args[0])
        assertEquals(CachePolicy.CacheOnly.toFetchPolicy(), secondCall.args[1])
    }

    @Test
    fun `follow-up master query uses cache-first policy if initial request was not cache-only`() = runTest {
        setupProducts(
            TestVariantDefinition("Cheap 1", STOCK_STATUS_DELIVERABLE_FEW_LEFT, 10.0),
        )

        createApi(coroutineScope = this).getProduct("0", cachePolicy = CachePolicy.CacheAndNetwork).test { awaitItem() }

        assertEquals(2, apolloRepository.calls.size)
        val firstCall = apolloRepository.calls[0]
        assertIs<ProductDetailQuery>(firstCall.args[0])
        assertEquals(CachePolicy.CacheAndNetwork.toFetchPolicy(), firstCall.args[1])

        val secondCall = apolloRepository.calls[1]
        assertIs<MasterProductVariantsQuery>(secondCall.args[0])
        assertEquals(CachePolicy.CacheFirst.toFetchPolicy(), secondCall.args[1])
    }

    @Test
    fun `follow-up variants query uses cache-only policy if initial request was cache-only`() = runTest {
        setupProducts(
            TestVariantDefinition("Cheap 1", STOCK_STATUS_DELIVERABLE_FEW_LEFT, 10.0),
        )

        createApi(coroutineScope = this)
            .getProduct(parentResponse.product.id, cachePolicy = CachePolicy.CacheOnly)
            .test {
                awaitItem()
            }

        assertEquals(2, apolloRepository.calls.size)
        val firstCall = apolloRepository.calls[0]
        assertIs<ProductDetailQuery>(firstCall.args[0])
        assertEquals(CachePolicy.CacheOnly.toFetchPolicy(), firstCall.args[1])

        val secondCall = apolloRepository.calls[1]
        assertIs<ProductDetailQuery>(secondCall.args[0])
        assertEquals(CachePolicy.CacheOnly.toFetchPolicy(), secondCall.args[1])
    }

    @Test
    fun `passed overrideProductId overrides returned product ID`() = runTest {
        setupProducts(
            TestVariantDefinition("Cheap 1", STOCK_STATUS_DELIVERABLE_FEW_LEFT, 10.0),
        )

        createApi(coroutineScope = this)
            .getProduct("0", overrideProductId = "123")
            .test {
                val product = (awaitItem() as Result.Success).value
                assertEquals(
                    "123",
                    product.id
                )
            }
    }
}

private lateinit var queryResponses: List<ProductDetailQuery.Data>
private lateinit var parentResponse: ProductDetailQuery.Data

/**
 * Required because a follow-up query for the parent uses a different model
 * than directly querying the parent.
 */
private lateinit var parentMasterResponse: MasterProductVariantsQuery.Data

private fun setupProducts(vararg variantDefinitions: TestVariantDefinition) {
    val builder: QueryBuilder.() -> Unit = {
        val products = variantDefinitions.mapIndexed { i, d ->
            buildProduct {
                id = "$i"
                title = d.name
                stock = buildProductStock { stockStatus = d.stockStatus }
                price = buildPrice {
                    price = d.price
                    currency = buildCurrency { name = "EUR" }
                }
                parentId = PARENT_ID
            }
        }

        queryResponses = products.map {
            buildProductDetailQueryData {
                product = it
            }
        }

        product = buildProduct {
            id = PARENT_ID
            parentId = ""
            variants = products
        }
    }
    parentResponse = buildProductDetailQueryData(block = builder)
    parentMasterResponse = buildMasterProductDetailQueryData(block = builder)
}

private data class TestVariantDefinition(val name: String, val stockStatus: Int, val price: Double)

private const val PARENT_ID = "parent"

private class FakeWishlistApi : LascanaWishlistApi {
    var wishlist: Wishlist = Wishlist(emptyList())
    override suspend fun getWishlist(): Result<Wishlist> = Result.Success(wishlist)
    override suspend fun addToWishlist(id: String): Result<Wishlist> {
        wishlist = wishlist.copy(items = wishlist.items + Wishlist.Item(id))
        return Result.Success(wishlist)
    }

    override suspend fun removeFromWishlist(id: String): Result<Wishlist> {
        wishlist = wishlist.copy(items = wishlist.items.filter { it.id != id })
        return Result.Success(wishlist)
    }
}

private class FakeBasketApi : LascanaBasketApi {
    var basket: Basket = Basket(emptyList())
    var lastGiftCardName: String? = null
    override suspend fun getBasket(): Result<Basket> = Result.Success(basket)
    override suspend fun addToBasket(id: String, giftCardName: String?): Result<Basket> {
        lastGiftCardName = giftCardName
        val existing = basket.items.find { it.id == id }
        val new = existing?.let { it.copy(amount = it.amount + 1) } ?: Basket.Item(id, 1)
        basket = basket.copy(items = basket.items.filter { it.id != id } + new)
        return Result.Success(basket)
    }
}

private class FakeApolloRepository : ApolloRepository, TestCallVerifiable by TestCallVerifier() {
    override suspend fun <T : Mutation.Data> mutate(
        mutation: Mutation<T>,
        httpHeaders: Map<String, String>
    ): Result<T> = error("Not implemented")
    override suspend fun <T : Query.Data> query(query: Query<T>, httpHeaders: Map<String, String>): Result<T> = error("Not implemented")
    override suspend fun <T : Query.Data> updateQuery(query: Query<T>, httpHeaders: Map<String, String>) = error("Not implemented")
    override fun <T : Query.Data> queryWithCache(
        query: Query<T>,
        fetchPolicy: FetchPolicy,
        httpHeaders: Map<String, String>
    ): Flow<Result<T>> {
        recordCall("queryWithCache", query, fetchPolicy)
        val requestedId = when (query) {
            is ProductDetailQuery -> query.productId
            is MasterProductVariantsQuery -> query.productId
            else -> throw IllegalArgumentException("Unsupported query: $query")
        }
        val data = if (requestedId == parentResponse.product.id) {
            when (query) {
                is ProductDetailQuery -> parentResponse
                is MasterProductVariantsQuery -> parentMasterResponse
                else -> throw IllegalArgumentException("Impossible to reach")
            }
        } else {
            queryResponses.find { it.product.id == requestedId } ?: error("Unsupported product id: $requestedId")
        }

        @Suppress("UNCHECKED_CAST")
        return MutableStateFlow(Result.Success(data as T))
    }

    override fun <T : Query.Data> queryWithCacheUsingClient(
        query: Query<T>,
        apolloClient: ApolloClient,
        fetchPolicy: FetchPolicy,
        httpHeaders: Map<String, String>
    ): Flow<Result<T>> {
        return queryWithCache(query, fetchPolicy, httpHeaders)
    }
}
