package com.ottogroup.appkit.nativeui.api

import app.cash.turbine.test
import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.api.NativeApi.CachePolicy
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.test.FakeNativeApiProvider
import com.ottogroup.appkit.test.assertNothingButComplete
import com.ottogroup.appkit.test.testProduct
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertIs
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.runTest

@OptIn(ExperimentalCoroutinesApi::class)
class ProductLoaderTest {

    private val nativeApiProvider = FakeNativeApiProvider()
    private val loader = ProductLoaderImpl(
        nativeApiProvider,
        LocalRecentlyViewedRepository()
    )

    private val testProduct1 = testProduct.copy(id = "1", title = "1")
    private val testProduct2 = testProduct.copy(id = "2", title = "2")

    @BeforeTest
    fun setup() {
        nativeApiProvider.products = mapOf(
            "1" to testProduct1,
            "2" to testProduct2,
        )
    }

    @Test
    fun `screen state is updated in place`() = runTest {
        loader.load(
            originalProductId = "1",
            id = "1",
            secondaryId = null,
            includeShopTheLook = true,
        ).test {
            // screen is loading, then displaying the first requested product
            assertEquals<Operation<Product>>(
                Operation.InProgress,
                awaitItem()
            )
            assertEquals<Operation<Product>>(
                Operation.Complete(
                    Result.Success(testProduct1),
                ),
                awaitItem()
            )
            // assert we have only done a single request for initial loading
            nativeApiProvider.verify(
                NativeApi::getProduct.name to listOf("1", CachePolicy.CacheAndNetwork),
            )

            assertNothingButComplete(this)
        }
        loader.load(
            originalProductId = "1",
            id = "2",
            secondaryId = null,
            includeShopTheLook = true,
        ).test {
            // after requesting an update, product is loading, then displaying a replica of the original product from cache, then the real second product
            assertEquals<Operation<Product>>(
                Operation.InProgress,
                awaitItem()
            )
            assertEquals<Operation<Product>>(
                Operation.Complete(
                    Result.Success(testProduct1.copy(id = "2", isOptimisticFake = true)),
                ),
                awaitItem()
            )
            assertEquals<Operation<Product>>(
                Operation.Complete(
                    Result.Success(testProduct2),
                ),
                awaitItem()
            )
            nativeApiProvider.verify(
                NativeApi::getProduct.name to listOf("1", CachePolicy.CacheAndNetwork),
                NativeApi::getProduct.name to listOf("1", CachePolicy.CacheOnly),
                NativeApi::getProduct.name to listOf("2", CachePolicy.CacheAndNetwork),
            )

            assertNothingButComplete(this)
        }
    }

    @Test
    fun `fallback to secondary id when primary product fails`() = runTest {
        nativeApiProvider.products = mapOf(
            "2" to testProduct2,
        )

        loader.load(
            originalProductId = "nonexistent",
            id = "nonexistent",
            secondaryId = "2",
            includeShopTheLook = true,
        ).test {
            // screen is loading
            assertEquals<Operation<Product>>(
                Operation.InProgress,
                awaitItem()
            )

            // after fallback, successfully loads the secondary product
            assertEquals<Operation<Product>>(
                Operation.Complete(
                    Result.Success(testProduct2),
                ),
                awaitItem()
            )

            assertNothingButComplete(this)
        }
    }

    @Test
    fun `both primary and secondary product fail`() = runTest {
        nativeApiProvider.products = emptyMap()

        loader.load(
            originalProductId = "nonexistent1",
            id = "nonexistent1",
            secondaryId = "nonexistent2",
            includeShopTheLook = true,
        ).test {
            // screen is loading
            assertEquals(
                Operation.InProgress,
                awaitItem()
            )

            // after both attempts fail, we get a failure result
            val failureItem = awaitItem()
            assertIs<Operation.Complete<Product>>(failureItem)
            assertIs<Result.Failure<Product>>(failureItem.result)

            nativeApiProvider.verify(
                NativeApi::getProduct.name to listOf("nonexistent1", CachePolicy.CacheAndNetwork),
                NativeApi::getProduct.name to listOf("nonexistent2", CachePolicy.CacheAndNetwork),
            )

            assertNothingButComplete(this)
        }
    }

    @Test
    fun `secondary id is null when primary fails - no fallback attempted`() = runTest {
        nativeApiProvider.products = emptyMap()

        loader.load(
            originalProductId = "nonexistent",
            id = "nonexistent",
            secondaryId = null,
            includeShopTheLook = true,
        ).test {
            // screen is loading
            assertEquals(
                Operation.InProgress,
                awaitItem()
            )

            // primary fails and no secondary to try, so we get failure immediately
            val failureItem = awaitItem()
            assertIs<Operation.Complete<Product>>(failureItem)
            assertIs<Result.Failure<Product>>(failureItem.result)

            nativeApiProvider.verify(
                NativeApi::getProduct.name to listOf("nonexistent", CachePolicy.CacheAndNetwork),
            )

            assertNothingButComplete(this)
        }
    }
}
