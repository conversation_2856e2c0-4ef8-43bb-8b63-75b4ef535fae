package com.ottogroup.appkit.nativeui.api.witt

import app.cash.turbine.test
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.api.witt.data.MAIN_PRODUCT_ID
import com.ottogroup.appkit.nativeui.api.witt.data.TestProductDefinition
import com.ottogroup.appkit.nativeui.api.witt.data.createEuImportersData
import com.ottogroup.appkit.nativeui.api.witt.data.createProductReviewsData
import com.ottogroup.appkit.nativeui.api.witt.data.createQueryProduct
import com.ottogroup.appkit.nativeui.api.witt.utils.FakeApolloRepository
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.model.domain.Basket
import com.ottogroup.appkit.nativeui.model.domain.Wishlist
import com.ottogroup.appkit.test.TestCookiesBridge
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.test.runTest

class WittNativeApiTest {
    private val apolloRepository = FakeApolloRepository()
    private val fakeWishlistApi = FakeWishlistApi()
    private val fakeBasketApi = FakeBasketApi()
    private val cookiesBridge = TestCookiesBridge()

    private val config = OGNativeConfig.Witt(
        graphQLBackend = OGNativeConfig.Backend("https://test.api"),
        productIdRegex = ".*",
        cookiesBridge = cookiesBridge,
        locale = "de-DE",
        cookies = OGNativeConfig.Witt.Cookies("https://test.api", "sessionToken"),
        webShopBaseUrl = "https://test.shop",
        displayPaybackPoints = true
    )

    private val configProvider = OGNativeConfigProvider().apply {
        update(config)
    }

    private val wittModelMapping = WittModelMapping(configProvider)

    private fun createApi(coroutineScope: CoroutineScope) = WittNativeApiImpl(
        apolloRepository = apolloRepository,
        wishlistApi = fakeWishlistApi,
        basketApi = fakeBasketApi,
        configProvider = configProvider,
        modelMapping = wittModelMapping,
        coroutineScope = coroutineScope,
    )

    private fun setupProducts(vararg productDefinitions: TestProductDefinition) {
        val siblingProducts = productDefinitions.filter { it.id != MAIN_PRODUCT_ID }

        val queryResponses = productDefinitions.associate { definition ->
            if (definition.id == MAIN_PRODUCT_ID) {
                definition.id to createQueryProduct(definition, siblingProducts)
            } else {
                definition.id to createQueryProduct(definition)
            }
        }

        val euImporterResponses = productDefinitions.associate { definition ->
            definition.id to createEuImportersData()
        }

        val reviewResponses = productDefinitions.associate { definition ->
            definition.id to createProductReviewsData(definition)
        }

        // Configure the shared apollo repository with the product responses
        apolloRepository.setupProductResponses(
            queryResponses = queryResponses,
            euImporterResponses = euImporterResponses,
            reviewResponses = reviewResponses
        )
    }

    @Test
    fun `returns product with siblings when product has siblings`() = runTest {
        setupProducts(
            TestProductDefinition(MAIN_PRODUCT_ID, "Main Product"),
            TestProductDefinition("sibling1", "Sibling 1"),
            TestProductDefinition("sibling2", "Sibling 2"),
        )

        val api = createApi(this)

        api.getProduct(MAIN_PRODUCT_ID).test {
            val product = (awaitItem() as Result.Success).value
            assertEquals("$MAIN_PRODUCT_ID--${MAIN_PRODUCT_ID}_variant0--000001001--001", product.id)
            assertEquals("Main Product", product.title)
        }
    }

    @Test
    fun `returns product without siblings when product has no siblings`() = runTest {
        setupProducts(
            TestProductDefinition(MAIN_PRODUCT_ID, "Main Product"),
        )

        val api = createApi(this)

        api.getProduct(MAIN_PRODUCT_ID).test {
            val product = (awaitItem() as Result.Success).value
            assertEquals("$MAIN_PRODUCT_ID--${MAIN_PRODUCT_ID}_variant0--000001001--001", product.id)
            assertEquals("Main Product", product.title)
        }
    }

    @Test
    fun `passed overrideProductId overrides returned product ID`() = runTest {
        setupProducts(
            TestProductDefinition(MAIN_PRODUCT_ID, "Main Product"),
        )

        val api = createApi(this)

        api.getProduct(MAIN_PRODUCT_ID, overrideProductId = "123").test {
            val product = (awaitItem() as Result.Success).value
            assertEquals("123--${MAIN_PRODUCT_ID}_variant0--000001001--001", product.id)
        }
    }

    @Test
    fun `internal wishlist state is updated when requesting a product`() = runTest {
        setupProducts(TestProductDefinition(MAIN_PRODUCT_ID, "Main Product"))
        val api = createApi(this)

        val wishlist = Wishlist(listOf(Wishlist.Item("1", "1"), Wishlist.Item("2", "2")))
        fakeWishlistApi.wishlist = wishlist
        api.getWishlist().test {
            api.getProduct(MAIN_PRODUCT_ID)
            assertEquals(Result.Success(wishlist), awaitItem())

            val emptyWishlist = Wishlist(emptyList())
            fakeWishlistApi.wishlist = emptyWishlist
            api.getProduct(MAIN_PRODUCT_ID)
            assertEquals(Result.Success(emptyWishlist), awaitItem())
        }
    }

    @Test
    fun `internal wishlist state is updated when directly modifying the wishlist`() = runTest {
        setupProducts(
            TestProductDefinition(MAIN_PRODUCT_ID, "Main Product"),
        )
        val api = createApi(this)

        var latestExpectation: Wishlist
        api.getWishlist().test {
            // initially, the list is empty
            latestExpectation = Wishlist(emptyList())
            assertEquals(Result.Success(latestExpectation), awaitItem())

            api.addProductToWishlist("1")

            latestExpectation = Wishlist(listOf(Wishlist.Item("1", "1")))
            assertEquals(Result.Success(latestExpectation), awaitItem())
            assertEquals(latestExpectation, fakeWishlistApi.wishlist)

            api.addProductToWishlist("2")
            latestExpectation = Wishlist(listOf(Wishlist.Item("1", "1"), Wishlist.Item("2", "2")))
            assertEquals(Result.Success(latestExpectation), awaitItem())
            assertEquals(latestExpectation, fakeWishlistApi.wishlist)

            api.removeProductFromWishlist("1")
            latestExpectation = Wishlist(listOf(Wishlist.Item("2", "2")))
            assertEquals(Result.Success(latestExpectation), awaitItem())
            assertEquals(latestExpectation, fakeWishlistApi.wishlist)

            api.removeProductFromWishlist("2")
            latestExpectation = Wishlist(emptyList())
            assertEquals(Result.Success(latestExpectation), awaitItem())
            assertEquals(latestExpectation, fakeWishlistApi.wishlist)
        }
    }

    @Test
    fun `internal wishlist state is updated when starting to observe it`() = runTest {
        setupProducts(
            TestProductDefinition(MAIN_PRODUCT_ID, "Main Product"),
        )
        val api = createApi(this)

        fakeWishlistApi.wishlist = Wishlist(listOf(Wishlist.Item("1", "1")))

        // access wishlist state to populate the internal cache
        api.getWishlist().test {
            cancelAndConsumeRemainingEvents()
        }

        // externally modify the wishlist
        val newWishlist = Wishlist(listOf(Wishlist.Item("1", "1"), Wishlist.Item("2", "2")))
        fakeWishlistApi.wishlist = newWishlist

        api.getWishlist().test {
            // re-observing the wishlist immediately delivers the externally updated state
            assertEquals(Result.Success(newWishlist), awaitItem())
        }
    }

    @Test
    fun `isProductOnWishlist returns correct state based on wishlist contents`() = runTest {
        setupProducts(
            TestProductDefinition(MAIN_PRODUCT_ID, "Main Product"),
        )
        val api = createApi(this)

        // Initially empty - but we need to trigger wishlist loading first
        api.getWishlist().test {
            awaitItem()
            cancelAndConsumeRemainingEvents()
        }

        api.isProductOnWishlist("1").test {
            assertEquals(Result.Success(false), awaitItem())
        }

        api.addProductToWishlist("1")

        api.isProductOnWishlist("1").test {
            assertEquals(Result.Success(true), awaitItem())
        }

        api.isProductOnWishlist("2").test {
            assertEquals(Result.Success(false), awaitItem())
        }
    }

    @Test
    fun `internal basket state is updated when directly modifying the basket`() = runTest {
        setupProducts(
            TestProductDefinition(MAIN_PRODUCT_ID, "Main Product"),
        )
        val api = createApi(this)

        var latestExpectation: Basket
        api.getBasket().test {
            // initially, the list is empty
            latestExpectation = Basket(emptyList())
            assertEquals(Result.Success(latestExpectation), awaitItem())

            api.addProductToBasket("1")

            latestExpectation = Basket(listOf(Basket.Item("1", 1)))
            assertEquals(Result.Success(latestExpectation), awaitItem())
            assertEquals(latestExpectation, fakeBasketApi.basket)

            api.addProductToBasket("2")
            latestExpectation = Basket(listOf(Basket.Item("1", 1), Basket.Item("2", 1)))
            assertEquals(Result.Success(latestExpectation), awaitItem())
            assertEquals(latestExpectation, fakeBasketApi.basket)
        }
    }

    @Test
    fun `internal basket state is updated when starting to observe it`() = runTest {
        setupProducts(
            TestProductDefinition(MAIN_PRODUCT_ID, "Main Product"),
        )
        val api = createApi(this)

        fakeBasketApi.basket = Basket(listOf(Basket.Item("1", 2)))

        // access state to populate the internal cache
        api.getBasket().test {
            cancelAndConsumeRemainingEvents()
        }

        // externally modify the basket
        val newBasket = Basket(listOf(Basket.Item("1", 2), Basket.Item("2", 1)))
        fakeBasketApi.basket = newBasket

        api.getBasket().test {
            // re-observing the basket immediately delivers the externally updated state
            assertEquals(Result.Success(newBasket), awaitItem())
        }
    }

    @Test
    fun `getReviews returns product reviews`() = runTest {
        setupProducts(
            TestProductDefinition(MAIN_PRODUCT_ID, "Main Product"),
        )
        val api = createApi(this)

        api.getReviews(MAIN_PRODUCT_ID).test {
            val reviews = (awaitItem() as Result.Success).value
            assertEquals(MAIN_PRODUCT_ID, reviews.id)
        }
    }
}

private class FakeWishlistApi : WittWishlistApi {
    var wishlist: Wishlist = Wishlist(emptyList())

    override suspend fun getWishlist(): Result<Wishlist> = Result.Success(wishlist)

    override suspend fun addToWishlist(id: String): Result<Wishlist> {
        wishlist = wishlist.copy(items = wishlist.items + Wishlist.Item(id, id))
        return Result.Success(wishlist)
    }

    override suspend fun removeFromWishlist(id: String): Result<Wishlist> {
        wishlist = wishlist.copy(items = wishlist.items.filter { it.id != id })
        return Result.Success(wishlist)
    }
}

private class FakeBasketApi : WittBasketApi {
    var basket: Basket = Basket(emptyList())

    override suspend fun getBasket(): Result<Basket> = Result.Success(basket)

    override suspend fun addToBasket(id: String): Result<Basket> {
        val existing = basket.items.find { it.id == id }
        val new = existing?.let { it.copy(amount = it.amount + 1) } ?: Basket.Item(id, 1)
        basket = basket.copy(items = basket.items.filter { it.id != id } + new)
        return Result.Success(basket)
    }
}
