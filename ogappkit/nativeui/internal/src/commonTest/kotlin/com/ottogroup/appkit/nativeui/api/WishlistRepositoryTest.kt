package com.ottogroup.appkit.nativeui.api

import app.cash.turbine.test
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.model.domain.Wishlist
import com.ottogroup.appkit.test.FakeNativeApiProvider
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.test.runTest

class WishlistRepositoryTest {

    private val nativeApiProvider = FakeNativeApiProvider()
    private fun createWishlistRepository(coroutineScope: CoroutineScope) = WishlistRepositoryImpl(
        nativeApiProvider,
        coroutineScope
    )

    @Test
    fun `add product operation is forwarded to API`() = runTest {
        val repository = createWishlistRepository(backgroundScope)
        nativeApiProvider.wishlist.value = Wishlist(listOf(Wishlist.Item("1")))

        val addResult = repository.addProductToWishlist("2")
        val expectation = Wishlist(listOf(Wishlist.Item("1"), Wishlist.Item("2")))
        assertEquals(
            Result.Success(expectation),
            addResult
        )
        assertEquals(
            expectation,
            nativeApiProvider.wishlist.value
        )
    }

    @Test
    fun `remove product operation is forwarded to API`() = runTest {
        val repository = createWishlistRepository(backgroundScope)
        nativeApiProvider.wishlist.value = Wishlist(listOf(Wishlist.Item("1"), Wishlist.Item("2")))

        val addResult = repository.removeProductFromWishlist("1")
        val expectation = Wishlist(listOf(Wishlist.Item("2")))
        assertEquals(
            Result.Success(expectation),
            addResult
        )
        assertEquals(
            expectation,
            nativeApiProvider.wishlist.value
        )
    }

    @Test
    fun `isProductOnWishlist updates when wishlist updates`() = runTest {
        val repository = createWishlistRepository(backgroundScope)

        repository.isProductOnWishlist("1").test {
            assertEquals(
                Result.Success(false),
                awaitItem()
            )

            nativeApiProvider.wishlist.value = Wishlist(listOf(Wishlist.Item("1")))
            assertEquals(
                Result.Success(true),
                awaitItem()
            )
        }
    }
}
