package com.ottogroup.appkit.nativeui.api.lascana

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.model.domain.Basket
import com.ottogroup.appkit.test.TestCookiesBridge
import io.ktor.client.engine.mock.MockEngine
import io.ktor.http.HttpMethod
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.test.runTest

class LascanaBasketApiTest {

    private val cookiesBridge = TestCookiesBridge()
    val config = OGNativeConfig.Lascana(
        restBackend = OGNativeConfig.Backend(
            url = "https://example.com/api",
            headers = mapOf(
                "Authorization" to "Basic dXNlcjpwYXNz"
            ),
        ),
        cookiesBridge = cookiesBridge,
        graphQLBackend = OGNativeConfig.Backend(url = ""),
        dynamicYield = OGNativeConfig.Lascana.DynamicYield(apiKey = "", cookiesUrl = ""),
        productIdRegex = ""
    )
    private val configProvider = OGNativeConfigProvider().apply {
        update(config)
    }

    private fun createApi(mockEngine: MockEngine): LascanaBasketApi {
        return LascanaBasketApiImpl(
            ogNativeConfigProvider = configProvider,
            httpClientProvider = object : LascanaHttpClientProvider {
                override val httpClient = lascanaHttpClient(
                    plugins = listOf(cookiesPlugin(configProvider)),
                    customEngine = mockEngine
                )
            }
        )
    }

    @Test
    fun `getBasket sends well-formed request`() = testApi(
        method = HttpMethod.Get,
        url = config.restBackend.url + "/graphql/getBasket/",
        headers = config.restBackend.headers,
        payload = "",
    ) { api ->
        assertEquals(
            Result.Success(Basket(listOf(Basket.Item(id = "687117781", amount = 2)))),
            api.getBasket()
        )
    }

    @Test
    fun `addToBasket sends well-formed request`() = testApi(
        method = HttpMethod.Post,
        url = config.restBackend.url + "/graphql/addToBasket/",
        headers = config.restBackend.headers,
        payload = goldenPayload("687117781"),
    ) { api ->
        assertEquals(
            Result.Success(Basket(listOf(Basket.Item(id = "687117781", amount = 2)))),
            api.addToBasket("687117781")
        )
    }

    @Test
    fun `addToBasket with custom name for voucher sends well-formed request`() = testApi(
        method = HttpMethod.Post,
        url = config.restBackend.url + "/graphql/addToBasket/",
        headers = config.restBackend.headers,
        payload = goldenPayloadForVoucher("687117781", "Tim Apple"),
    ) { api ->
        assertEquals(
            Result.Success(Basket(listOf(Basket.Item(id = "687117781", amount = 2)))),
            api.addToBasket("687117781", "Tim Apple")
        )
    }

    private fun testApi(
        method: HttpMethod,
        url: String,
        headers: Map<String, String>,
        payload: String,
        response: String = defaultResponse,
        block: suspend (LascanaBasketApi) -> Unit,
    ) = runTest {
        var error: Throwable? = null
        val api = createApi(assertingMockEngine(method, url, headers, payload, response) { error = it })
        block(api)

        assertEquals(
            listOf(
                url to "cookie1=value1",
                url to "cookie2=value2; expires=Fri, 14-Mar-2025 14:42:36 GMT",
            ),
            cookiesBridge.setCookies
        )

        error?.let { throw it }
    }
}

private val defaultResponse = """
    {
      "success": true,
      "itemCount": 2,
      "basketItems": [
        {
          "articleId": "687117781",
          "articleName": "Kurzarmpullover",
          "amount": 2
        }
      ]
    }
""".trimIndent()

private fun goldenPayload(id: String) = "{\"articleId\":\"$id\"}"
private fun goldenPayloadForVoucher(id: String, name: String) = "{\"articleId\":\"$id\", \"giftcardname\":\"$name\"}"
