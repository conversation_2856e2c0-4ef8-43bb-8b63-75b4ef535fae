package com.ottogroup.appkit.nativeui.api.witt.modelmapping

import com.ottogroup.appkit.nativeui.api.witt.data.MAIN_PRODUCT_ID
import com.ottogroup.appkit.nativeui.api.witt.data.TestProductDefinition
import com.ottogroup.appkit.nativeui.api.witt.data.createProductReviewsData
import com.ottogroup.appkit.nativeui.model.domain.ProductReviews
import com.ottogroup.appkit.nativeui.model.domain.Reviews
import com.ottogroup.appkit.nativeui.model.domain.Review
import com.ottogroup.appkit.nativeui.model.domain.Rating
import kotlin.test.Test
import kotlin.test.assertEquals

class ProductReviewsParsingTest {

    @Test
    fun `toProductReviews maps product with complete reviews and rating statistics correctly`() {
        val mapping = TestConfig.createStandardMapping()

        val reviewsData = createProductReviewsData(
            TestProductDefinition(
                id = MAIN_PRODUCT_ID,
                name = "Test Product"
            ),
            withReviews = true,
            withRatingStatistics = true
        )

        val result = mapping.toProductReviews(reviewsData)
        val expectedReviews = ProductReviews(
            id = MAIN_PRODUCT_ID,
            title = "Test Product",
            brandName = null,
            reviews = Reviews(
                rating = Rating(
                    averageRating = 4.5f,
                    count = 2,
                    ratingDistribution = mapOf(
                        5 to Pair(1, 50.0),
                        4 to Pair(1, 50.0)
                    )
                ),
                reviews = listOf(
                    Review(
                        rating = 5,
                        text = "I really love this shirt. The quality is excellent and it fits perfectly.",
                        title = "Great product!",
                        dateTime = kotlinx.datetime.Instant.parse("2024-01-15T10:30:00Z"),
                        reviewerName = "John D."
                    ),
                    Review(
                        rating = 4,
                        text = "Nice shirt for the price. Could be a bit softer but overall satisfied.",
                        title = "Good value",
                        dateTime = kotlinx.datetime.Instant.parse("2024-01-10T14:20:00Z"),
                        reviewerName = "Sarah M."
                    )
                ),
                writeReviewUrl = "customer#customerLoginModal"
            )
        )

        assertEquals(expectedReviews, result)
    }
}
