package com.ottogroup.appkit.nativeui.api.gkair

import app.cash.turbine.test
import app.cash.turbine.turbineScope
import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.api.witt.WittModelMapping
import com.ottogroup.appkit.nativeui.api.witt.WittProductId
import com.ottogroup.appkit.nativeui.api.witt.modelmapping.TestConfig
import com.ottogroup.appkit.nativeui.api.witt.utils.FakeApolloRepository
import com.ottogroup.appkit.nativeui.api.witt.utils.TestWittCookiesBridge
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.model.domain.Availability
import com.ottogroup.appkit.nativeui.model.domain.Dimension
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.ui.GkAirRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendations
import com.ottogroup.appkit.nativeui.witt.GkKAirRecosAfterAddToBasketQuery
import com.ottogroup.appkit.nativeui.witt.GkKAirRecosOnPdpQuery
import com.ottogroup.appkit.nativeui.witt.type.Currency
import com.ottogroup.appkit.nativeui.witt.type.QueryBuilder
import com.ottogroup.appkit.nativeui.witt.type.buildBrand
import com.ottogroup.appkit.nativeui.witt.type.buildGkAirOutput
import com.ottogroup.appkit.nativeui.witt.type.buildProduct
import com.ottogroup.appkit.nativeui.witt.type.buildProductImage
import com.ottogroup.appkit.nativeui.witt.type.buildProductPrice
import com.ottogroup.appkit.nativeui.witt.type.buildProductWithGkAirMetadata
import com.ottogroup.appkit.test.FakeOGTrackingConsent
import com.ottogroup.appkit.test.FakeWishlistRepository
import com.ottogroup.appkit.test.TestCallVerifiable
import com.ottogroup.appkit.test.assertNothingButComplete
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import kotlin.coroutines.cancellation.CancellationException
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertIs
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.test.runTest

class GkAirRepositoryTest {

    private val apolloRepository = FakeApolloRepository()
    private val cookiesBridge = TestWittCookiesBridge()
    private val config = TestConfig.createStandardConfig(cookiesBridge)
    private val configProvider = OGNativeConfigProvider().apply { update(config) }
    private val modelMapping = WittModelMapping(configProvider)
    private val wishlistRepository = FakeWishlistRepository()
    private val consent = FakeOGTrackingConsent()

    private fun createRepository(coroutineScope: CoroutineScope): GkAirRepository {
        return GkAirRepositoryImpl(
            apolloRepository = apolloRepository,
            configProvider = configProvider,
            modelMapping = modelMapping,
            wishlistRepository = wishlistRepository,
            trackingConsent = consent,
            coroutineScope = coroutineScope
        )
    }

    private val testProductId = WittProductId(
        productId = "123456",
        variantId = "abcdef",
        displayNumber = "999999001",
        promotion = "001",
    )

    @Test
    fun `getGkAirRecommendations returns failure if session cookie is missing`() = runTest {
        val repository = createRepository(this)
        cookiesBridge.cookies = emptyMap()

        repository.getGkAirRecommendations(
            productId = testProductId.toString(),
            categoryId = "9541",
            fusedDimensions = emptyList(),
            isAddToBasketSuccessScreen = false,
            config = GkAirRecommendations.Config(
                outputId = "1"
            )
        ).test {
            assertEquals(
                Operation.InProgress,
                awaitItem()
            )

            val item = awaitItem()
            assertIs<Operation.Complete<ProductRecommendations.Content>>(item)
            assertIs<Result.Failure<ProductRecommendations.Content>>(item.result)

            awaitComplete()
        }
    }

    @Test
    fun `getGkAirRecommendations returns specified reco from Apollo result on PDP`() = runTest {
        val repository = createRepository(backgroundScope)
        apolloRepository.queryResult = Result.Success(GkKAirRecosOnPdpQuery.Data(block = gkAirData))

        repository.getGkAirRecommendations(
            productId = testProductId.toString(),
            categoryId = "9541",
            fusedDimensions = emptyList(),
            isAddToBasketSuccessScreen = false,
            config = GkAirRecommendations.Config(
                outputId = "2"
            )
        ).test {
            assertEquals(
                Operation.InProgress,
                awaitItem()
            )

            assertEquals(
                Operation.Complete(
                    Result.Success(
                        ProductRecommendations.Content(
                            products = recoProducts.reversed(),
                            trackingId = "GKAir_Other_people_also_bought",
                            title = "Other people also bought",
                        )
                    )
                ),
                awaitItem()
            )

            wishlistRepository.addProductToWishlist("222222")

            assertEquals(
                Operation.Complete(
                    Result.Success(
                        ProductRecommendations.Content(
                            products = recoProducts.reversed().map { p ->
                                if (p.productId == "222222") {
                                    p.copy(isWishlisted = true)
                                } else p
                            },
                            trackingId = "GKAir_Other_people_also_bought",
                            title = "Other people also bought",
                        )
                    )
                ),
                awaitItem()
            )

            assertNothingButComplete(this)
        }

        apolloRepository.verify(
            "query" to listOf(
                GkKAirRecosOnPdpQuery(
                    productId = testProductId.productId,
                    categoryId = "C9541",
                    sessionId = "test-reco-session-id-456",
                    locale = "de-DE",
                    trackingConsent = true,
                )
            )
        )
    }

    @Test
    fun `getGkAirRecommendations returns specified reco from Apollo result after add to basket`() = runTest {
        val repository = createRepository(backgroundScope)
        apolloRepository.queryResult = Result.Success(GkKAirRecosAfterAddToBasketQuery.Data(block = gkAirData))
        consent.setGlobalConsent(true)
        consent.setConsentsForServices(mapOf(OGTrackingServiceId.GkAir to false))

        repository.getGkAirRecommendations(
            productId = testProductId.toString(),
            categoryId = "9541",
            fusedDimensions = listOf(
                Dimension(
                    name = WittModelMapping.FUSED_SIZE_DIMENSION_NAME,
                    type = Dimension.DimensionType.DEFAULT,
                    values = listOf(
                        Dimension.Value(
                            text = "S",
                            productId = "asdasd",
                            availability = Availability.Unknown,
                            price = Price.None,
                            thumbnailUrl = null,
                        ),
                        Dimension.Value(
                            text = "L",
                            productId = testProductId.toString(),
                            availability = Availability.Unknown,
                            price = Price.None,
                            thumbnailUrl = null,
                        )
                    )
                )
            ),
            isAddToBasketSuccessScreen = true,
            config = GkAirRecommendations.Config(
                outputId = "1"
            )
        ).test {
            assertEquals(
                Operation.InProgress,
                awaitItem()
            )

            assertEquals(
                Operation.Complete(
                    Result.Success(
                        ProductRecommendations.Content(
                            products = recoProducts,
                            trackingId = "GKAir_Possible_alternatives",
                            title = "Possible alternatives",
                        )
                    )
                ),
                awaitItem()
            )

            wishlistRepository.addProductToWishlist("111111")

            assertEquals(
                Operation.Complete(
                    Result.Success(
                        ProductRecommendations.Content(
                            products = recoProducts.map { p ->
                                if (p.productId == "111111") {
                                    p.copy(isWishlisted = true)
                                } else p
                            },
                            trackingId = "GKAir_Possible_alternatives",
                            title = "Possible alternatives",
                        )
                    )
                ),
                awaitItem()
            )

            assertNothingButComplete(this)
        }

        apolloRepository.verify(
            "query" to listOf(
                GkKAirRecosAfterAddToBasketQuery(
                    productId = testProductId.productId,
                    categoryId = "C9541",
                    size = "L",
                    sessionId = "test-reco-session-id-456",
                    locale = "de-DE",
                    trackingConsent = false,
                )
            )
        )
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    @Test
    fun `multiple near-simultaneous requests using the same query only perform single upstream request`() {
        try {
            runTest {
                val repository = createRepository(this)
                apolloRepository.queryResult = Result.Success(GkKAirRecosOnPdpQuery.Data(block = gkAirData))

                turbineScope {
                    val turbine1 = repository.getGkAirRecommendations(
                        productId = testProductId.toString(),
                        categoryId = "9541",
                        fusedDimensions = emptyList(),
                        isAddToBasketSuccessScreen = false,
                        config = GkAirRecommendations.Config(
                            outputId = "1"
                        )
                    ).testIn(backgroundScope)

                    // next call happens within debounce window
                    delay(500)
                    val turbine2 = repository.getGkAirRecommendations(
                        productId = testProductId.toString(),
                        categoryId = "9541",
                        fusedDimensions = emptyList(),
                        isAddToBasketSuccessScreen = false,
                        config = GkAirRecommendations.Config(
                            outputId = "2"
                        )
                    ).testIn(backgroundScope)

                    // next call happens after debounce window
                    delay(3000)

                    turbine1.cancelAndIgnoreRemainingEvents()
                    turbine2.cancelAndIgnoreRemainingEvents()
                    // until now, a single request happened
                    apolloRepository.verify(
                        "query" to listOf(
                            GkKAirRecosOnPdpQuery(
                                productId = testProductId.productId,
                                categoryId = "C9541",
                                sessionId = "test-reco-session-id-456",
                                locale = "de-DE",
                                trackingConsent = true,
                            )
                        )
                    )

                    val turbine3 = repository.getGkAirRecommendations(
                        productId = testProductId.toString(),
                        categoryId = "9541",
                        fusedDimensions = emptyList(),
                        isAddToBasketSuccessScreen = false,
                        config = GkAirRecommendations.Config(
                            outputId = "1"
                        )
                    ).testIn(backgroundScope)

                    delay(2000)
                    turbine3.cancelAndIgnoreRemainingEvents()
                    apolloRepository.verify(
                        expectedCalls = List(2) {
                            TestCallVerifiable.Call(
                                name = "query",
                                args = listOf(
                                    GkKAirRecosOnPdpQuery(
                                        productId = testProductId.productId,
                                        categoryId = "C9541",
                                        sessionId = "test-reco-session-id-456",
                                        locale = "de-DE",
                                        trackingConsent = true,
                                    )
                                )
                            )
                        }
                    )
                }

                /* The repository needs to use the test coroutine scope for this test (instead of the background scope),
                 * so that we can control the time. However, since we are creating SharedFlows on that scope, the
                 * test coroutine scope will never naturally complete. We need to cancel it manually while catching the
                 * exception to avoid the test failing.
                 * I hope there is a better way to do this, but I couldn't find one.
                 */
                cancel()
            }
        } catch (_: CancellationException) {
        }
    }

    private val gkAirData: QueryBuilder.() -> Unit = {
        val dummyProducts = (1..2).map {
            buildProductWithGkAirMetadata {
                product = buildProduct {
                    id = it.toString().repeat(6)
                    name = "Product $it"
                    brand = buildBrand {
                        name = "Test Brand"
                        price = buildProductPrice {
                            max = 2999
                            min = 2999
                            old = 3999
                            currency = Currency.EUR
                            priceRange = false
                        }
                        images = listOf(
                            buildProductImage {
                                hash = "image_hash"
                            }
                        )
                    }
                }
            }
        }
        gkAir = listOf(
            buildGkAirOutput {
                outputId = "1"
                title = "Possible alternatives"
                products = dummyProducts
            },
            buildGkAirOutput {
                outputId = "2"
                title = "Other people also bought"
                products = dummyProducts.reversed()
            },
        )
    }

    private val recoProducts = (1..2).map {
        ProductRecommendations.RecommendedProduct(
            productId = it.toString().repeat(6),
            secondaryId = null,
            brandName = "Test Brand",
            title = "Product $it",
            price = Price(
                currency = "EUR",
                value = 2999,
                oldValue = 3999,
            ),
            image = with(modelMapping) {
                Image(url = "image_hash".recoImageUrl, thumbnailUrl = "image_hash".thumbnailUrl)
            },
            isWishlisted = false,
            productIdForWishlisting = it.toString().repeat(6),
        )
    }
}
