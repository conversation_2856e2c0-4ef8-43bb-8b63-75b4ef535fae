package com.ottogroup.appkit.nativeui.api.witt.modelmapping

import com.ottogroup.appkit.nativeui.api.witt.WittModelMapping
import com.ottogroup.appkit.nativeui.api.witt.data.Availability
import com.ottogroup.appkit.nativeui.api.witt.data.DimensionData
import com.ottogroup.appkit.nativeui.api.witt.data.TestProductDefinition
import com.ottogroup.appkit.nativeui.api.witt.data.VariantData
import com.ottogroup.appkit.nativeui.api.witt.data.createQueryProducts
import com.ottogroup.appkit.nativeui.model.domain.Dimension
import com.ottogroup.appkit.nativeui.model.domain.Dimension.DimensionType
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.witt.type.AvailabilityState
import com.ottogroup.appkit.test.createDimensionValue
import kotlin.test.Test
import kotlin.test.assertEquals

class VariantConstructionTest {

    private val mapping = TestConfig.createStandardMapping()

    @Test
    fun `correctly constructs combined variants from three dimensions`() {
        val colors = listOf("blau", "rot")
        val cups = listOf("A", "B")
        val sizes = listOf("70", "80")

        val products = createQueryProducts(
            colors.map { color ->
                TestProductDefinition(
                    id = color,
                    name = color,
                    dimensions = cups.map {
                        DimensionData(
                            label = "Cup $it",
                            variants = sizes.map { size ->
                                VariantData(size = size)
                            }
                        )
                    }
                )
            }
        )

        val mainProduct = products.first()
        val siblings = products.drop(1)

        val domainProduct = mapping.toProduct(
            queryProduct = mainProduct,
            siblings = siblings,
            reviews = emptyList(),
            rating = null,
            requestedId = mainProduct.id,
            euImporters = emptyList()
        )
        val expectedDimensions = listOf(
            Dimension(
                name = WittModelMapping.COLOR_VARIANT_LABEL,
                type = DimensionType.COLOR,
                values = listOf(
                    createDimensionValue(
                        text = "blau",
                        productId = "blau--blau_variant0--000001001--001",
                        hasThumb = true,
                        thumbnailUrl = "https://cdn.witt.info/blau?brightness=0.97&width=256",
                        price = Price(
                            value = 2999,
                            currency = "EUR"
                        ),
                        availabilityMessage = "key"
                    ),
                    createDimensionValue(
                        text = "rot",
                        productId = "rot--rot_variant0--000001001--001",
                        hasThumb = true,
                        thumbnailUrl = "https://cdn.witt.info/rot?brightness=0.97&width=256",
                        price = Price(
                            value = 2999,
                            currency = "EUR"
                        ),
                        availabilityMessage = "key"
                    ),
                )
            ),
            Dimension(
                name = WittModelMapping.FUSED_SIZE_DIMENSION_NAME,
                type = DimensionType.DEFAULT,
                values = listOf(
                    createDimensionValue(
                        text = "70A",
                        productId = "blau--blau_variant0--000001001--001",
                        hasThumb = false,
                        price = Price(
                            value = 2999,
                            currency = "EUR"
                        ),
                        availabilityMessage = "key"
                    ),
                    createDimensionValue(
                        text = "80A",
                        productId = "blau--blau_variant1--000001001--001",
                        hasThumb = false,
                        price = Price(
                            value = 2999,
                            currency = "EUR"
                        ),
                        availabilityMessage = "key"
                    ),
                    createDimensionValue(
                        text = "70B",
                        productId = "blau--blau_variant0--000002001--001",
                        hasThumb = false,
                        price = Price(
                            value = 2999,
                            currency = "EUR"
                        ),
                        availabilityMessage = "key"
                    ),
                    createDimensionValue(
                        text = "80B",
                        productId = "blau--blau_variant1--000002001--001",
                        hasThumb = false,
                        price = Price(
                            value = 2999,
                            currency = "EUR"
                        ),
                        availabilityMessage = "key"
                    ),
                )
            ),
        )
        assertEquals(
            expectedDimensions,
            domainProduct.individualDimensions
        )
    }

    @Test
    fun `filters out unavailable variants from combined dimensions`() {
        val colors = listOf("blau", "rot")

        val products = createQueryProducts(
            colors.map { color ->
                TestProductDefinition(
                    id = color,
                    name = color,
                    dimensions = listOf(
                        DimensionData(
                            label = "Cup A",
                            variants = listOf(
                                VariantData(size = "70"),
                                VariantData(
                                    size = "80",
                                    availability = Availability(
                                        state = AvailabilityState.outOfStock,
                                        quantity = 0,
                                    )
                                ),
                            )
                        ),
                        DimensionData(
                            label = "Cup B",
                            variants = listOf(
                                VariantData(size = "70"),
                                VariantData(size = "80"),
                            )
                        ),
                    )
                )
            }
        )

        val mainProduct = products.first()
        val siblings = products.drop(1)

        val domainProduct = mapping.toProduct(
            queryProduct = mainProduct,
            siblings = siblings,
            reviews = emptyList(),
            rating = null,
            requestedId = mainProduct.id,
            euImporters = emptyList()
        )
        val expectedDimensions = listOf(
            Dimension(
                name = WittModelMapping.COLOR_VARIANT_LABEL,
                type = DimensionType.COLOR,
                values = listOf(
                    createDimensionValue(
                        text = "blau",
                        productId = "blau--blau_variant0--000001001--001",
                        hasThumb = true,
                        thumbnailUrl = "https://cdn.witt.info/blau?brightness=0.97&width=256",
                        price = Price(
                            value = 2999,
                            currency = "EUR"
                        ),
                        availabilityMessage = "key"
                    ),
                    createDimensionValue(
                        text = "rot",
                        productId = "rot--rot_variant0--000001001--001",
                        hasThumb = true,
                        thumbnailUrl = "https://cdn.witt.info/rot?brightness=0.97&width=256",
                        price = Price(
                            value = 2999,
                            currency = "EUR"
                        ),
                        availabilityMessage = "key"
                    ),
                )
            ),
            Dimension(
                name = WittModelMapping.FUSED_SIZE_DIMENSION_NAME,
                type = DimensionType.DEFAULT,
                values = listOf(
                    createDimensionValue(
                        text = "70A",
                        productId = "blau--blau_variant0--000001001--001",
                        hasThumb = false,
                        price = Price(
                            value = 2999,
                            currency = "EUR"
                        ),
                        availabilityMessage = "key"
                    ),
                    createDimensionValue(
                        text = "70B",
                        productId = "blau--blau_variant0--000002001--001",
                        hasThumb = false,
                        price = Price(
                            value = 2999,
                            currency = "EUR"
                        ),
                        availabilityMessage = "key"
                    ),
                    createDimensionValue(
                        text = "80B",
                        productId = "blau--blau_variant1--000002001--001",
                        hasThumb = false,
                        price = Price(
                            value = 2999,
                            currency = "EUR"
                        ),
                        availabilityMessage = "key"
                    ),
                )
            ),
        )
        assertEquals(
            expectedDimensions,
            domainProduct.individualDimensions
        )
    }

    @Test
    fun `keeps colors with only unavailable variants with empty variants`() {
        val colors = listOf("blau", "rot")
        val cups = listOf("A", "B")
        val sizes = listOf("70", "80")

        val products = createQueryProducts(
            colors.map { color ->
                val availability = if (color == "blau") {
                    Availability(
                        state = AvailabilityState.outOfStock,
                        quantity = 0,
                    )
                } else {
                    Availability(
                        state = AvailabilityState.available,
                        quantity = 100,
                    )
                }
                TestProductDefinition(
                    id = color,
                    name = color,
                    dimensions = cups.map {
                        DimensionData(
                            label = "Cup $it",
                            variants = sizes.map { size ->
                                VariantData(size = size, availability = availability)
                            }
                        )
                    }
                )
            }
        )

        val mainProduct = products.first()
        val siblings = products.drop(1)

        val domainProduct = mapping.toProduct(
            queryProduct = mainProduct,
            siblings = siblings,
            reviews = emptyList(),
            rating = null,
            requestedId = mainProduct.id,
            euImporters = emptyList()
        )
        val expectedDimensions = listOf(
            Dimension(
                name = WittModelMapping.COLOR_VARIANT_LABEL,
                type = DimensionType.COLOR,
                values = listOf(
                    createDimensionValue(
                        text = "blau",
                        productId = "blau--blau_variant0--000001001--001",
                        hasThumb = true,
                        thumbnailUrl = "https://cdn.witt.info/blau?brightness=0.97&width=256",
                        price = Price(
                            value = 2999,
                            currency = "EUR"
                        ),
                        availabilityMessage = "key"
                    ),
                    createDimensionValue(
                        text = "rot",
                        productId = "rot--rot_variant0--000001001--001",
                        hasThumb = true,
                        thumbnailUrl = "https://cdn.witt.info/rot?brightness=0.97&width=256",
                        price = Price(
                            value = 2999,
                            currency = "EUR"
                        ),
                        availabilityMessage = "key"
                    ),
                )
            ),
            Dimension(
                name = WittModelMapping.FUSED_SIZE_DIMENSION_NAME,
                type = DimensionType.DEFAULT,
                values = listOf()
            ),
        )
        assertEquals(
            expectedDimensions,
            domainProduct.individualDimensions
        )
    }
}
