package com.ottogroup.appkit.test

import app.cash.turbine.Event
import app.cash.turbine.ReceiveTurbine
import kotlin.test.assertEquals
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.test.TestScope
import kotlinx.coroutines.test.advanceUntilIdle

@OptIn(ExperimentalCoroutinesApi::class)
internal suspend fun TestScope.assertNothingButComplete(turbine: ReceiveTurbine<*>) {
    advanceUntilIdle()
    val events = turbine.cancelAndConsumeRemainingEvents()
    // assert nothing else except possibly Complete happens
    assertEquals(
        emptyList(),
        events.filter { it !is Event.Complete },
        message = "Expected no events other than Complete, but got: $events"
    )
}
