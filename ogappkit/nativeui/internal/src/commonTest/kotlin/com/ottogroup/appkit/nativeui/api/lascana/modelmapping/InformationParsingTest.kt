package com.ottogroup.appkit.nativeui.api.lascana.modelmapping

import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping
import com.ottogroup.appkit.nativeui.api.lascana.data.complexTestQueryProduct
import com.ottogroup.appkit.nativeui.model.domain.ArticleStandards
import com.ottogroup.appkit.nativeui.model.domain.Information
import com.ottogroup.appkit.nativeui.model.domain.Link
import kotlin.test.Test
import kotlin.test.assertEquals

class InformationParsingTest {

    @Test
    fun `information is parsed correctly`() {
        val mapping = ModelMapping(fuseNonColorDimensionsInto = null)

        val domainProduct = mapping.toProduct(complexTestQueryProduct)
        val expectedInformation = Information(
            articleNumber = "123456",
            description = "This product is awesome & great!\nWinner of an award.",
            bulletPoints = listOf(
                "Stitched logo",
                "Machine washable",
                "Cool zipper",
            ),
            attributesTable = Information.AttributesTable(
                sections = listOf(
                    Information.AttributesTable.Section(
                        title = "Style",
                        entries = listOf(
                            Information.AttributesTable.Section.Entry(
                                key = "Look",
                                values = listOf("single-color", "stitched"),
                            ),
                            Information.AttributesTable.Section.Entry(
                                key = "Style",
                                values = listOf("casual & relaxed"),
                            ),
                        ),
                    ),
                    Information.AttributesTable.Section(
                        title = "Color",
                        entries = listOf(
                            Information.AttributesTable.Section.Entry(
                                key = "Color",
                                values = listOf("rainbow"),
                            ),
                        ),
                    ),
                )
            ),
            distributingCompanies = listOf(
                Information.DistributingCompany(
                    name = "ACME Corp. & Sons",
                    address = "Fäkestreet 123\n12345 Toωnsville, XY",
                    phone = "+1234567890",
                    email = "<EMAIL>",
                    url = "https://www.example.com",
                ),
                Information.DistributingCompany(
                    name = "Second Inc.",
                    address = "Streetlane 1\n54321 Secondtown, XY",
                    phone = "+1234567890",
                    email = "<EMAIL>",
                    url = "https://www.example.com",
                )
            ),
            documents = listOf(
                Link(text = "Manual", url = "https://example.com/files/manual.pdf"),
                Link(text = "Legal doc", url = "https://example.com/files/LEGAL.PDF"),
            ),
            articleStandards = ArticleStandards.StructuredSeals(
                listOf(ArticleStandards.Seal.RecycledMaterial)
            )
        )
        assertEquals(
            expectedInformation,
            domainProduct.information,
        )
    }
}
