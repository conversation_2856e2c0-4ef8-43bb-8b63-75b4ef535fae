package com.ottogroup.appkit.nativeui.api.witt

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.api.witt.utils.FakeApolloRepository
import com.ottogroup.appkit.nativeui.api.witt.utils.TestWittCookiesBridge
import com.ottogroup.appkit.nativeui.api.witt.utils.WishlistItemData
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.model.domain.Wishlist
import com.ottogroup.appkit.nativeui.witt.AddItemToWishlistMutation
import com.ottogroup.appkit.nativeui.witt.GetWishlistQuery
import com.ottogroup.appkit.nativeui.witt.RemoveItemFromWishlistMutation
import com.ottogroup.appkit.test.TestCallVerifiable
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.test.runTest

class WittWishlistApiTest {
    private val apolloRepository = FakeApolloRepository()
    private val cookiesBridge = TestWittCookiesBridge()

    private val config = OGNativeConfig.Witt(
        graphQLBackend = OGNativeConfig.Backend(
            url = "https://test.api",
            headers = mapOf("Authorization" to "Bearer test-token")
        ),
        productIdRegex = ".*",
        cookiesBridge = cookiesBridge,
        locale = "de-DE",
        cookies = OGNativeConfig.Witt.Cookies("https://test.api", "sessionToken"),
        webShopBaseUrl = "https://test.shop",
        displayPaybackPoints = true
    )

    private val configProvider = OGNativeConfigProvider().apply {
        update(config)
    }

    private fun createApi() = WittWishlistApiImpl(
        apolloRepository = apolloRepository,
        configProvider = configProvider
    )

    @Test
    fun `getWishlist returns wishlist with items`() = runTest {
        apolloRepository.setupGetWishlistResponse(
            wishlistItems = listOf(
                WishlistItemData("wishlist-item-1", "product-1"),
                WishlistItemData("wishlist-item-2", "product-2")
            )
        )

        val api = createApi()
        val result = api.getWishlist()

        assertTrue(result is Result.Success)
        val wishlist = result.value
        assertEquals(2, wishlist.items.size)
        assertEquals(Wishlist.Item("wishlist-item-1", "product-1"), wishlist.items[0])
        assertEquals(Wishlist.Item("wishlist-item-2", "product-2"), wishlist.items[1])

        apolloRepository.verify("query" to listOf(GetWishlistQuery("de-DE")))
    }

    @Test
    fun `getWishlist returns empty wishlist when no items`() = runTest {
        apolloRepository.setupGetWishlistResponse(wishlistItems = emptyList())

        val api = createApi()
        val result = api.getWishlist()

        assertTrue(result is Result.Success)
        assertEquals(0, result.value.items.size)

        apolloRepository.verify("query" to listOf(GetWishlistQuery("de-DE")))
    }

    @Test
    fun `getWishlist returns failure when repository fails`() = runTest {
        val error = RuntimeException("Network error")
        apolloRepository.setupQueryFailure(error)

        val api = createApi()
        val result = api.getWishlist()

        assertTrue(result is Result.Failure)
        assertEquals(error, result.failure)

        apolloRepository.verify("query" to listOf(GetWishlistQuery("de-DE")))
    }

    @Test
    fun `addToWishlist with valid product ID returns updated wishlist`() = runTest {
        val productId = "123--variant1--display1--promo1"
        val updatedWishlist = listOf(
            WishlistItemData("existing-item", "456"),
            WishlistItemData("new-wishlist-item", "123")
        )

        apolloRepository.setupWishlistAddSequence(updatedWishlist)

        val api = createApi()
        val result = api.addToWishlist(productId)

        assertTrue(result is Result.Success)
        val wishlist = result.value
        assertEquals(2, wishlist.items.size)
        assertEquals(Wishlist.Item("existing-item", "456"), wishlist.items[0])
        assertEquals(Wishlist.Item("new-wishlist-item", "123"), wishlist.items[1])

        apolloRepository.verify(
            listOf(
                TestCallVerifiable.Call(
                    "mutate",
                    listOf(AddItemToWishlistMutation(productId = "123", locale = "de-DE"))
                ),
                TestCallVerifiable.Call(
                    "query",
                    listOf(GetWishlistQuery("de-DE"))
                )
            ),
            exactMatch = true
        )
    }

    @Test
    fun `addToWishlist with simple product ID works`() = runTest {
        val productId = "123"
        val updatedWishlist = listOf(WishlistItemData("new-wishlist-item", "123"))

        apolloRepository.setupWishlistAddSequence(updatedWishlist)

        val api = createApi()
        val result = api.addToWishlist(productId)

        assertTrue(result is Result.Success)
        val wishlist = result.value
        assertEquals(1, wishlist.items.size)
        assertEquals(Wishlist.Item("new-wishlist-item", "123"), wishlist.items[0])

        apolloRepository.verify(
            listOf(
                TestCallVerifiable.Call(
                    "mutate",
                    listOf(AddItemToWishlistMutation(productId = "123", locale = "de-DE"))
                ),
                TestCallVerifiable.Call(
                    "query",
                    listOf(GetWishlistQuery("de-DE"))
                )
            ),
            exactMatch = true
        )
    }

    @Test
    fun `addToWishlist returns failure when mutation fails`() = runTest {
        val productId = "123"
        val error = RuntimeException("Network error")
        apolloRepository.setupMutationFailure(error)

        val api = createApi()
        val result = api.addToWishlist(productId)

        assertTrue(result is Result.Failure)
        assertEquals(error, result.failure)

        apolloRepository.verify(
            "mutate" to listOf(AddItemToWishlistMutation(productId = "123", locale = "de-DE"))
        )
        apolloRepository.verifyNone("query")
    }

    @Test
    fun `addToWishlist returns failure when getWishlist fails after successful mutation`() = runTest {
        val productId = "123"
        apolloRepository.setupAddToWishlistResponse()
        apolloRepository.setupQueryFailure(RuntimeException("Failed to get wishlist"))

        val api = createApi()
        val result = api.addToWishlist(productId)

        assertTrue(result is Result.Failure)
        assertTrue(result.failure is RuntimeException)
        assertEquals("Failed to get wishlist", result.failure.message)
    }

    @Test
    fun `removeFromWishlist returns updated wishlist`() = runTest {
        val itemId = "wishlist-item-1"
        val updatedWishlist = listOf(WishlistItemData("remaining-item", "456"))

        apolloRepository.setupWishlistRemoveSequence(updatedWishlist)

        val api = createApi()
        val result = api.removeFromWishlist(itemId)

        assertTrue(result is Result.Success)
        val wishlist = result.value
        assertEquals(1, wishlist.items.size)
        assertEquals(Wishlist.Item("remaining-item", "456"), wishlist.items[0])

        apolloRepository.verify(
            listOf(
                TestCallVerifiable.Call(
                    "mutate",
                    listOf(RemoveItemFromWishlistMutation(itemKey = "wishlist-item-1", locale = "de-DE"))
                ),
                TestCallVerifiable.Call(
                    "query",
                    listOf(GetWishlistQuery("de-DE"))
                )
            ),
            exactMatch = true
        )
    }

    @Test
    fun `removeFromWishlist returns failure when mutation fails`() = runTest {
        val itemId = "wishlist-item-1"
        val error = RuntimeException("Network error")
        apolloRepository.setupMutationFailure(error)

        val api = createApi()
        val result = api.removeFromWishlist(itemId)

        assertTrue(result is Result.Failure)
        assertEquals(error, result.failure)

        apolloRepository.verify(
            "mutate" to listOf(
                RemoveItemFromWishlistMutation(itemKey = "wishlist-item-1", locale = "de-DE")
            )
        )
        apolloRepository.verifyNone("query")
    }
}
