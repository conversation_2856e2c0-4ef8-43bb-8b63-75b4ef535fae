package com.ottogroup.appkit.test

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.nativeui.api.NativeApi
import com.ottogroup.appkit.nativeui.api.contentful.ContentfulRepository
import com.ottogroup.appkit.nativeui.model.domain.CompanyOfOrigin
import com.ottogroup.appkit.nativeui.model.domain.SizeTableData
import com.ottogroup.appkit.nativeui.model.ui.PromoBanner
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow

internal class FakeContentfulRepository : ContentfulRepository {
    val sizeTable: MutableSharedFlow<Result<SizeTableData>> = MutableSharedFlow()

    override fun getSizeTable(
        productMkz: String,
        companyOfOrigin: CompanyOfOrigin,
        cachePolicy: NativeApi.CachePolicy
    ): Flow<Operation<SizeTableData>> {
        return sizeTable.asOperation()
    }

    val banner: MutableSharedFlow<Result<PromoBanner.Content>> =
        MutableSharedFlow(replay = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)

    override fun getBanner(cachePolicy: NativeApi.CachePolicy): Flow<Operation<PromoBanner.Content>> {
        return banner.asOperation()
    }
}
