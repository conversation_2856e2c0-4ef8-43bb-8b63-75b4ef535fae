package com.ottogroup.appkit.nativeui.tracking.lascana

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.DY_ALTERNATIVE_ID_PARAMETER
import com.ottogroup.appkit.nativeui.api.NativeApi
import com.ottogroup.appkit.nativeui.api.NativeApi.CachePolicy
import com.ottogroup.appkit.nativeui.api.lascana.LascanaNativeApi
import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping
import com.ottogroup.appkit.nativeui.api.lascana.data.VariantData
import com.ottogroup.appkit.nativeui.model.domain.Flag
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.Rating
import com.ottogroup.appkit.nativeui.model.domain.Review
import com.ottogroup.appkit.nativeui.model.domain.Reviews
import com.ottogroup.appkit.nativeui.util.safeParentId
import com.ottogroup.appkit.test.FakeNativeApiProvider
import com.ottogroup.appkit.test.FakeOGTracking
import com.ottogroup.appkit.test.createTestProduct
import com.ottogroup.appkit.test.testProduct
import com.ottogroup.appkit.tracking.OGTracking
import com.ottogroup.appkit.tracking.event.CustomParameter
import com.ottogroup.appkit.tracking.event.ECommerceItem
import com.ottogroup.appkit.tracking.event.Interaction
import com.ottogroup.appkit.tracking.event.View
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.test.advanceTimeBy
import kotlinx.coroutines.test.runTest

@OptIn(ExperimentalCoroutinesApi::class)
class LascanaNativeTrackingTest {

    private val ogTracking = FakeOGTracking()
    private val nativeApi = TestLascanaNativeApi()
    private fun createLascanaNativeTracking(
        coroutineScope: CoroutineScope,
    ) = LascanaNativeTracking(
        ogTracking = ogTracking,
        nativeApi = nativeApi,
        coroutineScope = coroutineScope,
    )

    @Test
    fun `viewItem tracks event with some optional values`() = runTest {
        val tracking = createLascanaNativeTracking(backgroundScope)

        tracking.viewItem(testProduct)

        ogTracking.verify(
            OGTracking::track.name,
            listOf(
                View.ProductDetailViewItem(
                    item = testProductECommerceItem,
                    additionalParameters = mapOf(
                        "view_context" to CustomParameter("native_app"),
                        "product_availability" to CustomParameter("in_stock"),
                    ),
                )
            )
        )
    }

    @Test
    fun `viewItem tracks complete event`() = runTest {
        val tracking = createLascanaNativeTracking(backgroundScope)

        val testProduct = createTestProduct(
            variantLabels = listOf(ModelMapping.COLOR_VARIANT_LABEL, "Cup", "Size"),
            variantData = listOf(
                VariantData(listOf("White", "Cup B", "75")),
                VariantData(listOf("White", "Cup C", "80")),
                VariantData(listOf("White", "Cup C", "90")),
            ),
            fuseNonColorDimensionsInto = ModelMapping.FUSED_SIZE_DIMENSION_NAME
        ).copy(
            flags = listOf(Flag.Sale, Flag.New),
            reviews = Reviews(
                rating = Rating(
                    averageRating = 3.5f,
                    count = 2,
                    ratingDistribution = emptyMap(),
                ),
                reviews = listOf(
                    Review(
                        text = "Good product",
                        rating = 4,
                    ),
                    Review(
                        text = "Mid product",
                        rating = 3,
                    )
                ),
                writeReviewUrl = "/write-review",
            ),
            price = Price(
                currency = "EUR",
                value = 1999,
                oldValue = 2999,
            ),
            breadcrumbs = listOf("fashion", "lingerie", "bras")
        )
        tracking.viewItem(testProduct)

        ogTracking.verify(
            OGTracking::track.name,
            listOf(
                View.ProductDetailViewItem(
                    item = ECommerceItem(
                        name = "${testProduct.shortTitle}:${testProduct.safeParentId}",
                        id = testProduct.safeParentId,
                        price = testProduct.price.value / 100f,
                        currency = testProduct.price.currency,
                        discount = (testProduct.price.oldValue!! - testProduct.price.value) / 100f,
                        brand = testProduct.brand?.name,
                        category = "fashion::lingerie::bras",
                        category2 = "75B",
                        category3 = "White",
                        category4 = testProduct.id,
                        category5 = "sale | new",
                        variant = "White",
                        quantity = 1,
                        hasColors = false,
                        hasSizes = true,
                        additionalParameters = mapOf(
                            "custom_review_rating" to CustomParameter(3.5f),
                            "custom_review_number" to CustomParameter(2),
                            DY_ALTERNATIVE_ID_PARAMETER to CustomParameter(testProduct.sku),
                        )
                    ),
                    additionalParameters = mapOf(
                        "view_context" to CustomParameter("native_app"),
                        "product_availability" to CustomParameter("in_stock"),
                    ),
                )
            )
        )
    }

    @Test
    fun `addItemToCart fetches product details`() = runTest {
        val tracking = createLascanaNativeTracking(backgroundScope)
        nativeApi.nextProduct = testProduct

        tracking.addItemToCart(testProduct.id)
        advanceTimeBy(50)

        ogTracking.verify(
            OGTracking::track.name,
            listOf(
                Interaction.AddItemToCart(
                    item = testProductECommerceItem,
                    additionalParameters = mapOf("view_context" to CustomParameter("native_app")),
                )
            )
        )
    }

    @Test
    fun `addItemToWishlist fetches product details`() = runTest {
        val tracking = createLascanaNativeTracking(backgroundScope)
        nativeApi.nextProduct = testProduct

        tracking.addItemToWishlist(testProduct.id)
        advanceTimeBy(50)

        ogTracking.verify(
            OGTracking::track.name,
            listOf(
                Interaction.AddItemToWishlist(
                    item = testProductECommerceItem,
                    additionalParameters = mapOf("view_context" to CustomParameter("native_app")),
                )
            ),
        )
    }

    @Test
    fun `getViewPromotionEvent returns assembled tracking event`() = runTest {
        val tracking = createLascanaNativeTracking(backgroundScope)

        val item = testProductECommerceItem
        val creativeName = "Test creative"
        val creativeSlot = "top_banner"
        val promotionId = "promo123"
        val promotionName = "Summer Sale"

        assertEquals(
            View.ProductDetailPromotion(
                item = item,
                creativeName = creativeName,
                creativeSlot = creativeSlot,
                promotionId = promotionId,
                promotionName = promotionName,
                additionalParameters = mapOf("view_context" to CustomParameter("native_app")),
            ),
            tracking.getViewPromotionEvent(item, creativeName, creativeSlot, promotionId, promotionName)
        )
    }

    @Test
    fun `getSelectPromotionEvent returns assembled tracking event`() = runTest {
        val tracking = createLascanaNativeTracking(backgroundScope)

        val item = testProductECommerceItem
        val creativeName = "Test creative"
        val creativeSlot = "top_banner"
        val promotionId = "promo123"
        val promotionName = "Summer Sale"

        assertEquals(
            Interaction.ProductDetailSelectPromotion(
                item = item,
                creativeName = creativeName,
                creativeSlot = creativeSlot,
                promotionId = promotionId,
                promotionName = promotionName,
                additionalParameters = mapOf("view_context" to CustomParameter("native_app")),
            ),
            tracking.getSelectPromotionEvent(item, creativeName, creativeSlot, promotionId, promotionName)
        )
    }
}

private class TestLascanaNativeApi : LascanaNativeApi, NativeApi by FakeNativeApiProvider().nativeApi {
    var nextProduct: Product? = null
    override fun getProduct(
        id: String,
        resolveVariant: Boolean,
        includeShopTheLook: Boolean,
        fetchPolicy: CachePolicy,
        overrideProductId: String?,
    ): Flow<Result<Product>> {
        return flowOf<Result<Product>>(
            try {
                require(fetchPolicy == CachePolicy.CacheFirst)
                Result.Success<Product>(nextProduct!!)
            } catch (t: Throwable) {
                Result.Failure<Product>(t)
            }
        )
    }
}

private val testProductECommerceItem = ECommerceItem(
    name = "${testProduct.shortTitle}:${testProduct.safeParentId}",
    id = testProduct.safeParentId,
    price = testProduct.price.value / 100f,
    currency = testProduct.price.currency,
    discount = (testProduct.price.oldValue!! - testProduct.price.value) / 100f,
    brand = testProduct.brand?.name,
    category = testProduct.breadcrumbs.joinToString("::"),
    category2 = null,
    category3 = "White",
    category4 = testProduct.id,
    category5 = "sale",
    variant = "White",
    quantity = 1,
    hasColors = true,
    hasSizes = false,
    additionalParameters = buildMap {
        testProduct.reviews?.rating?.averageRating?.let {
            put("custom_review_rating", CustomParameter(it))
        }
        testProduct.reviews?.reviews?.size?.let {
            put("custom_review_number", CustomParameter(it))
        }
        put(DY_ALTERNATIVE_ID_PARAMETER, CustomParameter(testProduct.sku))
    },
)
