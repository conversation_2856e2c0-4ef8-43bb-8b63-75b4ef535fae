package com.ottogroup.appkit.nativeui.api

import app.cash.turbine.test
import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.MoreFromTheSeries
import com.ottogroup.appkit.nativeui.model.domain.ShopTheLook
import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldRecommendations
import com.ottogroup.appkit.nativeui.model.ui.GkAirRecommendations
import com.ottogroup.appkit.nativeui.model.ui.MoreFromTheSeriesRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendations
import com.ottogroup.appkit.nativeui.model.ui.RecentlyViewedRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ShopTheLookRecommendations
import com.ottogroup.appkit.nativeui.model.ui.StaticProductRecommendations
import com.ottogroup.appkit.nativeui.util.safeParentId
import com.ottogroup.appkit.test.FakeDynamicYieldRepository
import com.ottogroup.appkit.test.FakeGKAirRepository
import com.ottogroup.appkit.test.FakeNativeApiProvider
import com.ottogroup.appkit.test.FakeWishlistRepository
import com.ottogroup.appkit.test.testProduct
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertIs
import kotlinx.coroutines.test.runTest

class RecommendationsRepositoryTest {

    private val fakeNativeApiProvider = FakeNativeApiProvider()
    private val fakeLocalRecentlyViewedRepository = LocalRecentlyViewedRepository()
    private val fakeDynamicYieldRepository = FakeDynamicYieldRepository()
    private val fakeGKAirRepository = FakeGKAirRepository()
    private val repository = RecommendationsRepositoryImpl(
        nativeApiProvider = fakeNativeApiProvider,
        localRecentlyViewedRepository = fakeLocalRecentlyViewedRepository,
        dynamicYieldRepository = fakeDynamicYieldRepository,
        gkAirRepository = fakeGKAirRepository,
        wishlistRepository = FakeWishlistRepository(),
    )

    @Test
    fun `getStaticRecommendations limits maximum number and filters failed results`() = runTest {
        val ids = listOf("1", "2", "3", "4", "5")
        val products = ids.map { testProduct.copy(id = it) }
        fakeNativeApiProvider.products = products.associateBy { it.id }

        repository.getStaticRecommendations(
            StaticProductRecommendations.Config(
                productIds = listOf("nonexistant") + ids,
                maxEntries = 3,
            )
        ).test {
            assertEquals(
                Operation.InProgress,
                awaitItem()
            )

            assertEquals(
                Operation.Complete(
                    Result.Success(
                        /* Item count is limited before doing the requests, so we are expecting to request
                         * the failing product + two successful ones */
                        ProductRecommendations.Content(
                            listOf(
                                testProduct.copy(id = "1").toRecommendedProduct(isWishlisted = false),
                                testProduct.copy(id = "2").toRecommendedProduct(isWishlisted = false),
                            ),
                            trackingId = StaticProductRecommendations.TRACKING_ID,
                        )
                    )
                ),
                awaitItem()
            )
        }
    }

    @Test
    fun `getRecentlyViewedRecommendations limits maximum number and filters current product and failed results`() =
        runTest {
            val ids = listOf("1", "2", "3", "4", "5")
            val products = ids.map { testProduct.copy(id = it, parentId = it) }
            // return an error for product "4"
            fakeNativeApiProvider.products = products.filter { it.id != "4" }.associateBy { it.id }

            products.forEach {
                fakeLocalRecentlyViewedRepository.addToRecentlyViewed(it.safeParentId)
            }

            repository.getRecentlyViewedRecommendations(
                productParentId = "5",
                RecentlyViewedRecommendations.Config(
                    maxEntries = 3,
                )
            ).test {
                assertEquals(
                    Operation.InProgress,
                    awaitItem()
                )

                assertEquals(
                    Operation.Complete(
                        Result.Success(
                            /* Current product is filtered out first, before limiting the count.
                             * Item count is limited before doing the requests, so we are expecting to request
                             * 4, 3, 2 of which 4 fails to load. */
                            ProductRecommendations.Content(
                                listOf(
                                    testProduct.copy(id = "3", parentId = "3")
                                        .toRecommendedProduct(isWishlisted = false),
                                    testProduct.copy(id = "2", parentId = "2")
                                        .toRecommendedProduct(isWishlisted = false),
                                ),
                                trackingId = RecentlyViewedRecommendations.TRACKING_ID,
                            )
                        )
                    ),
                    awaitItem()
                )
            }
        }

    @Test
    fun `getRecentlyViewedRecommendations returns empty result if nothing has been recently viewed`() =
        runTest {
            repository.getRecentlyViewedRecommendations(
                productParentId = "5",
                RecentlyViewedRecommendations.Config(
                    maxEntries = 3,
                )
            ).test {
                assertEquals(
                    Operation.InProgress,
                    awaitItem()
                )

                assertEquals(
                    Operation.Complete(
                        Result.Success(
                            ProductRecommendations.Content(
                                emptyList(),
                                trackingId = RecentlyViewedRecommendations.TRACKING_ID,
                            )
                        )
                    ),
                    awaitItem()
                )

                awaitComplete()
            }
        }

    @Test
    fun `getDynamicYieldRecommendations returns results from DY repository`() = runTest {
        val resultProducts = listOf(
            testProduct.copy(id = "1").toRecommendedProduct(isWishlisted = false),
            testProduct.copy(id = "2").toRecommendedProduct(isWishlisted = false),
        )
        repository.getDynamicYieldRecommendations(
            "123456",
            DynamicYieldRecommendations.Config(),
        ).test {
            assertEquals(
                Operation.InProgress,
                awaitItem()
            )

            val content = ProductRecommendations.Content(
                resultProducts,
                trackingId = "last_viewed",
            )
            fakeDynamicYieldRepository.recommendations.emit(
                Result.Success(content)
            )

            assertEquals(
                Operation.Complete(
                    Result.Success(content)
                ),
                awaitItem()
            )
        }
    }

    @Test
    fun `getGkAirRecommendations returns results from GKAir repository`() = runTest {
        val resultProducts = listOf(
            testProduct.copy(id = "1").toRecommendedProduct(isWishlisted = false),
            testProduct.copy(id = "2").toRecommendedProduct(isWishlisted = false),
        )
        repository.getGkAirRecommendations(
            "123456",
            "123",
            fusedDimensions = emptyList(),
            isAddToBasketSuccessScreen = false,
            GkAirRecommendations.Config("3"),
        ).test {
            assertEquals(
                Operation.InProgress,
                awaitItem()
            )

            val content = ProductRecommendations.Content(
                resultProducts,
                trackingId = "last_viewed",
            )
            fakeGKAirRepository.recommendations.emit(
                Result.Success(content)
            )

            assertEquals(
                Operation.Complete(
                    Result.Success(content)
                ),
                awaitItem()
            )
        }
    }

    @Test
    fun `getShopTheLookRecommendations returns error when passed ShopTheLook object is null`() =
        runTest {
            repository.getShopTheLookRecommendations(
                shopTheLook = null,
                ShopTheLookRecommendations.Config(),
            ).test {
                assertEquals(
                    Operation.InProgress,
                    awaitItem()
                )

                val item = awaitItem()
                assertIs<Operation.Complete<Result.Failure<*>>>(item)
                awaitComplete()
            }
        }

    @Test
    fun `getShopTheLookRecommendations returns results from passed ShopTheLook object and limits maximum number`() =
        runTest {
            val shopTheLook = ShopTheLook(
                image = Image("https://example.com/image.jpg", null),
                articles = listOf(
                    testProduct.copy(id = "1"),
                    testProduct.copy(id = "2"),
                    testProduct.copy(id = "3"),
                ),
            )
            repository.getShopTheLookRecommendations(
                shopTheLook,
                ShopTheLookRecommendations.Config(maxEntries = 2),
            ).test {
                assertEquals(
                    Operation.InProgress,
                    awaitItem()
                )

                assertEquals(
                    Operation.Complete(
                        Result.Success(
                            ProductRecommendations.Content(
                                listOf(
                                    testProduct.copy(id = "1").toRecommendedProduct(isWishlisted = false),
                                    testProduct.copy(id = "2").toRecommendedProduct(isWishlisted = false),
                                ),
                                image = shopTheLook.image,
                                trackingId = ShopTheLookRecommendations.TRACKING_ID,
                            )
                        )
                    ),
                    awaitItem()
                )
            }
        }

    @Test
    fun `getMoreFromTheSeriesRecommendations returns error when passed MoreFromTheSeries object is null`() =
        runTest {
            repository.getMoreFromTheSeriesRecommendations(
                moreFromTheSeries = null,
                MoreFromTheSeriesRecommendations.Config(),
            ).test {
                assertEquals(
                    Operation.InProgress,
                    awaitItem()
                )

                val item = awaitItem()
                assertIs<Operation.Complete<Result.Failure<*>>>(item)
                awaitComplete()
            }
        }

    @Test
    fun `getMoreFromTheSeriesRecommendations returns results from passed MoreFromTheSeries object and limits maximum number`() =
        runTest {
            val moreFromTheSeries = MoreFromTheSeries(
                articles = listOf(
                    testProduct.copy(id = "1"),
                    testProduct.copy(id = "2"),
                    testProduct.copy(id = "3"),
                ),
            )
            repository.getMoreFromTheSeriesRecommendations(
                moreFromTheSeries,
                MoreFromTheSeriesRecommendations.Config(maxEntries = 2),
            ).test {
                assertEquals(
                    Operation.InProgress,
                    awaitItem()
                )

                assertEquals(
                    Operation.Complete(
                        Result.Success(
                            ProductRecommendations.Content(
                                listOf(
                                    testProduct.copy(id = "1").toRecommendedProduct(isWishlisted = false),
                                    testProduct.copy(id = "2").toRecommendedProduct(isWishlisted = false),
                                ),
                                trackingId = ShopTheLookRecommendations.TRACKING_ID,
                            )
                        )
                    ),
                    awaitItem()
                )
            }
        }
}
