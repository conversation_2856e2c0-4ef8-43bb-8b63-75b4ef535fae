package com.ottogroup.appkit.nativeui.api.witt.data

import com.ottogroup.appkit.nativeui.witt.GetEuImportersQuery
import com.ottogroup.appkit.nativeui.witt.ProductDetailQuery
import com.ottogroup.appkit.nativeui.witt.ProductReviewsQuery
import com.ottogroup.appkit.nativeui.witt.type.AvailabilityState
import com.ottogroup.appkit.nativeui.witt.type.Currency
import com.ottogroup.appkit.nativeui.witt.type.ProductImageType
import com.ottogroup.appkit.nativeui.witt.type.buildAvailability
import com.ottogroup.appkit.nativeui.witt.type.buildBrand
import com.ottogroup.appkit.nativeui.witt.type.buildDimension
import com.ottogroup.appkit.nativeui.witt.type.buildPrice
import com.ottogroup.appkit.nativeui.witt.type.buildProduct
import com.ottogroup.appkit.nativeui.witt.type.buildProductAttribute
import com.ottogroup.appkit.nativeui.witt.type.buildProductImage
import com.ottogroup.appkit.nativeui.witt.type.buildProductPrice
import com.ottogroup.appkit.nativeui.witt.type.buildProductRatingDistribution
import com.ottogroup.appkit.nativeui.witt.type.buildProductRatingRatingValues
import com.ottogroup.appkit.nativeui.witt.type.buildProductRatingReview
import com.ottogroup.appkit.nativeui.witt.type.buildProductRatingReviewStatistic
import com.ottogroup.appkit.nativeui.witt.type.buildSibling
import com.ottogroup.appkit.nativeui.witt.type.buildSize
import com.ottogroup.appkit.nativeui.witt.type.buildVariant

internal data class TestProductDefinition(
    val id: String,
    val name: String,
    val dimensions: List<DimensionData> = listOf(
        DimensionData(
            label = "Size",
            variants = listOf(VariantData(size = "M"))
        ),
    ),
    val withAttributes: Boolean = false,
    val price: Int? = 2999,
    val oldPrice: Int = 0,
    val availability: Availability? = Availability(),
    val withImages: Boolean = true,
)

internal data class DimensionData(
    val label: String,
    val variants: List<VariantData>,
)

internal data class VariantData(
    val size: String,
    val availability: Availability = Availability(),
    // TODO add attributes?
)

internal data class Availability(
    val state: AvailabilityState = AvailabilityState.available,
    val quantity: Int = 100,
)

internal const val MAIN_PRODUCT_ID = "123456"

internal fun createQueryProducts(
    definitions: List<TestProductDefinition>,
): List<ProductDetailQuery.ProductBy> {
    return definitions.map { definition ->
        createQueryProduct(definition, definitions - definition)
    }.map { it.productBy }
}

internal fun createQueryProduct(
    definition: TestProductDefinition,
    siblingDefinitions: List<TestProductDefinition> = emptyList(),
): ProductDetailQuery.Data {
    return ProductDetailQuery.Data {
        productBy = buildProduct {
            id = definition.id
            canonicalId = definition.id
            sustainabilityLogos = emptyList()
            name = definition.name
            mainTitle = null
            brand = buildBrand {
                name = "TestBrand"
            }
            // siblings include the product itself
            siblings = (siblingDefinitions + definition).map { siblingDef ->
                buildSibling {
                    id = siblingDef.id
                    label = siblingDef.name
                    name = siblingDef.name
                    price = definition.price?.let {
                        buildProductPrice {
                            max = definition.price
                            min = definition.price
                            old = definition.oldPrice
                            currency = Currency.EUR
                            priceRange = false
                        }
                    }
                    availability = siblingDef.availability?.let {
                        buildAvailability {
                            quantity = siblingDef.availability.quantity
                            state = siblingDef.availability.state
                        }
                    }
                }
            }
            dimensions = definition.dimensions.mapIndexed { i, dimension ->
                val dimensionId = "00000${i + 1}"
                buildDimension {
                    id = dimensionId
                    promotion = "001"
                    label = dimension.label
                    variants = dimension.variants.mapIndexed { i, variant ->
                        buildVariant {
                            id = "${definition.id}_variant$i"
                            iid = "$dimensionId.$id"
                            promotion = "001"
                            size = buildSize {
                                label = variant.size
                            }
                            availability = buildAvailability {
                                state = variant.availability.state
                                quantity = variant.availability.quantity
                            }
                            price = buildPrice {
                                currency = Currency.EUR
                                withTax = 2999
                                reductions = emptyList()
                            }
                            attributes = emptyList()
                        }
                    }
                }
            }
            attributes = if (definition.withAttributes) {
                listOf(
                    buildProductAttribute {
                        key = "material"
                        value = "Cotton"
                    },
                    buildProductAttribute {
                        key = "care"
                        value = "Machine wash"
                    }
                )
            } else {
                emptyList()
            }
            breadcrumb = emptyList()
            images = if (definition.withImages) {
                listOf(
                    buildProductImage {
                        hash = definition.id
                        type = ProductImageType.product
                    }
                )
            } else {
                emptyList()
            }
        }
    }
}

internal fun createEuImportersData(): GetEuImportersQuery.Data {
    return GetEuImportersQuery.Data(
        euImporterByProduct = emptyList()
    )
}

internal fun createProductReviewsData(
    definition: TestProductDefinition,
    withReviews: Boolean = false,
    withRatingStatistics: Boolean = false
): ProductReviewsQuery.Data {
    return ProductReviewsQuery.Data {
        productBy = buildProduct {
            id = definition.id
            name = definition.name
            brand = null
        }
        productRatings = if (withReviews) {
            listOf(
                buildProductRatingReview {
                    title = "Great product!"
                    userNickname = "John D."
                    text = "I really love this shirt. The quality is excellent and it fits perfectly."
                    submissionTime = "2024-01-15T10:30:00Z"
                    rating = buildProductRatingRatingValues {
                        rating = 5.0
                    }
                },
                buildProductRatingReview {
                    title = "Good value"
                    userNickname = "Sarah M."
                    text = "Nice shirt for the price. Could be a bit softer but overall satisfied."
                    submissionTime = "2024-01-10T14:20:00Z"
                    rating = buildProductRatingRatingValues {
                        rating = 4.0
                    }
                }
            )
        } else {
            emptyList()
        }
        productRatingStatistics = if (withRatingStatistics) {
            buildProductRatingReviewStatistic {
                totalReviewCount = 2
                averageOverallRating = 4.5
                ratingDistribution = listOf(
                    buildProductRatingDistribution {
                        rating = 5
                        count = 1
                        percentage = 50.0
                    },
                    buildProductRatingDistribution {
                        rating = 4
                        count = 1
                        percentage = 50.0
                    }
                )
            }
        } else {
            null
        }
    }
}
