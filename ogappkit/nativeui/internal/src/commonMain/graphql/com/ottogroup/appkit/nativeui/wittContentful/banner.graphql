query Banner($locale: String) {
    bannerCollection(
        locale: $locale
        preview: false
        where: { # TODO clarify with <PERSON><PERSON> if this is the correct filter to use
            AND: [
                { pages_contains_some: ["all", "productDetailPage"]},
                { trigger_exists: false },
            ]
        }
    ) {
        items {
            modal {
                coupon {
                    code
                    hint
                }
            }
            text {
                json
            }
        }
    }
}
