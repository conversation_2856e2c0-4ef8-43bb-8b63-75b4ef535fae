query GetBasket($locale: String!) {
    basket(input: { locale: $locale }) {
        items {
            id
            quantity
        }
    }
}

mutation AddItemToBasket(
    $displayNumber: String!
    $variantId: ID!
    $productId: ID!
    $promotion: String!
    $locale: String!
) {
    addItemsToBasket(
        input: [
            {
                displayNumber: $displayNumber
                variantId: $variantId
                productId: $productId
                promotion: $promotion
                quantity: 1
            }
        ]
        locale: $locale
    ) {
        items {
            id
            quantity
        }
    }
}
