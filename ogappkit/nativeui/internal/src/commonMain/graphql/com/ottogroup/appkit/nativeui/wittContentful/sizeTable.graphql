query SizeTableCollection(
  $productMkz: String
  $locale: String
  $company: String
) {
  sizeTableCollection(
    locale: $locale
    preview: false
    where: {
      mkz_contains_all: [$productMkz]
      companyOfOrigin_contains_all: [$company]
    }
  ) {
    items {
      content {
        json
      }
      measuringAsset {
        url
      }
      measuringHeadline
      measuringIntroductionText
      measuringItemsCollection {
        items {
          title
          richtext {
            json
          }
        }
      }
      measuringTip {
        tipText
      }
    }
  }
}
