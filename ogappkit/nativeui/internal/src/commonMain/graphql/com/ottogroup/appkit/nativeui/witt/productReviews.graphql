query ProductReviews($productID: ID!, $locale: String!, $limit: Int = 100, $offset: Int = 0) {
    productBy(input: { id: $productID, locale: $locale }) {
        id
        name
        brand {
            name
        }
    }
    productRatings(productId: $productID, locale: $locale, limit: $limit, offset: $offset) {
        ...ProductRatingFields
    }

    productRatingStatistics(productId: $productID, locale: $locale) {
        ...ProductRatingStatisticsFields
    }
}
