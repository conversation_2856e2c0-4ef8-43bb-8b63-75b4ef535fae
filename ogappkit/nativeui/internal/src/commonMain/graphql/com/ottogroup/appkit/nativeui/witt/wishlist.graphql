query GetWishlist($locale: String!) {
    wishlist(input: { locale: $locale }) {
        id
        product {
            id
        }
    }
}
mutation AddItemToWishlist(
    $productId: ID!
    $locale: String!
) {
    addWishlistItem(input: { productId: $productId, locale: $locale }) {
        id
    }
}

mutation RemoveItemFromWishlist(
    $itemKey: ID!
    $locale: String!
) {
    removeWishlistItem(input: { itemKey: $itemKey, locale: $locale })
}

