type Query {
  version: String!

  """
  Loads the basket by the key from the context
  """
  basket(input: BasketInput!): Basket!

  """
  Load all non-hidden categories
  """
  navigation(input: NavigationInput!): [Category!]!

  """
  Load all categories
  """
  navigationIncludingHidden(input: NavigationInput!): [Category!]!

  """
  Load all shadow categories (including hidden)
  """
  shadowNavigation(input: NavigationInput!): [Category!]!

  """
  Load a category by either the ID or the path. For performance reasons, the ID is preferred
  """
  category(input: CategoryInput!): Category

  """
  Returns all information about the current client
  """
  client: ClientInfo!

  """
  Fetches all information about the current customer
  """
  customer(input: CustomerInput!): Customer!

  """
  Fetch email verification status of customer
  """
  emailVerificationStatus(input: EmailVerificationStatusInput!): EmailVerificationStatusReturn!

  """
  Fetch shipping costs flat status of customer
  """
  shippingCostsFlat(input: ShippingCostsFlatInput!): ShippingCostsFlatStatus

  """
  Fetches all information about an order
  """
  order(input: OrderInput!): Order!

  """
  Fetches a list of orders for a certain time range
  """
  orderOverview(input: OrderOverviewInput!): OrderOverview!

  """
  Fetches a list of deliveries from the last n days
  """
  deliveryOverview(input: DeliveryOverviewInput!): [Delivery!]!

  """
  Current account balance as a negative or positive number and the currency code
  """
  balance(input: BalanceInput!): Balance

  """
  Current maturities as Maturities object
  """
  maturities(input: MaturitiesInput!): Maturities

  """
  Fetches a list of invoices for a certain time range
  """
  invoiceOverview(input: InvoiceOverviewInput!): InvoiceOverview!

  """
  Returns the email address based on the checkout id
  """
  email(input: EmailInput!): String

  """
  Returns the newsletter status for a specific email address
  """
  newsletterStatus(input: NewsletterStatusInput!): NewsletterStatusReturn!

  """
  Returns the cached newsletter status for a specific email address (30 minutes cache)
  """
  newsletterCachedStatus(input: NewsletterCachedStatusInput!): NewsletterCachedStatusReturn

  """
  Returns the newsletter personal data for a specific email address
  """
  newsletterPersonalData(input: NewsletterPersonalDataInput!): NewsletterPersonalDataReturn!

  """
  Fetches all information about an order after checkout from AY
  """
  orderData(input: OrderDataInput!): OrderData!

  """
  Load products by id
  """
  productsByIds(input: ProductsByIdsInput!): [Product!]

  productById(input: ProductByIdInput!): Product!

  """
  Load a product by id, dimensionId or displayNumber
  """
  productBy(input: ProductByInput!): Product!

  """
  Load all catalogs
  """
  catalogs(input: productInCategoryArgs!): [Product!]!

  """
  Get the EU importers for one product. Necessary for ProdSV
  """
  euImporterByProduct(input: EuImporterByProductInput!): [EuImporter!]

  productRatingForm(productId: ID!, locale: String!): ProductRatingForm

  """
  Fetches product ratings for one product
  """
  productRatings(productId: ID!, locale: String!, limit: Int!, offset: Int!, filters: [RatingFilter!], sortOptions: [RatingSortOption!]): [ProductRatingReview!]

  """
  Fetches product rating statistics for one product
  """
  productRatingStatistics(productId: ID!, locale: String!, filters: [RatingFilter!]): ProductRatingReviewStatistic

  """
  Load a GK Air recommendation
  """
  gkAir(input: GkAirInput!): [GkAirOutput!]!

  """
  Search function for the search field, returns the corresponding search results depending on term
  """
  searchField(input: SearchFieldInput!): SearchFieldResult!

  """
  Suggestion for the current term
  """
  suggestion(input: SuggestionInput!): Suggestion!

  """
  Smarthub suggestion for the current term
  """
  smartSuggestion(input: SmartSuggestionInput!): SmartSuggestion!

  """
  Loads the wishlist
  """
  wishlist(input: WishlistInput!): [WishlistItem!]!
}

type Mutation {
  version: String!

  """
  Add one or multiple items to the basket
  """
  addItemsToBasket(input: [AddItemInput!]!, service: ServiceInput, locale: String!): Basket!

  """
  Removes the item with the given ID from the basket and returns the updated Basket
  """
  removeItemFromBasket(input: RemoveItemInput!, locale: String!): Basket!

  """
  Updates the item with the given ID and returns the updated Basket
  """
  updateItemInBasket(input: UpdateItemInput!, locale: String!): Basket!

  """
  Clears the basket (e.g. after checkout)
  """
  clearBasket(input: ClearBasketInput!): Basket!

  """
  Migrates the basket from the userId to the current basketKey (e.g. after login)
  """
  migrateBasket(input: MigrateBasketInput!): Basket!

  """
  Send the input from the contact form to the customer service
  """
  sendContactEmail(input: ContactInput!): ContactEmailResponse!

  """
  Send a request to delete the users account
  """
  accountDeletionRequest(input: AccountDeletionInput!): AccountDeletionResponse!

  """
  Send the input from the contact form to the customer service
  """
  submitCatalogOrder(input: CatalogOrderInput!): CatalogOrderResponse!

  """
  Requests email verification
  """
  verifyEmail(input: EmailVerificationInput!): EmailVerificationReturn!

  """
  Verifies an email address
  """
  verifyEmailAddress(input: VerifyEmailInput!): VerifyEmailReturn!

  """
  Subscibes an email address to the newsletter by adding it to the newsletter double opt-in process
  """
  subscribeToNewsletter(input: NewsletterSubscribeInput!): NewsletterSubscribeReturn!

  """
  Activates a newsletter subscription that has been in the double opt-in process
  """
  activateNewsletterSubscription(input: NewsletterActivateInput!): NewsletterActivateReturn!

  """
  Unsubscribes an email address from the newsletter
  """
  unsubscribeFromNewsletter(input: NewsletterUnsubscribeInput!): NewsletterUnsubscribeReturn!

  """
  Updates the personal data for a specific email address
  """
  newsletterUpdatePersonalData(input: NewsletterUpdatePersonalDataInput!): NewsletterUpdatePersonalDataReturn!

  """
  User can give feedback if review was helpful or not
  """
  productRatingReviewFeedback(id: ID!, isHelpful: Boolean!, locale: String!): ProductRatingApiResponseStatus

  """
  User can report review (false data or bad words)
  """
  productRatingReportReview(id: ID!, locale: String!): ProductRatingApiResponseStatus

  """
  User can submit a review for a product
  """
  productRatingFormSubmit(input: ProductRatingInput!): ProductRatingForm

  """
  Track a click on a product inside a GK Air recommendation
  """
  trackClickGkAir(input: TrackClickGkAirInput!): TrackingResponse!

  """
  Track all product's ids, quantities and totals when a purchase has been made
  """
  trackBasketGkAir(input: TrackBasketGkAirInput!): TrackingResponse!

  """
  Track all product's ids and quantities when a product is added to the basket
  """
  trackAddToBasketGkAir(input: TrackAddToBasketGkAirInput!): TrackingResponse!

  """
  Track product view event in GK Air
  """
  trackProductViewGkAir(input: TrackProductViewGkAirInput!): TrackingResponse!

  """
  Track search event in GK Air
  """
  trackSearchGkAir(input: TrackSearchGkAirInput!): TrackingResponse!

  """
  Track category view event in GK Air
  """
  trackCategoryViewGkAir(input: TrackCategoryViewGkAirInput!): TrackingResponse!

  """
  Track a click on a category inside a GK Air recommendation
  """
  trackCategoryClickGkAir(input: TrackCategoryClickGkAirInput!): TrackingResponse!

  """
  Add the item to the wishlist and returns the added item
  """
  addWishlistItem(input: AddWishlistItemInput!): WishlistItem!

  """
  Remove the item from the wishlist and returns the ID of the removed item
  """
  removeWishlistItem(input: RemoveWishlistItemInput!): String!

  """
  Migrates the Wishlist from the userId to the current wishlistKey
  """
  migrateWishList(input: MigrateWishListInput!): [WishlistItem!]!
}

"""
Representation of the status of one item in the basket
"""
enum ItemStatus {
  available

  cancelled

  deliverable

  unavailable

  undeliverable
}

"""
Representation of one item in the basket
"""
type BasketItem {
  """
  Identifier of a basket item
  """
  id: ID!

  """
  Color information
  """
  color: Color! @deprecated(reason: "Use product.color instead")

  """
  Price of the product multiplied with the quantity
  """
  price: Price!

  """
  Product information with name/description/images
  """
  product: Product!

  """
  Count of how many items of this product are in the basket
  """
  quantity: Int!

  """
  States the status of the item (available, deliverable ...)
  """
  status: ItemStatus!

  """
  Variant information with size/availability
  """
  variant: Variant!

  """
  Dimension information aligned with variant
  """
  dimension: Dimension!
}

"""
Basket with all products and the calculated price
"""
type Basket {
  """
  List of items in the basket
  """
  items: [BasketItem!]!

  """
  Sum of all basket item prices and the reductions
  """
  price: Price!

  """
  Number of all products in the basket
  """
  size: Int!

  """
  Number of all unique products in the basket
  """
  uniqueSize: Int!
}

"""
Input arguments for adding an item to the basket
"""
input AddItemInput {
  """
  Catalog ID for pricePromotionKey
  """
  catalogId: ID

  """
  Displaynumber of the item (e.g. 123456789)
  """
  displayNumber: String!

  """
  Variant ID for size information
  """
  variantId: ID!

  """
  Product ID for color information
  """
  productId: ID!

  """
  Promotion number of the item
  """
  promotion: String!

  """
  Quantity of one item
  """
  quantity: QuantityLimit!
}

scalar QuantityLimit

"""
Input arguments for removing an item from the basket
"""
input RemoveItemInput {
  """
  ID of the BasketItem
  """
  id: ID!
}

"""
Input arguments for updating an item in the basket
"""
input UpdateItemInput {
  """
  ID of the BasketItem
  """
  id: ID!

  """
  Catalog ID for pricePromotionKey
  """
  catalogId: ID

  """
  Displaynumber of the item (e.g. 123456789)
  """
  displayNumber: String!

  """
  Promotion number of the item
  """
  promotion: String!

  """
  Quantity of one item
  """
  quantity: QuantityLimit!

  """
  Variant ID for size information
  """
  variantId: ID!
}

input BasketInput {
  locale: String!
}

input ClearBasketInput {
  locale: String!
}

input MigrateBasketInput {
  locale: String!
}

"""
Input for services that could be added to the basket when buying an product
"""
input ServiceInput {
  """
  Quantity of the disposal services (e.g. mattress)
  """
  disposalQuantity: Int
}

"""
The Category type provides all neccessary information
for building the navigation tree and accessing ProductListingPages
"""
type Category {
  """
  Unique identifier for the category
  """
  id: ID!

  """
  Name of the category, which will be visible for the customer
  """
  name: String!

  """
  The endpoint / path in the URL, use it for category matching or linking to ProductListingPages
  """
  path: String!

  """
  A more specific category name, if provided, it will be used on the PLP instead of the cateogry name
  """
  title: String

  """
  Identifier of the parent category
  """
  parentId: ID

  """
  Flag for adding a highlight styling in the navigation
  """
  isHighlighted: Boolean!

  """
  List of nested categories (children)
  """
  children: [Category!]!

  """
  Icon of category
  """
  icon: String

  """
  Teaser of category
  """
  teaser: String

  """
  Category is used as content page title
  """
  isContentTitle: Boolean

  """
  Category is hidden
  """
  isHidden: Boolean

  """
  Category is shadow navigation
  """
  isShadowNavigation: Boolean

  """
  Category is a visual category
  """
  showAsVisualCategory: Boolean

  """
  Filters which should be active when accessing this page
  """
  activeFilters: [ActiveFilterType!]

  """
  A list of filter ids that should be visualized on the page
  """
  visualFilterIds: [String!]

  """
  List of supported filter keys
  """
  supportedFilters: [String!]

  """
  Load all filters for a category or search term
  """
  filters(input: FilterInput!): [Filter!]

  """
  Load filter values for the given filters
  """
  activeFilterList(input: ActiveFilterListInput!): [ActiveFilter!]

  """
  Extend the category type to optionally load products
  """
  products(input: productInCategoryArgs!): [Product!]

  """
  Extend the category type to optionaly load listing of products
  """
  productListing(input: ProductListingInput!): ProductListing
}

input CategoryInput {
  id: ID

  path: String

  locale: String!
}

input NavigationInput {
  locale: String!
}

"""
Gathered information about the client/user/customer
"""
type ClientInfo {
  """
  Client IP
  """
  ip: String!

  """
  Whether the user is logged in
  """
  isLoggedIn: Boolean!

  """
  Identifies a user on a device
  """
  userId: String!

  """
  Identifies a logged in customer in the AY system
  """
  checkoutId: String

  """
  BasketKey that is either the userId or the customerId
  """
  basketKey: String!
}

enum ContactReason {
  accessibility

  accountDeletion

  cancellation

  contactDataChange

  contradiction

  customerComplaint

  default

  deliveryInquiries

  fitGuarantee

  invoiceAccountBalance

  order

  others

  privacy

  productSecurity

  purchaseOrderChange

  requestCatalog

  requestNewsletter

  return

  unsubscribeCatalog

  unsubscribeNewsletter

  voucher
}

type ContactEmailResponse {
  success: Boolean!
}

input Subject {
  id: ContactReason!

  value: String!
}

input ContactInput {
  email: Email

  firstName: FirstName!

  lastName: LastName!

  customerNumber: CustomerNumber

  subject: Subject!

  message: Message!

  locale: String!
}

scalar Email

scalar FirstName

scalar LastName

scalar CustomerNumber

scalar Message

input AccountDeletionInput {
  subject: Subject!

  message: Message!

  locale: String!
}

type AccountDeletionResponse {
  success: Boolean!
}

type CatalogOrderResponse {
  success: Boolean!
}

input ItemsInput {
  productId: String!

  dimension: String!

  promotion: String!
}

input CatalogOrderInput {
  shippingAddress: ShippingAddressInput!

  email: Email

  items: [ItemsInput!]

  locale: String!
}

enum Gender {
  Male

  Female

  Other
}

type ShippingCostsFlat {
  active: Boolean!

  start: String!

  end: String!
}

type ShippingCostsFlatStatus {
  active: Boolean!
}

"""
All information about a customer
"""
type Customer {
  """
  Identifier for the customer
  """
  id: ID!

  """
  First name of the customer, optional if a customer only placed orders via telephone
  """
  firstName: String

  """
  Last name of the customer
  """
  lastName: String

  """
  Salutation of the customer
  """
  salutation: String

  """
  Customer number of the customer
  """
  customerNumber: Int

  """
  Gender of the customer
  """
  gender: Gender

  """
  Customer is potentially fraudulent, e.g. they where already submitted to a debt collection company
  """
  isFraudulent: Boolean

  """
  Flatrate for free shipping
  """
  shippingCostsFlat: ShippingCostsFlat

  """
  Email of the customer
  """
  email: String

  """
  Status of the email verification of the customer
  """
  emailVerificationStatus: String
}

enum EmailVerificationStatus {
  ACTIVE

  NO_CONSENT

  PENDING

  WITHDRAWN
}

input CustomerInput {
  locale: String!
}

type EmailVerificationStatusReturn {
  emailVerificationStatus: EmailVerificationStatus!
}

input EmailVerificationStatusInput {
  email: String!

  locale: String!
}

input ShippingCostsFlatInput {
  locale: String!
}

"""
Enum of all categories of fees for an order
"""
enum OrderFeeCategory {
  """
  Costs that will be applied to the order if the customer choses regular delivery
  """
  shipping

  """
  Costs that will be applied to the order if the customer choses express delivery
  """
  expressDelivery

  """
  Costs that will be applied to the order if the customer choses the payment method cash on delivery
  """
  cashOnDelivery

  """
  Costs that will be applied to the order if the customer choses an other service company to send the order
  """
  carrier

  """
  Costs that will be applied to the order if the customer choses the payment method paybreak that he has to pay later for the current order
  """
  paybreak

  """
  Costs that will be applied to the order if the customer choses the payment method credit and has to pay interest
  """
  installments
}

"""
Fee information for order
"""
type OrderFee {
  """
  Category of the fee
  """
  category: OrderFeeCategory!

  """
  Price of the fee
  """
  withTax: Int!

  """
  Reduction in percent price of the fee
  """
  relative: Int!
}

"""
Delivery provider that will be used
"""
enum DeliveryType {
  """
  Hermes (default)
  """
  hermes

  """
  DHL (fallback)
  """
  dhl

  """
  TNT (postal service in the netherlands)
  """
  tnt

  """
  Item is being sent directly by the vendor
  """
  direct

  """
  Bonprix
  """
  bonprix

  """
  CESKA POSTA (postal service in czech republic)
  """
  ceskaposta

  """
  Intern
  """
  intern

  """
  Post (postal service in germany)
  """
  post
}

"""
Information about the current state of the item delivery
"""
type Delivery {
  """
  Delivery provider
  """
  type: DeliveryType!

  """
  Localized name of the delivery provider
  """
  name: String!

  """
  Current step the delivery is in (1-5)
  """
  step: Int

  """
  Description for the current step
  """
  hint: String!

  """
  URL that points to the delivery provider system
  """
  trackingUrl: String
}

"""
The current state the Order is in (on the way, returned, ...)
"""
type OrderStatus {
  """
  Order status key e.g. completed
  """
  key: String

  """
  Order status value e.g. available
  """
  value: String

  """
  Order status color e.g. green
  """
  color: String
}

"""
An Item inside an Order
"""
type OrderItem {
  """
  Dimension with id and promotion
  """
  dimension: Dimension!

  """
  Identifier for a product
  """
  productId: ID

  """
  Identifier for the catalog that the product is sold in
  """
  catalogId: ID

  """
  Name of the product
  """
  name: String!

  """
  Size of the product including additional size information
  """
  size: String!

  """
  Color of the product
  """
  color: String!

  """
  Quantity of the product
  """
  quantity: Int!

  """
  Price of the product when it was ordered including reductions
  """
  price: Price!

  """
  Current images for the product
  """
  images: [ProductImage!]!

  """
  Current state of the Order
  """
  status: OrderStatus!

  """
  Invoice ID for the invoice PDF
  """
  invoiceId: ID

  """
  Sales Unit for a product, e.g. 5 Paar
  """
  salesUnit: String

  """
  Invoice number for the invoice PDF name
  """
  invoiceNumber: String

  """
  Information about the current delivery state
  """
  delivery: Delivery

  """
  Determines if ProductRating is active
  """
  productRatingActive: Boolean
}

"""
Price for an Order including reductions and detailed breakdown
"""
type OrderPrice {
  """
  Total price of the order including tax (what the customer paid)
  """
  withTax: Int!

  """
  Currency of the values used
  """
  currency: Currency!

  """
  Vouchers and other reductions (currently there are only vouchers)
  """
  reductions: [PriceReduction!]!

  """
  List of fees of the order
  """
  fees: [OrderFee!]!

  """
  Sum of all article
  """
  sumProduct: Int!
}

"""
A valid Shipping address
"""
input ShippingAddressInput {
  """
  Gender of the customer
  """
  gender: String

  """
  First name of the customer, optional if a customer only placed orders via telephone
  """
  firstName: FirstName!

  """
  Last name of the customer
  """
  lastName: LastName!

  """
  House number of the address
  """
  houseNumber: HouseNumber

  """
  Street of the address
  """
  street: Street!

  """
  Zip code of the city
  """
  zipCode: ZipCode!

  """
  City of the address
  """
  city: City!

  """
  Additional information (e.g. Room Nr. 43)
  """
  additional: String
}

scalar HouseNumber

scalar Street

scalar ZipCode

scalar City

"""
A valid Shipping address
"""
type ShippingAddress {
  """
  First name of the customer, optional if a customer only placed orders via telephone
  """
  firstName: String

  """
  Last name of the customer
  """
  lastName: String!

  """
  Salutation for the customer
  """
  salutation: String!

  """
  House number of the address
  """
  houseNumber: String!

  """
  Street of the address
  """
  street: String!

  """
  Zip code of the city
  """
  zipCode: String!

  """
  City of the address
  """
  city: String!

  """
  Additional information (e.g. Room Nr. 43)
  """
  additionalInformation: String
}

"""
All information about an order
"""
type Order {
  """
  Identifier for the order
  """
  id: ID!

  """
  Id that is unique across multiple pages
  """
  iid: ID

  """
  Date for when the customer ordered
  """
  orderDate: String!

  """
  Type of payment the customer used
  """
  paymentMethod: PaymentMethod

  """
  Type of delivery the customer chose
  """
  deliveryType: OrderDeliveryType!

  """
  The preferred date of delivery if the customer selected one
  """
  preferredDeliveryDate: String

  """
  Total price of the order including reductions and fees
  """
  price: OrderPrice

  """
  Shipping address for the order
  """
  shippingAddress: ShippingAddress

  """
  Items included in the order
  """
  items: [OrderItem!]!
}

"""
Type of delivery the customer chose
"""
type OrderDeliveryType {
  """
  Identifier for the order delivery type
  """
  id: ID!

  """
  Name of the order delivery type
  """
  value: String!
}

"""
Type of delivery the customer chose
"""
type PaymentMethod {
  """
  Identifier for the payment method
  """
  id: ID!

  """
  Name of the payment method
  """
  value: String!
}

"""
An Order overview with multiple orders and a pagination
"""
type OrderOverview {
  """
  Orders in the overview
  """
  orders: [Order!]!

  """
  Pagination information
  """
  page: Page!
}

type Maturity {
  dueDate: String!

  value: Int!

  currency: Currency!
}

type Maturities {
  maturities: [Maturity!]!

  total: Int!

  currency: Currency!
}

type Balance {
  balance: Int!

  currencyCode: Currency!
}

type InvoiceProductImage {
  hash: String!

  url: String

  attributes: ImageAttributes
}

type ImageAttributes {
  imageType: String
}

type Invoice {
  """
  Identifier of an invoice item
  """
  id: ID!

  """
  Identifier for the invoice
  """
  invoiceId: String!

  """
  Number for the invoice
  """
  invoiceNumber: String!

  """
  Status of the invoice
  """
  invoiceStatus: InvoiceStatus!

  """
  Amount of the invoice
  """
  invoiceAmount: Int!

  """
  Date of the invoice
  """
  invoiceDate: String!

  """
  Currency of the invoice amount
  """
  currencyCode: Currency!

  """
  Product images for the invoice
  """
  images: [InvoiceProductImage!]!
}

"""
Enum of all states for an invoice
"""
enum InvoiceStatus {
  PAID

  OPEN

  OVERDUE

  ALL
}

"""
An Invoice overview with multiple invoices and a pagination
"""
type InvoiceOverview {
  """
  Invoices in the overview
  """
  invoices: [Invoice!]!

  """
  Pagination information
  """
  page: Page!
}

input EmailVerificationInput {
  locale: String!
}

type EmailVerificationReturn {
  success: Boolean!
}

input OrderInput {
  id: ID!

  locale: String!
}

input OrderOverviewInput {
  activePage: Int

  itemsPerPage: Int

  locale: String!
}

input DeliveryOverviewInput {
  locale: String!
}

input BalanceInput {
  locale: String!
}

input MaturitiesInput {
  locale: String!
}

enum InvoiceOverviewInputDate {
  LAST_3_MONTHS

  LAST_6_MONTHS

  ALL
}

input InvoiceOverviewInput {
  activePage: Int

  itemsPerPage: Int

  date: InvoiceOverviewInputDate

  locale: String!
}

enum CacheControlScope {
  PUBLIC

  PRIVATE
}

input EmailInput {
  locale: String!
}

input VerifyEmailInput {
  """
  Encrypted parameter string from landingpage URL
  """
  data: String!

  """
  Origin of subscription
  """
  origin: String!

  """
  Language code
  """
  locale: String!
}

enum VerifyEmailStatus {
  success

  invalid

  technical_error
}

type VerifyEmailReturn {
  status: VerifyEmailStatus!
}

"""
Value for defining an attribute based filter
"""
type AttributeFilterChoice {
  """
  Identifier for the filter value
  """
  id: ID!

  """
  Label of a value, we will show this one to the customer as description
  """
  label: String!

  """
  Count of products, that can be shown after choosing this filter
  """
  productCount: Int!

  """
  Flag if the current value is choosen
  """
  active: Boolean!
}

"""
Value for defining an color based filter
"""
type ColorFilterChoice {
  """
  Identifier for the filter value
  """
  id: ID!

  """
  Label of a value, we will show this one to the customer as description
  """
  label: String!

  """
  Count of products, that can be shown after choosing this filter
  """
  productCount: Int!

  """
  Flag if the current value is choosen
  """
  active: Boolean!

  """
  Color for the colorTile of the value
  """
  color: String!
}

"""
Value for defining a range based filter
"""
type RangeFilterChoice {
  """
  Minimum value to define the beginning of the range
  """
  min: Int!

  """
  Minium value that is configured from the user
  """
  minActive: Int

  """
  Maximum value to define the end of the range
  """
  max: Int!

  """
  Maxium value that is configured from the user
  """
  maxActive: Int

  """
  Flag if the current value is choosen
  """
  active: Boolean

  """
  Count of products, that can be shown after choosing this filter
  """
  productCount: Int!
}

type BooleanFilterChoice {
  """
  A boolean filter has two choices(true/false), which are the name of this filters choices
  """
  name: Boolean!

  """
  Flag if the current value is choosen
  """
  active: Boolean

  """
  Count of products, that can be shown after choosing this filter
  """
  productCount: Int!
}

"""
Wrapper around the different FilterChoices
"""
union FilterChoice = RangeFilterChoice|AttributeFilterChoice|BooleanFilterChoice|ColorFilterChoice

"""
Type an filter could have (different visualization on the client)
"""
enum FilterType {
  """
  Filters for products containing a certain set of attributes
  """
  attribute

  """
  Filters for products in a certain range (e.g. price)
  """
  range

  """
  Filters for products conforming to one certain attribute
  """
  boolean

  """
  Filters for products with the selected color
  """
  color

  """
  Filter combines multiple filters in one group
  """
  combination
}

"""
Additional attributes for the specific filter
"""
type AttributeValue {
  key: String!

  value: String!
}

"""
With the filter type the products on a PLP and SRP can be configured
The main parts for the logic is the id and the id of the values
The label`s are for the description for the customer
"""
type Filter {
  """
  Identifier for the filter
  """
  id: ID!

  """
  Flag if the current filter has an choosen value
  """
  active: Boolean!

  """
  The kind of filter that is applied
  """
  type: FilterType!

  """
  Possible choices for the filter, that the customer can use for refining the product list
  """
  choices: [FilterChoice!]!

  """
  List of filters that should be shown in the same filter dropdown. Only viable on filters of type combination
  """
  filterList: [Filter!]
}

"""
Input for choosing filter
id -> Filters.id
values -> [FilterChoice.id]
"""
input SelectedFilter {
  """
  Identifier for the filter
  """
  id: String!

  """
  List of Identifiers from active FilterChoice`s
  """
  values: [String!]!
}

input FilterInput {
  term: String

  filters: [SelectedFilter!]

  locale: String!
}

type FilterValue {
  """
  Identifier of this filter value
  """
  id: ID!

  """
  Name of this filter
  """
  name: String!
}

"""
Type for selected filters. These consist of:
- id: Which filter is selected (eg. 'searchColor')
- values: List of FilterValue with identifier and speaking name
"""
type ActiveFilter {
  id: String!

  type: FilterType!

  values: [FilterValue!]!
}

"""
Type for selected filters. These consist of:
- id: Which filter is selected (eg. 'searchColor')
- values: With which data should this filter be initialized (eg. ["902"] -> id 902 represents the color green)

Attention: The interface should be the same as the `input SelectedFilter` which we can sadly not reuse because it is a inputType
"""
type ActiveFilterType {
  id: String!

  values: [String!]!
}

input ActiveFilterListInput {
  """
  The URL query parameters on the page, could be filter or also (invalid/other) information
  """
  query: [SelectedFilter!]!

  """
  Locale code of the shop
  """
  locale: String!

  """
  The searched term
  """
  term: String
}

"""
Status of the newsletter subscription
"""
enum NewsletterStatus {
  unknown

  """
  E-Mail was sent, but the recipient has not given their double opt in yet
  """
  pending

  """
  Subscription is active and double opt in is set
  """
  active

  withdrawn

  no_consent
}

"""
Type of the recipient that is subscribed to the newsletter
"""
enum NewsletterAccountType {
  unknown

  """
  Subscribed to the newsletter in the shop, but has no customer account
  """
  recipient

  """
  Only ordered and subscribed via the catalog
  """
  catalogCustomer

  """
  Recipient that is a normal customer
  """
  customer
}

"""
Type for possible unsubscribable mails
"""
enum UnsubscribeType {
  """
  States if user will unsubscribe from uwg mails
  """
  uwg

  """
  States if user will unsubscribe from newsletter
  """
  newsletter
}

type NewsletterStatusReturn {
  status: NewsletterStatus!

  accountType: NewsletterAccountType!
}

type NewsletterCachedStatusReturn {
  status: NewsletterStatus!
}

type NewsletterSubscribeReturn {
  success: Boolean!
}

type NewsletterActivateReturn {
  success: Boolean!
}

type NewsletterUnsubscribeReturn {
  success: Boolean!
}

type NewsletterPersonalDataReturn {
  salutation: String

  firstname: String

  lastname: String

  dateOfBirth: String
}

type NewsletterUpdatePersonalDataReturn {
  email: String!

  salutation: String

  firstname: String

  lastname: String

  dateOfBirth: String
}

input NewsletterStatusInput {
  """
  E-mail address (max. 60 characters)
  """
  email: Email

  """
  Language code
  """
  locale: String!
}

input NewsletterCachedStatusInput {
  """
  E-mail address (max. 60 characters)
  """
  email: Email

  """
  Language code
  """
  locale: String!
}

input NewsletterSubscribeInput {
  """
  E-mail address (max. 60 characters)
  """
  email: Email

  """
  Origin of subscription
  """
  origin: String!

  """
  Hint the user accepted
  """
  hint: String!

  """
  Language code
  """
  locale: String!
}

input NewsletterUnsubscribeInput {
  """
  E-mail address (max. 60 characters)
  """
  email: Email

  """
  Type for possible unsubscribable mails
  """
  type: UnsubscribeType

  """
  Language code
  """
  locale: String!
}

input NewsletterActivateInput {
  """
  Encrypted parameter string from landingpage URL
  """
  data: String!

  """
  `api=consent` Param to indicate the usage of the new COPS v2 endpoint
  """
  usingNewEndpoint: Boolean

  """
  Language code
  """
  locale: String!
}

input NewsletterPersonalDataInput {
  """
  E-mail address (max. 60 characters)
  """
  email: Email

  """
  Language code
  """
  locale: String!
}

input NewsletterUpdatePersonalDataInput {
  """
  E-mail address (max. 60 characters)
  """
  email: ID!

  """
  Salutation of the customer
  """
  salutation: String

  """
  First name of the customer
  """
  firstname: String

  """
  Last name of the customer
  """
  lastname: String

  """
  Birthdate of the customer
  """
  dateOfBirth: String

  """
  Language code
  """
  locale: String!
}

"""
All information about a customer after Checkout from AY
"""
type CustomerAY {
  """
  First name
  """
  firstname: String!

  """
  Last name
  """
  lastname: String!

  """
  Email address
  """
  email: String!

  """
  Calculated age
  """
  age: String!

  """
  Date of birth e.g. 1997-08-30
  """
  dateOfBirth: String!

  """
  Gender abbreviation e.g. m
  """
  gender: String!

  """
  Customer number of the customer
  """
  customerNumber: Int

  """
  If this is the first invoice order for this customer
  """
  firstInvoiceOrder: Boolean!

  """
  Customer type e.g. new
  """
  customerType: String!

  """
  Address
  """
  address: AddressAY!
}

type AddressAY {
  street: String!

  houseNumber: String!

  zipCode: String!

  city: String!

  """
  countryCode as ISO 3166-1 alpha-3
  """
  countryCode: String!

  """
  countryCode as ISO 3166-1 alpha-2
  """
  countryCodeShort: String!
}

"""
All information about an order after Checkout from AY
"""
type OrderAY {
  """
  Identifier for the order
  """
  id: String!

  """
  Type of delivery the customer chose
  """
  deliveryType: String!

  """
  Type of payment the customer used
  """
  paymentMethod: String!

  """
  Number of items in the order
  """
  itemCount: String!

  """
  Indicates that this is no real but a test order
  """
  isTestOrder: String!

  """
  Indicates if the checkout was called with isFreeShipping
  """
  isFreeShipping: String

  """
  Total price of the order including reductions and fees
  """
  price: OrderPriceAY!

  """
  Information about items after checkout from AY
  """
  items: [OrderItemAY!]!

  """
  Payback Customer Number
  """
  paybackCustomerNumber: String

  """
  Payback Points
  """
  paybackStandardOrderPoints: Int
}

type OrderPriceAY {
  """
  Total price of the order including tax (what the customer paid)
  """
  withTax: String!

  """
  Total price of the order including tax, without voucher discount
  """
  withTaxWithoutVoucher: String!

  """
  Currency of the values used
  """
  currency: Currency!

  """
  Costs for shipping
  """
  shippingCosts: String!

  """
  Information about the coupon that was redeemed in the checkout
  """
  coupon: Coupon

  """
  Basket overall net revenue forecast
  """
  netRevenueForecast: String
}

"""
Coupon that was redeemed in the checkout
"""
type Coupon {
  """
  Coupon value e.g. 5%
  """
  value: String!

  """
  Coupon code
  """
  code: String!
}

"""
All information about an order item after Checkout from AY
"""
type OrderItemAY {
  """
  Identifier for the product
  """
  productId: ID!

  """
  Variant ID for size information
  """
  variantId: String!

  """
  Name of the product
  """
  name: String!

  """
  Size of the product including additional size information
  """
  size: String!

  """
  Quantity of the product
  """
  quantity: Int!

  """
  Color of the product
  """
  color: String!

  """
  MKZ of the product
  """
  mkz: String

  """
  StyleType of the product e.g. 5
  """
  styleType: String

  """
  Availability state of the product on the thanks page
  """
  availability: String!

  """
  Badge on the product e.g. new
  """
  badge: String

  """
  Dimension with id and promotion
  """
  dimension: DimensionAY!

  """
  Price of the product when it was ordered including reductions
  """
  price: PriceItemAY!

  """
  Information where the customer found the product
  """
  category: ProductCategoryAY!

  """
  Master Key from AY
  """
  ayMasterKey: String

  """
  Variant Reference Key
  """
  variantReferenceKey: String!
}

"""
Price information that are neccessary for displaying the size
"""
type PriceItemAY {
  """
  Product price for the customer with tax (europe)
  """
  withTax: String!

  """
  Price of the item including tax, without voucher discount
  """
  withTaxWithoutVoucher: String!

  """
  Product price currency for the current shop
  """
  currency: Currency!

  """
  Reduction type e.g. sale,voucher
  """
  reductionType: String

  """
  Information for sale reduction
  """
  sale: SaleAY

  """
  Information for voucher reduction
  """
  voucher: VoucherAY
}

"""
Sale information from AY
"""
type SaleAY {
  """
  Relative sale value
  """
  value: String!

  """
  Absolute sale value
  """
  absoluteWithTax: String!
}

"""
Voucher information from AY
"""
type VoucherAY {
  """
  Relative voucher value
  """
  value: String!

  """
  Voucher code e.g. SHIRTS10
  """
  code: String!

  """
  Absolute voucher discount with Tax
  """
  absoluteWithTax: String!
}

"""
Dimension information from AY
"""
type DimensionAY {
  """
  Size dimension identifier (witt article number with 6 digits)
  """
  id: ID!

  """
  Promotion number of the item
  """
  promotion: String!

  """
  Information if the product has a default promotion
  """
  isDefaultPromotion: String!
}

"""
Reduction that is applied to the price
"""
type PriceReductionAY {
  """
  Reduction with tax
  """
  withTax: Int!

  """
  Reduction in percent (0.1)
  """
  relative: Float!

  """
  Revenue of product
  """
  revenue: Int

  """
  Category of the reduction (voucher, sale, ...)
  """
  category: PriceReductionCategoryAY
}

"""
Reduction category from AY
"""
type PriceReductionCategoryAY {
  """
  Category name for reduction
  """
  name: String
}

"""
Category of product
"""
type ProductCategoryAY {
  """
  Category name e.g. clothing | women | shirts
  """
  name: String!
}

"""
All information about an order after Checkout from AY
"""
type OrderData {
  """
  Customer information
  """
  customer: CustomerAY!

  """
  Order information from AY after checkout
  """
  order: OrderAY!
}

input OrderDataInput {
  cbd: String!

  locale: String!
}

"""
Product image with all information (see ProductImage on bapi-schema)
"""
type ProductImage {
  """
  Image hash for the CDN
  """
  hash: String!

  """
  Alt tag for SEO and a11n
  """
  alt: String!

  """
  Which image type is represented by this image
  """
  type: ProductImageType!

  """
  If the background of the image is cut out
  """
  isCutOut: Boolean!
}

"""
Type for grouping/sorting the images
"""
enum ProductImageType {
  bust

  bustBack

  bustBackND

  bustBackTotal

  bustBackPart

  bustBackPartND

  BustBackTotalND

  bustBottom

  bustBottomPart

  bustBottomTotal

  bustFront

  bustFrontND

  bustFrontPart

  bustFrontPartND

  bustLeft

  bustLeftTotal

  bustLeftPart

  bustPart

  bustRight

  bustRightND

  bustRightPart

  bustRightTotal

  bustFrontTotal

  bustFrontTotalND

  bustTotal

  bustSide

  bustSidePart

  bustSidePartND

  bustSideTotal

  bustSideTotalND

  bustInsidePart

  bustInsidePartND

  bustInsideTotal

  bustInsideTotalND

  detail

  detailBack

  detailBackND

  detailFront

  detailFrontND

  detailLeft

  detailLeftND

  detailND

  detailRight

  detailRightND

  detailTop

  fallback

  folded

  foldedBack

  foldedBackND

  foldedBackPart

  foldedBackPartND

  foldedBackTotal

  foldedBackTotalND

  foldedFront

  foldedFrontND

  foldedFrontPart

  foldedFrontPartND

  foldedFrontTotal

  foldedFrontTotalND

  foldedLeft

  foldedLeftND

  foldedLeftPart

  foldedLeftPartNd

  foldedLeftTotal

  foldedLeftTotalND

  foldedND

  foldedRight

  foldedRightND

  foldedRightPart

  foldedRightPartND

  foldedRightTotal

  foldedRightTotalND

  foldedPart

  foldedPartNd

  foldedTop

  foldedTopND

  foldedTopPart

  foldedTopPartND

  foldedTopTotal

  foldedTopTotalNd

  foldedTotal

  foldedTotalND

  foldedBottom

  foldedBottomND

  milieu

  milieuND

  milieuTotal

  milieuTotalND

  model

  modelBack

  modelBackND

  modelBackSD

  modelBackPart

  modelBackPartND

  modelBottom

  modelBottomND

  modelBottomPart

  modelBottomPartND

  modelBottomTotal

  modelBottomTotalND

  modelFront

  modelFrontSD

  modelFrontND

  modelFrontPart

  modelFrontPartND

  modelLeft

  modelLeftND

  modelLeftPart

  modelInsidePart

  modelInsidePartND

  modelInsideTotal

  modelInsideTotalND

  modelND

  modelRight

  modelRightND

  modelRightPart

  modelRightPartND

  modelSide

  modelTop

  modelTopND

  modelTopPart

  modelTopPartND

  modelTopTotal

  modelTopTotalND

  modelTotal

  modelTotalNd

  modelFrontTotal

  modelFrontTotalND

  modelFrontTotalSD

  modelBackTotal

  modelBackTotalND

  modelLeftPartND

  modelLeftTotal

  modelLeftTotalND

  modelPart

  modelPartNd

  modelRightTotal

  modelRightTotalND

  modelSidePart

  modelSidePartND

  modelSideTotal

  modelSideTotalND

  outfit

  outfitBack

  outfitBackND

  outfitBackTotal

  outfitBackTotalND

  outfitFront

  outfitFrontND

  outfitFrontPart

  outfitFrontSD

  outfitFrontTotal

  outfitFrontTotalND

  outfitLeft

  outfitLeftND

  outfitLeftPart

  outfitLeftTotal

  outfitLeftTotalNd

  outfitND

  outfitRight

  outfitRightND

  outfitSide

  outfitRightTotal

  outfitRightTotalND

  outfitTotal

  outfitTotalNd

  product

  productBack

  productBackND

  productBackPart

  productBackPartND

  productBackTotal

  productBackTotalND

  productBottom

  productBottomND

  productBottomPart

  productBottomPartND

  productBottomTotal

  productBottomTotalND

  productFront

  productFrontND

  productFrontPart

  productFrontPartND

  productFrontTotal

  productFrontTotalND

  productLeft

  productLeftND

  productLeftPart

  productLeftPartND

  productND

  productPart

  productPartND

  productRight

  productRightND

  productRightPart

  productRightPartND

  productRightTotal

  productRightTotalND

  productLeftTotal

  productLeftTotalND

  productSide

  productSideND

  productSideTotal

  productSideTotalND

  productTop

  productTopND

  productTopPart

  productTopPartND

  productTopTotal

  productTopTotalND

  productTotal

  productTotalND

  stack

  stackFront

  stackFrontND

  stackND

  back

  backND

  bottom

  bottomND

  front

  frontND

  left

  leftND

  right

  rightND

  top

  topND

  side

  inside

  nd
}

"""
Different types of image orders (default is PDP)
"""
enum ImageOrder {
  PLP

  PDP
}

"""
Currency ISO-Codes (https://en.wikipedia.org/wiki/ISO_4217#X_currencies)
"""
enum Currency {
  CHF

  CZK

  EUR

  SEK

  USD
}

"""
Price information that are neccessary for displaying the size
"""
type Price {
  """
  product price for the customer with tax (europe)
  """
  withTax: Int!

  """
  product price for customer without tax (international)
  """
  withoutTax: Int

  """
  product price currency for the current shop
  """
  currency: Currency!

  """
  price reductions (sale, ...)
  """
  reductions: [PriceReduction!]!

  """
  Calculated sum of all reductions
  """
  savings: Int
}

"""
Category for grouping the price reductions
"""
enum PriceReductionCategory {
  """
  reduction through marketing campaign
  """
  campaign

  """
  reduction through sale
  """
  sale

  """
  reduction through voucher
  """
  voucher

  """
  reduction is not known by the mapper
  """
  unknown
}

"""
Reduction that is applied to the price
"""
type PriceReduction {
  """
  Reduction with tax
  """
  withTax: Int!

  """
  Reduction in percent (0.1)
  """
  relative: Float!

  """
  Category of the reduction (voucher, sale, ...)
  """
  category: PriceReductionCategory!
}

"""
Availability identifier
"""
enum AvailabilityState {
  available

  delayed

  outsold

  outOfStock
}

"""
Availability information for a variant
"""
type Availability {
  """
  Availability identifier
  """
  id: ID

  """
  Key for the i18n availability text
  """
  key: String!

  """
  How much variants are available
  """
  quantity: Int

  """
  Identifier for the availability
  """
  state: AvailabilityState!

  """
  Indicates if the product is subsequent deliverable even when the user will order more items than available
  """
  isSubsequentDelivery: Boolean

  """
  Key for the i18n subsequent delivery availability text. Use in combination with isSubsequentDelivery
  """
  subsequentDeliveryKey: String
}

"""
Dimension createas a cluster over sizes
"""
type Dimension {
  """
  size dimension identifier (witt article number with 6 digits)
  """
  id: ID!

  """
  Id that is unique across promotions, query this if you want to properly cache the displayNumber
  """
  iid: ID

  """
  size dimension (example cup size)
  """
  label: String!

  """
  promotion number
  """
  promotion: String!

  """
  default promotion number as a fallback
  """
  defaultPromotion: String @deprecated(reason: "never used, save some bytes")

  """
  is the current promotion the default (shop) promotion (required for tracking)
  """
  isDefaultPromotion: Boolean!

  """
  all variants of dimension
  """
  variants: [Variant!]!
}

"""
All information that are neccessary for the visualisation of the price
"""
type Size {
  """
  size label for the customer (example 36)
  """
  label: String!

  """
  more size information if the label is not really expressive (60x90 cm)
  """
  description: String
}

"""
Variant represents the size and price information
"""
type Variant {
  """
  identifier for the variant required for adding a product to the basket
  """
  id: ID!

  """
  Id that is unique across promotions, query this if you want to properly cache the displayNumber
  """
  iid: ID

  """
  Unique identifier across systems
  """
  ean: String

  """
  Unique identifier for nitro and tracking data
  """
  referenceKey: String

  """
  availability information for the product
  """
  availability: Availability!

  """
  Flag if additional shipping costs apply
  """
  deliverabilityForwardingAgent: Boolean!

  """
  Flag if disposal service is possible with this variant
  """
  disposalService: Boolean!

  """
  cluster over sizes
  """
  dimension: Dimension @deprecated(reason: "Variant is child of dimension")

  """
  gives a hint for customer (example is smaller, wash it only with 30°)
  """
  hint: String

  """
  price information
  """
  price: Price!

  """
  size information
  """
  size: Size!

  """
  Dynamic variant attributes as key/value pair
  """
  attributes: [ProductAttribute!]!

  """
  Custom promotion that only matches a subset of variant(s)
  """
  promotion: String!
}

"""
Price information for the whole product
"""
type ProductPrice {
  """
  oldest lowest price of all variants in the product
  """
  old: Int!

  """
  lowest price of all variants in the product
  """
  min: Int!

  """
  highest price of all variants in the product
  """
  max: Int!

  """
  different prices over the variants
  """
  priceRange: Boolean!

  """
  currency for the price
  """
  currency: Currency!

  """
  price reduction that the customer saves
  """
  saving: Float

  """
  different relative reductions over the priceReductions
  """
  savingRange: Boolean
}

"""
Flag for different product visualisations
"""
enum ProductFlag {
  new

  sale

  exclusiveOnline

  priceHighlighting

  none
}

"""
Formerly FastColors is now Colors
"""
type Color {
  """
  Identifier for a color (same as product.id)
  """
  id: ID!

  """
  color code (hex, rgb or hsl - the current format is not fixed at the moment)
  """
  code: String!

  """
  color name (Lightblue, ...)
  """
  label: String!

  """
  color name for search (blue, ...)
  """
  searchLabel: String!

  """
  Product with color is sold out
  """
  isSoldOut: Boolean!
}

type Sibling {
  """
  Identifier for a color (same as product.id)
  """
  id: ID!

  """
  Product name for this color (can differ from the original product)
  """
  name: String!

  """
  color code (hex, rgb or hsl - the current format is not fixed at the moment)
  """
  code: String!

  """
  color name (red, green, blue)
  """
  label: String!

  """
  All images for product visualisation
  """
  images(input: ImagesInput!): [ProductImage!]

  """
  Global price information for the product gathered over all variants
  """
  price: ProductPrice

  """
  Availability information for the product
  """
  availability: Availability

  """
  Flag for different product visualisations (new, sale, exclusiveOnline, priceHighlighting, none)
  """
  flag: ProductFlag!
}

"""
Dynamic product attributes
"""
type ProductAttribute {
  """
  key of the attribute (brand)
  """
  key: String!

  """
  value of the attribute (Nike)
  """
  value: String!
}

"""
A category the product belongs to
"""
type ProductCategory {
  """
  Unique ID
  """
  id: ID!

  """
  Human-readable name
  """
  name: String!

  """
  The path for routing with a leading slash
  """
  path: String!

  """
  Title is used instead of name, if available
  """
  title: String
}

"""
Defines the usage of the product, category, ... in another shop
"""
type Alternate {
  """
  Path of the alternate in the different shop
  """
  path: String!

  """
  Shortcode for the shop
  """
  shop: String!
}

"""
Defines the different EnergyEfficiencyClasses (like 'A', or 'C')
"""
enum EnergyEfficiencyClass {
  A

  B

  C

  D

  E

  F

  G
}

"""
Defines the different heat degrees. 0 means summer jacket, 3 means very warm winter jacket.
"""
enum HeatDegree {
  LEVEL_0

  LEVEL_1

  LEVEL_2

  LEVEL_3
}

input SubstitutionInput {
  locale: String!
}

input ColorsInput {
  includeSoldOut: Boolean
}

input SiblingsInput {
  includeSoldOut: Boolean

  locale: String!
}

input ImagesInput {
  """
  Amount of image per product
  """
  count: Int

  """
  ProductImageType Enum
  """
  imageType: ProductImageType

  imageTypeNew: String

  """
  Match the exact image type
  """
  exactImageType: Boolean
}

input AlternateInput {
  """
  Language code
  """
  locale: String!
}

input SustainabilityLogosInput {
  limit: Int!
}

"""
All information that are neccessary for the product visualisation and interaction
"""
type Product {
  """
  Identifier for a product in backbone
  """
  id: ID!

  """
  Id that is unique across promotions, query this if you want to properly cache the displayNumber
  """
  iid: ID

  """
  Product name
  """
  name: String!

  """
  Product title (keyword)
  """
  mainTitle: String

  """
  Sustainability Logos (Url)
  """
  sustainabilityLogos(input: SustainabilityLogosInput!): [String!]

  """
  Product description (floating text)
  """
  description: String

  """
  AboutYou master identifier
  """
  masterKey: ID

  """
  Id of the canonical product
  """
  canonicalId: ID!

  """
  Ids of associated categories (including hidden categories)
  """
  categoryIds: [Int]

  """
  Identifier of a catalog, have to be used for promotion preservation
  """
  catalogId: ID

  """
  Dimension id only for tracking
  """
  trackingDimensionId: String

  """
  The detailed color of the product
  """
  color: String!

  """
  MKZ of the product
  """
  mkz: String

  """
  Style type of the product
  """
  styleType: String

  """
  Master category of the product
  """
  masterCategory: String

  """
  Is the product completly sold out
  """
  outsold: Boolean!

  """
  Brand information for the product
  """
  brand: Brand

  """
  Flag if the current product is the disposalService
  """
  disposalServiceProduct: Boolean!

  """
  Flat if the current product has a disposal note
  """
  disposalNote: Boolean!

  """
  Flag for different product visualisations (new, sale, exclusiveOnline, priceHighlighting, none)
  """
  flag: ProductFlag!

  """
  Dynamic product attributes as key/value pair
  """
  attributes: [ProductAttribute!]!

  """
  The category path that should be displayed in the breadcrumb
  """
  breadcrumb: [ProductCategory!]!

  """
  Global price information for the product gathered over all variants
  """
  price: ProductPrice

  """
  The text that is shown if the product is a multipack
  """
  salesUnit: String

  """
  All images for product visualisation
  """
  images(input: ImagesInput!): [ProductImage!]

  """
  All dimensions for the product
  """
  dimensions: [Dimension!]!

  """
  All colors that are available for this product (faster loading than colors)
  """
  colors(input: ColorsInput!): [Color!]

  siblings(input: SiblingsInput!): [Sibling!]

  """
  Substition for free sold out products
  """
  substitution(input: SubstitutionInput!): Product

  """
  Alternate product in another shop
  """
  alternate(input: AlternateInput): [Alternate!]!

  """
  Product rating in % stored in AY-Backbone (data synced daily with Bazaarvoice)
  """
  rating: Float

  """
  Label for the energyEfficiencyClass (like: 'A')
  """
  energyEfficiencyClass: EnergyEfficiencyClass

  """
  Heat Degree (indicates how warm a jacket is)
  """
  heatDegree: HeatDegree

  """
  Flag if the current product is longline
  """
  longline: Boolean

  """
  Company of origin for the product
  """
  companyOfOrigin: String
}

type Page {
  """
  The total number of pages
  """
  pageCount: Int!

  """
  The requested page number
  """
  activePage: Int!

  """
  The total number of items for this query
  """
  totalItemsCount: Int!

  """
  The number of items on this page
  """
  itemsOnPage: Int

  """
  The requested page size
  """
  itemsPerPage: Int!
}

type ProductListing {
  """
  Primary Identifier of the productlisting
  """
  id: ID!

  """
  Secondary Identifier for the productlisting
  """
  page: ID!

  """
  Products in the listing
  """
  products: [Product!]!

  """
  Pagination information
  """
  meta: ProductListingMeta!
}

enum SortType {
  """
  sorts by price ascending
  """
  priceAsc

  """
  sorts by price descending
  """
  priceDesc

  """
  sorts by the most popular products
  """
  default

  """
  sorts by data driven decision
  """
  defaultV2

  """
  sorts by date descending
  """
  dateDesc

  """
  Personalized sorting: Sorts via ecc brand
  """
  eccBrand

  """
  Personalized sorting: Sorts via ecc om
  """
  eccOm

  """
  Personalized sorting: Sorts via ecc om version 2
  """
  eccOmV2

  """
  Personalized sorting: Sorts via ranking algorithm
  """
  defaultTest

  """
  Personalized sorting: Sorts via ranking algorithm - Variant A
  """
  defaultTestA

  """
  Personalized sorting: Sorts via ranking algorithm - Variant B
  """
  defaultTestB
}

type ProductListingMeta {
  """
  The total number of items for this query
  """
  totalItemsCount: Int!
}

type EuImporter {
  """
  Combination of other properties for caching. Is no iid because of backwards compatability
  """
  id: ID!

  companyName: String!

  street: String!

  houseNumber: String

  additionalAddress: String

  zipCode: String!

  city: String!

  countryCode: String!

  email: String

  url: String
}

input ProductListingInput {
  activePage: Int!

  filters: [SelectedFilter!]

  itemsPerPage: ItemsPerPage

  locale: String!

  sort: SortType

  term: String
}

scalar ItemsPerPage

input productInCategoryArgs {
  limit: ProductLimit!

  locale: String!

  sort: SortType
}

scalar ProductLimit

input ProductsByIdsInput {
  catalogId: ID

  ids: [ID!]!

  locale: String!

  referrer: String
}

input ProductByIdInput {
  catalogId: ID

  id: ID!

  locale: String!

  referrer: String
}

input EuImporterByProductInput {
  dimensionId: String!

  locale: String!
}

input ProductByInput {
  catalogId: ID

  dimensionId: ID

  displayNumber: ID

  id: ID

  locale: String!

  promotion: ID
}

"""
Api response status
"""
type ProductRatingApiResponseStatus {
  status: String!
}

"""
Product statistics
"""
type ProductRatingReviewStatistic {
  overallRatingRange: Int

  totalReviewCount: Int

  averageOverallRating: Float

  averageOverallRatingInPercent: Float

  ratingDistribution: [ProductRatingDistribution!]

  attributeRatingsAverage: [ProductRatingAttributeRating!]
}

"""
Product review item
"""
type ProductRatingReview {
  id: ID!

  userNickname: String!

  rating: ProductRatingRatingValues

  title: String

  text: String

  submissionTime: String!

  isRecommended: Boolean

  isSyndicated: Boolean

  totalFeedbackCount: Int

  totalPositiveFeedbackCount: Int

  totalNegativeFeedbackCount: Int

  attributeRatings: [ProductRatingAttributeRating!]

  additionalFields: [ProductRatingAdditionalField!]

  verifiedPurchaser: Boolean
}

"""
Defines different rating values
"""
type ProductRatingRatingValues {
  rating: Float!

  range: Int!

  percentage: Float!
}

"""
Attribute rating like size, length, ...
"""
type ProductRatingAttributeRating {
  attributeKey: String!

  displayType: String!

  label: String!

  minLabel: String!

  maxLabel: String!

  valueRange: Int!

  value: Float!

  percentage: Float!
}

"""
Additional fields
"""
type ProductRatingAdditionalField {
  attributeKey: String!

  label: String!

  value: String!
}

"""
Rating distribution
"""
type ProductRatingDistribution {
  rating: Int!

  count: Int!

  percentage: Float!
}

"""
Possible attributes for filtering. Listed in the following BV documentations:

https://developer.bazaarvoice.com/conversations-api/reference/v5.4/reviews/review-display#filter-options
https://developer.bazaarvoice.com/conversations-api/getting-started/display-fundamentals
"""
enum RatingFilterAttribute {
  Rating

  ProductId

  ContentLocale
}

"""
Represents all operators which can be used for filtering.
More filter operators may be added based on the following documentation:
https://developer.bazaarvoice.com/conversations-api/getting-started/display-fundamentals#filtering
"""
enum FilterOperator {
  eq
}

"""
Filters to limit results for a rating query to BV
"""
input RatingFilter {
  attribute: RatingFilterAttribute!

  values: [String!]!

  operator: FilterOperator
}

"""
Rating Attributes, which may be used to sort ratings in a BV query
"""
enum RatingSortAttribute {
  Rating

  Helpfulness

  SubmissionTime
}

"""
Direction in which the sorted results should be displayed. (Ascending/Descending)
"""
enum SortDirection {
  asc

  desc
}

"""
Option to sort the rating results returned by BV
"""
input RatingSortOption {
  attribute: RatingSortAttribute!

  direction: SortDirection
}

enum RatingFieldType {
  number

  text

  string

  boolean

  choice
}

type RatingFieldChoice {
  id: ID!

  label: String!
}

enum RatingFieldGroup {
  general

  detail

  personal
}

type RatingField {
  id: ID!

  choices: [RatingFieldChoice!]

  error: String

  group: RatingFieldGroup!

  label: String!

  maxLength: Int

  minLength: Int

  required: Boolean!

  type: RatingFieldType!

  value: String
}

type ProductRatingForm {
  id: ID!

  submissionId: ID

  fields: [RatingField!]!
}

input ProductRatingFieldInput {
  id: ID!

  value: String!
}

input ProductRatingInput {
  productId: ID!

  locale: String!

  fields: [ProductRatingFieldInput!]!
}

"""
Type for all tracking responses
"""
type TrackingResponse {
  success: Boolean!
}

"""
Configuration for the recommendation output element
"""
input GkAirOutputConfigInput {
  """
  The ID of the output element
  """
  outputId: ID!

  """
  Concatenated list of image types, separated by a pipe (|) character.
  """
  imageType: String!
}

"""
GK Air Query Parameter
"""
input GkAirParameterInput {
  """
  Parameter Name
  """
  name: String!

  """
  Parameter Value
  """
  value: String!
}

"""
Input for loading a GK Air recommendation
"""
input GkAirInput {
  """
  Service ID, e.g. homepage
  """
  serviceId: ID!

  """
  Language code
  """
  locale: String!

  """
  Session ID
  """
  sessionId: ID

  """
  User ID
  """
  userId: ID

  """
  GK Air Query Parameters
  """
  parameters: [GkAirParameterInput]!

  """
  Indicates if the user has given tracking consent
  """
  trackingConsent: Boolean!

  """
  Recommendation output configuration
  """
  outputConfig: [GkAirOutputConfigInput!]
}

"""
Input for tracking a click on a product shown in a GK Air recommendation
"""
input TrackClickGkAirInput {
  """
  ID of the clicked product
  """
  productId: ID!

  """
  The tracking token of the product. It is provided by GK Air when initially requesting the recommendation.
  """
  trackingToken: String!

  """
  Session ID
  """
  sessionId: ID!

  """
  User ID
  """
  userId: ID

  """
  Indicates if the user has given tracking consent
  """
  trackingConsent: Boolean!

  """
  Language code
  """
  locale: String!
}

"""
Input for tracking in GK Air all purchased products from basket
"""
input TrackBasketGkAirInput {
  """
  List of product's ids purchased
  """
  productIds: [ID!]!

  """
  Quantity of the products that are added to the basket. At least one quantity for one product.
  """
  quantities: [Int]

  """
  Total of the item(s). One price per product multiplied by the quantity and optionally adjusted by rebates/deductions.
  """
  totals: [Float!]!

  """
  Session ID
  """
  sessionId: ID!

  """
  User ID
  """
  userId: ID

  """
  Indicates if the user has given tracking consent
  """
  trackingConsent: Boolean!

  """
  Language code
  """
  locale: String!
}

"""
Input for tracking in GK Air products added to the basket
"""
input TrackAddToBasketGkAirInput {
  """
  List of product's ids purchased
  """
  productIds: [ID!]!

  """
  Quantity of the products that are added to the basket. At least one quantity for one product.
  """
  quantities: [Int]

  """
  Session ID
  """
  sessionId: ID!

  """
  User ID
  """
  userId: ID

  """
  Indicates if the user has given tracking consent
  """
  trackingConsent: Boolean!

  """
  Language code
  """
  locale: String!
}

"""
Input for tracking in GK Air product view events
"""
input TrackProductViewGkAirInput {
  """
  Product ID
  """
  productId: ID!

  """
  Session ID
  """
  sessionId: ID!

  """
  User ID
  """
  userId: ID

  """
  Indicates if the user has given tracking consent
  """
  trackingConsent: Boolean!

  """
  Language code
  """
  locale: String!
}

"""
Input for tracking in GK Air search events
"""
input TrackSearchGkAirInput {
  """
  Search Term
  """
  searchTerm: String!

  """
  Session ID
  """
  sessionId: ID!

  """
  User ID
  """
  userId: ID

  """
  Indicates if the user has given tracking consent
  """
  trackingConsent: Boolean!

  """
  Language code
  """
  locale: String!
}

"""
Input for tracking in GK Air category view events
"""
input TrackCategoryViewGkAirInput {
  """
  Category ID (without leading 'C'; number only; i.e. '1234')
  """
  categoryId: ID!

  """
  Session ID
  """
  sessionId: ID!

  """
  User ID
  """
  userId: ID

  """
  Indicates if the user has given tracking consent
  """
  trackingConsent: Boolean!

  """
  Language code
  """
  locale: String!
}

"""
Input for tracking a click on a category shown in a GK Air recommendation
"""
input TrackCategoryClickGkAirInput {
  """
  ID of the clicked category (without leading 'C'; number only; i.e. '1234')
  """
  categoryId: ID!

  """
  The tracking token of the category. It is provided by GK Air when initially requesting the recommendation.
  """
  trackingToken: String!

  """
  Session ID
  """
  sessionId: ID!

  """
  User ID
  """
  userId: ID

  """
  Indicates if the user has given tracking consent
  """
  trackingConsent: Boolean!

  """
  Language code
  """
  locale: String!
}

"""
Combines a product with its GK Air metadata
"""
type ProductWithGkAirMetadata {
  """
  The product that is shown in the recommendation
  """
  product: Product!

  """
  The tracking token provided by GK Air for this product
  """
  trackingToken: String!

  """
  The reason why this product was selected in the Gk Air recommendation
  """
  reason: String!

  """
  The outfitID provided by GKAir for this product
  """
  outfitID: String

  """
  The category ID in case of an assortment promotion
  """
  category: String

  """
  The category path in case of an assortment promotion
  """
  categoryPath: String

  """
  The category name in case of an assortment promotion
  """
  categoryName: String
}

"""
An output element (i.e. recommendation) of a GK Air service
"""
type GkAirOutput {
  """
  The ID of the output element
  """
  outputId: ID!

  """
  The products that are shown in this output element
  """
  products: [ProductWithGkAirMetadata!]!

  """
  Title of the recommendation
  """
  title: String
}

"""
Search field results (used to route to a PLP or PDP when a catalog number was entered)
"""
type SearchFieldResult {
  productId: ID

  catalogId: ID

  category: Category

  totalItemsCount: Int

  term: String!

  redirect: String
}

"""
Information about a brand
"""
type Brand {
  id: ID!

  name: String!

  image: String
}

"""
Sugested filter to a category (e.g. color search)
"""
type SuggestedFilter {
  id: ID!

  key: String!

  value: String!
}

"""
Information about a suggested category with an optional Filter and the matching score
"""
type SuggestedCategory {
  id: ID!

  category: Category!

  suggestedFilter: ActiveFilterType

  score: Float
}

"""
Information about a suggested product with the matching score
"""
type SuggestedProduct {
  id: ID!

  product: Product!

  score: Float
}

"""
Wrapper around the different suggestion types
"""
type Suggestion {
  id: ID!

  brands: [Brand!]!

  categories: [SuggestedCategory!]!

  products: [SuggestedProduct!]!
}

"""
Information about a suggested search term
"""
type Term {
  id: ID!

  name: String!
}

"""
Wrapper around the smart suggestions
"""
type SmartSuggestion {
  id: ID!

  suggestions: [Term!]!
}

enum SearchVariant {
  as

  scayle

  sh
}

"""
Input type for the search field query
"""
input SearchFieldInput {
  term: String!

  locale: String!

  variant: SearchVariant
}

"""
Input type for the suggestion query
"""
input SuggestionInput {
  term: String!

  locale: String!
}

"""
Input type for the smart suggestion query
"""
input SmartSuggestionInput {
  term: String!
}

type WishlistItem {
  """
  The key needed by AY to identify the wishlist item
  """
  id: ID!

  """
  All information that are neccessary for the product visualisation
  """
  product: Product!
}

input RemoveWishlistItemInput {
  itemKey: ID!

  locale: String!

  isTargetingAllowed: Boolean
}

input AddWishlistItemInput {
  productId: ID!

  locale: String!

  isTargetingAllowed: Boolean
}

input MigrateWishListInput {
  locale: String!

  isTargetingAllowed: Boolean
}

input WishlistInput {
  locale: String!
}

"""
A GraphQL Schema defines the capabilities of a GraphQL server. It exposes all available types and directives on the server, as well as the entry points for query, mutation, and subscription operations.
"""
type __Schema {
  description: String

  """
  A list of all types supported by this server.
  """
  types: [__Type!]!

  """
  The type that query operations will be rooted at.
  """
  queryType: __Type!

  """
  If this server supports mutation, the type that mutation operations will be rooted at.
  """
  mutationType: __Type

  """
  If this server support subscription, the type that subscription operations will be rooted at.
  """
  subscriptionType: __Type

  """
  A list of all directives supported by this server.
  """
  directives: [__Directive!]!
}

"""
The fundamental unit of any GraphQL Schema is the type. There are many kinds of types in GraphQL as represented by the `__TypeKind` enum.

Depending on the kind of a type, certain fields describe information about that type. Scalar types provide no information beyond a name, description and optional `specifiedByURL`, while Enum types provide their values. Object and Interface types provide the fields they describe. Abstract types, Union and Interface, provide the Object types possible at runtime. List and NonNull types compose other types.
"""
type __Type {
  kind: __TypeKind!

  name: String

  description: String

  specifiedByURL: String

  fields(includeDeprecated: Boolean = false): [__Field!]

  interfaces: [__Type!]

  possibleTypes: [__Type!]

  enumValues(includeDeprecated: Boolean = false): [__EnumValue!]

  inputFields(includeDeprecated: Boolean = false): [__InputValue!]

  ofType: __Type

  isOneOf: Boolean
}

"""
An enum describing what kind of type a given `__Type` is.
"""
enum __TypeKind {
  """
  Indicates this type is a scalar.
  """
  SCALAR

  """
  Indicates this type is an object. `fields` and `interfaces` are valid fields.
  """
  OBJECT

  """
  Indicates this type is an interface. `fields`, `interfaces`, and `possibleTypes` are valid fields.
  """
  INTERFACE

  """
  Indicates this type is a union. `possibleTypes` is a valid field.
  """
  UNION

  """
  Indicates this type is an enum. `enumValues` is a valid field.
  """
  ENUM

  """
  Indicates this type is an input object. `inputFields` is a valid field.
  """
  INPUT_OBJECT

  """
  Indicates this type is a list. `ofType` is a valid field.
  """
  LIST

  """
  Indicates this type is a non-null. `ofType` is a valid field.
  """
  NON_NULL
}

"""
Object and Interface types are described by a list of Fields, each of which has a name, potentially a list of arguments, and a return type.
"""
type __Field {
  name: String!

  description: String

  args(includeDeprecated: Boolean = false): [__InputValue!]!

  type: __Type!

  isDeprecated: Boolean!

  deprecationReason: String
}

"""
Arguments provided to Fields or Directives and the input fields of an InputObject are represented as Input Values which describe their type and optionally a default value.
"""
type __InputValue {
  name: String!

  description: String

  type: __Type!

  """
  A GraphQL-formatted string representing the default value for this input value.
  """
  defaultValue: String

  isDeprecated: Boolean!

  deprecationReason: String
}

"""
One possible value for a given Enum. Enum values are unique values, not a placeholder for a string or numeric value. However an Enum value is returned in a JSON response as a string.
"""
type __EnumValue {
  name: String!

  description: String

  isDeprecated: Boolean!

  deprecationReason: String
}

"""
A Directive provides a way to describe alternate runtime execution and type validation behavior in a GraphQL document.

In some cases, you need to provide options to alter GraphQL's execution behavior in ways field arguments will not suffice, such as conditionally including or skipping a field. Directives provide this by describing additional information to the executor.
"""
type __Directive {
  name: String!

  description: String

  isRepeatable: Boolean!

  locations: [__DirectiveLocation!]!

  args(includeDeprecated: Boolean = false): [__InputValue!]!
}

"""
A Directive can be adjacent to many parts of the GraphQL language, a __DirectiveLocation describes one such possible adjacencies.
"""
enum __DirectiveLocation {
  """
  Location adjacent to a query operation.
  """
  QUERY

  """
  Location adjacent to a mutation operation.
  """
  MUTATION

  """
  Location adjacent to a subscription operation.
  """
  SUBSCRIPTION

  """
  Location adjacent to a field.
  """
  FIELD

  """
  Location adjacent to a fragment definition.
  """
  FRAGMENT_DEFINITION

  """
  Location adjacent to a fragment spread.
  """
  FRAGMENT_SPREAD

  """
  Location adjacent to an inline fragment.
  """
  INLINE_FRAGMENT

  """
  Location adjacent to a variable definition.
  """
  VARIABLE_DEFINITION

  """
  Location adjacent to a schema definition.
  """
  SCHEMA

  """
  Location adjacent to a scalar definition.
  """
  SCALAR

  """
  Location adjacent to an object type definition.
  """
  OBJECT

  """
  Location adjacent to a field definition.
  """
  FIELD_DEFINITION

  """
  Location adjacent to an argument definition.
  """
  ARGUMENT_DEFINITION

  """
  Location adjacent to an interface definition.
  """
  INTERFACE

  """
  Location adjacent to a union definition.
  """
  UNION

  """
  Location adjacent to an enum definition.
  """
  ENUM

  """
  Location adjacent to an enum value definition.
  """
  ENUM_VALUE

  """
  Location adjacent to an input object type definition.
  """
  INPUT_OBJECT

  """
  Location adjacent to an input object field definition.
  """
  INPUT_FIELD_DEFINITION
}

directive @constraint (minLength: Int, maxLength: Int, startsWith: String, endsWith: String, contains: String, notContains: String, pattern: String, format: String, min: Float, max: Float, exclusiveMin: Float, exclusiveMax: Float, multipleOf: Float, minItems: Int, maxItems: Int, uniqueTypeName: String) on INPUT_FIELD_DEFINITION|FIELD_DEFINITION|ARGUMENT_DEFINITION

directive @cacheControl (maxAge: Int, scope: CacheControlScope) on FIELD_DEFINITION|OBJECT|INTERFACE

"""
Directs the executor to include this field or fragment only when the `if` argument is true.
"""
directive @include ("Included when true." if: Boolean!) on FIELD|FRAGMENT_SPREAD|INLINE_FRAGMENT

"""
Directs the executor to skip this field or fragment when the `if` argument is true.
"""
directive @skip ("Skipped when true." if: Boolean!) on FIELD|FRAGMENT_SPREAD|INLINE_FRAGMENT

"""
Marks an element of a GraphQL schema as no longer supported.
"""
directive @deprecated ("Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax, as specified by [CommonMark](https://commonmark.org/)." reason: String = "No longer supported") on FIELD_DEFINITION|ARGUMENT_DEFINITION|INPUT_FIELD_DEFINITION|ENUM_VALUE

"""
Exposes a URL that specifies the behavior of this scalar.
"""
directive @specifiedBy ("The URL that specifies the behavior of this scalar." url: String!) on SCALAR

"""
Indicates exactly one field must be supplied and this field must not be `null`.
"""
directive @oneOf on INPUT_OBJECT

schema {
  query: Query
  mutation: Mutation
}
