"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/accordion)
"""
type Accordion implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  defaultIndex(locale: String): Int

  internalTitle(locale: String): String

  itemsCollection(limit: Int = 100, locale: String, order: [AccordionItemsCollectionOrder], preview: Boolean, skip: Int = 0, where: RichtextFilter): AccordionItemsCollection

  linkedFrom(allowedLocales: [String]): AccordionLinkingCollections

  sys: Sys!
}

type AccordionCollection {
  items: [Accordion]!

  limit: Int!

  skip: Int!

  total: Int!
}

input AccordionFilter {
  AND: [AccordionFilter]

  OR: [AccordionFilter]

  contentfulMetadata: ContentfulMetadataFilter

  defaultIndex: Int

  defaultIndex_exists: Boolean

  defaultIndex_gt: Int

  defaultIndex_gte: Int

  defaultIndex_in: [Int]

  defaultIndex_lt: Int

  defaultIndex_lte: Int

  defaultIndex_not: Int

  defaultIndex_not_in: [Int]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  items: cfRichtextNestedFilter

  itemsCollection_exists: Boolean

  sys: SysFilter
}

type AccordionItemsCollection {
  items: [Richtext]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum AccordionItemsCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

type AccordionLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum AccordionOrder {
  defaultIndex_ASC

  defaultIndex_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/actionButton)
"""
type ActionButton implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  label(locale: String): String

  linkedFrom(allowedLocales: [String]): ActionButtonLinkingCollections

  sys: Sys!

  type(locale: String): String
}

type ActionButtonCollection {
  items: [ActionButton]!

  limit: Int!

  skip: Int!

  total: Int!
}

input ActionButtonFilter {
  AND: [ActionButtonFilter]

  OR: [ActionButtonFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter

  type: String

  type_contains: String

  type_exists: Boolean

  type_in: [String]

  type_not: String

  type_not_contains: String

  type_not_in: [String]
}

type ActionButtonLinkingCollections {
  contactBoxCollection(limit: Int = 100, locale: String, order: [ActionButtonLinkingCollectionsContactBoxCollectionOrder], preview: Boolean, skip: Int = 0): ContactBoxCollection

  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum ActionButtonLinkingCollectionsContactBoxCollectionOrder {
  icon_ASC

  icon_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

enum ActionButtonOrder {
  internalTitle_ASC

  internalTitle_DESC

  label_ASC

  label_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  type_ASC

  type_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/anchorLink)
"""
type AnchorLink implements Entry & _Node {
  _id: ID!

  anchor(locale: String): String

  contentfulMetadata: ContentfulMetadata!

  label(locale: String): String

  linkedFrom(allowedLocales: [String]): AnchorLinkLinkingCollections

  sys: Sys!
}

type AnchorLinkCollection {
  items: [AnchorLink]!

  limit: Int!

  skip: Int!

  total: Int!
}

input AnchorLinkFilter {
  AND: [AnchorLinkFilter]

  OR: [AnchorLinkFilter]

  anchor: String

  anchor_contains: String

  anchor_exists: Boolean

  anchor_in: [String]

  anchor_not: String

  anchor_not_contains: String

  anchor_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

type AnchorLinkLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  inspirationPageCollection(limit: Int = 100, locale: String, order: [AnchorLinkLinkingCollectionsInspirationPageCollectionOrder], preview: Boolean, skip: Int = 0): InspirationPageCollection

  seoAdviceCollection(limit: Int = 100, locale: String, order: [AnchorLinkLinkingCollectionsSeoAdviceCollectionOrder], preview: Boolean, skip: Int = 0): SeoAdviceCollection
}

enum AnchorLinkLinkingCollectionsInspirationPageCollectionOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  overviewHeadline_ASC

  overviewHeadline_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  transitionalHeadline_ASC

  transitionalHeadline_DESC
}

enum AnchorLinkLinkingCollectionsSeoAdviceCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

enum AnchorLinkOrder {
  anchor_ASC

  anchor_DESC

  label_ASC

  label_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
Represents a binary file in a space. An asset can be any file type.
"""
type Asset {
  contentType(locale: String): String

  contentfulMetadata: ContentfulMetadata!

  description(locale: String): String

  fileName(locale: String): String

  height(locale: String): Int

  linkedFrom(allowedLocales: [String]): AssetLinkingCollections

  size(locale: String): Int

  sys: Sys!

  title(locale: String): String

  url(locale: String, transform: ImageTransformOptions): String

  width(locale: String): Int
}

type AssetCollection {
  items: [Asset]!

  limit: Int!

  skip: Int!

  total: Int!
}

input AssetFilter {
  AND: [AssetFilter]

  OR: [AssetFilter]

  contentType: String

  contentType_contains: String

  contentType_exists: Boolean

  contentType_in: [String]

  contentType_not: String

  contentType_not_contains: String

  contentType_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  description: String

  description_contains: String

  description_exists: Boolean

  description_in: [String]

  description_not: String

  description_not_contains: String

  description_not_in: [String]

  fileName: String

  fileName_contains: String

  fileName_exists: Boolean

  fileName_in: [String]

  fileName_not: String

  fileName_not_contains: String

  fileName_not_in: [String]

  height: Int

  height_exists: Boolean

  height_gt: Int

  height_gte: Int

  height_in: [Int]

  height_lt: Int

  height_lte: Int

  height_not: Int

  height_not_in: [Int]

  size: Int

  size_exists: Boolean

  size_gt: Int

  size_gte: Int

  size_in: [Int]

  size_lt: Int

  size_lte: Int

  size_not: Int

  size_not_in: [Int]

  sys: SysFilter

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]

  url: String

  url_contains: String

  url_exists: Boolean

  url_in: [String]

  url_not: String

  url_not_contains: String

  url_not_in: [String]

  width: Int

  width_exists: Boolean

  width_gt: Int

  width_gte: Int

  width_in: [Int]

  width_lt: Int

  width_lte: Int

  width_not: Int

  width_not_in: [Int]
}

type AssetLinkingCollections {
  assetSetCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): AssetSetCollection

  boxCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): BoxCollection

  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  errorPageCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): ErrorPageCollection

  globalContentCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): GlobalContentCollection

  inlineAssetCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): InlineAssetCollection

  inspirationItemCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): InspirationItemCollection

  inspirationPageCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): InspirationPageCollection

  localeImageCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): LocaleImageCollection

  noSearchResultCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): NoSearchResultCollection

  outfitItemCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): OutfitItemCollection

  promotionCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): PromotionCollection

  seoAdviceCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): SeoAdviceCollection

  sizeTableCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): SizeTableCollection
}

enum AssetOrder {
  contentType_ASC

  contentType_DESC

  fileName_ASC

  fileName_DESC

  height_ASC

  height_DESC

  size_ASC

  size_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  url_ASC

  url_DESC

  width_ASC

  width_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/assetSet)
"""
type AssetSet implements Entry & _Node {
  _id: ID!

  alt(locale: String): String

  contentfulMetadata: ContentfulMetadata!

  defaultImage(locale: String, preview: Boolean): Asset

  defaultImageCloudinary(locale: String): JSON

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): AssetSetLinkingCollections

  ratio1To1(locale: String, preview: Boolean): Asset

  ratio1To1Cloudinary(locale: String): JSON

  ratio2To1(locale: String, preview: Boolean): Asset

  ratio2To1Cloudinary(locale: String): JSON

  ratio3To1(locale: String, preview: Boolean): Asset

  ratio3To1Cloudinary(locale: String): JSON

  ratio3To4(locale: String, preview: Boolean): Asset

  ratio3To4Cloudinary(locale: String): JSON

  ratio4To3(locale: String, preview: Boolean): Asset

  ratio4To3Cloudinary(locale: String): JSON

  ratio7To1(locale: String, preview: Boolean): Asset

  ratio7To1Cloudinary(locale: String): JSON

  sys: Sys!
}

type AssetSetCollection {
  items: [AssetSet]!

  limit: Int!

  skip: Int!

  total: Int!
}

input AssetSetFilter {
  AND: [AssetSetFilter]

  OR: [AssetSetFilter]

  alt: String

  alt_contains: String

  alt_exists: Boolean

  alt_in: [String]

  alt_not: String

  alt_not_contains: String

  alt_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  defaultImageCloudinary_exists: Boolean

  defaultImage_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  ratio1to1Cloudinary_exists: Boolean

  ratio1to1_exists: Boolean

  ratio2to1Cloudinary_exists: Boolean

  ratio2to1_exists: Boolean

  ratio3to1Cloudinary_exists: Boolean

  ratio3to1_exists: Boolean

  ratio3to4Cloudinary_exists: Boolean

  ratio3to4_exists: Boolean

  ratio4to3Cloudinary_exists: Boolean

  ratio4to3_exists: Boolean

  ratio7to1Cloudinary_exists: Boolean

  ratio7to1_exists: Boolean

  sys: SysFilter
}

type AssetSetLinkingCollections {
  bannerCollection(limit: Int = 100, locale: String, order: [AssetSetLinkingCollectionsBannerCollectionOrder], preview: Boolean, skip: Int = 0): BannerCollection

  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  promotionCollection(limit: Int = 100, locale: String, order: [AssetSetLinkingCollectionsPromotionCollectionOrder], preview: Boolean, skip: Int = 0): PromotionCollection
}

enum AssetSetLinkingCollectionsBannerCollectionOrder {
  countdownStart_ASC

  countdownStart_DESC

  countdown_ASC

  countdown_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  trackingName_ASC

  trackingName_DESC
}

enum AssetSetLinkingCollectionsPromotionCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

enum AssetSetOrder {
  alt_ASC

  alt_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/banner)
"""
type Banner implements Entry & _Node {
  _id: ID!

  bannerImage(locale: String, preview: Boolean, where: AssetSetFilter): AssetSet

  bannerImageLink(locale: String, preview: Boolean, where: BannerBannerImageLinkFilter): BannerBannerImageLink

  contentfulMetadata: ContentfulMetadata!

  countdown(locale: String): DateTime

  countdownStart(locale: String): DateTime

  internalTitle(locale: String): String

  layoutColor(locale: String): JSON

  linkedFrom(allowedLocales: [String]): BannerLinkingCollections

  modal(locale: String, preview: Boolean, where: ModalFilter): Modal

  modalImageCloudinary(locale: String): JSON

  pages(locale: String): [String]

  sys: Sys!

  text(locale: String): BannerText

  trackingName(locale: String): String

  trigger(locale: String, preview: Boolean, where: TopLevelTriggerFilter): TopLevelTrigger
}

union BannerBannerImageLink = ExternalLink|InternalLink

input BannerBannerImageLinkFilter {
  AND: [BannerBannerImageLinkFilter]

  OR: [BannerBannerImageLinkFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

type BannerCollection {
  items: [Banner]!

  limit: Int!

  skip: Int!

  total: Int!
}

input BannerFilter {
  AND: [BannerFilter]

  OR: [BannerFilter]

  bannerImage: cfAssetSetNestedFilter

  bannerImageLink: cfbannerImageLinkMultiTypeNestedFilter

  bannerImageLink_exists: Boolean

  bannerImage_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  countdown: DateTime

  countdownStart: DateTime

  countdownStart_exists: Boolean

  countdownStart_gt: DateTime

  countdownStart_gte: DateTime

  countdownStart_in: [DateTime]

  countdownStart_lt: DateTime

  countdownStart_lte: DateTime

  countdownStart_not: DateTime

  countdownStart_not_in: [DateTime]

  countdown_exists: Boolean

  countdown_gt: DateTime

  countdown_gte: DateTime

  countdown_in: [DateTime]

  countdown_lt: DateTime

  countdown_lte: DateTime

  countdown_not: DateTime

  countdown_not_in: [DateTime]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  layoutColor_exists: Boolean

  modal: cfModalNestedFilter

  modalImageCloudinary_exists: Boolean

  modal_exists: Boolean

  pages_contains_all: [String]

  pages_contains_none: [String]

  pages_contains_some: [String]

  pages_exists: Boolean

  sys: SysFilter

  text_contains: String

  text_exists: Boolean

  text_not_contains: String

  trackingName: String

  trackingName_contains: String

  trackingName_exists: Boolean

  trackingName_in: [String]

  trackingName_not: String

  trackingName_not_contains: String

  trackingName_not_in: [String]

  trigger: cfTopLevelTriggerNestedFilter

  trigger_exists: Boolean
}

type BannerLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  homepageCreationLCollection(limit: Int = 100, locale: String, order: [BannerLinkingCollectionsHomepageCreationLCollectionOrder], preview: Boolean, skip: Int = 0): HomepageCreationLCollection

  homepageSiehAnCollection(limit: Int = 100, locale: String, order: [BannerLinkingCollectionsHomepageSiehAnCollectionOrder], preview: Boolean, skip: Int = 0): HomepageSiehAnCollection

  pdpCollection(limit: Int = 100, locale: String, order: [BannerLinkingCollectionsPdpCollectionOrder], preview: Boolean, skip: Int = 0): PdpCollection

  plpCollection(limit: Int = 100, locale: String, order: [BannerLinkingCollectionsPlpCollectionOrder], preview: Boolean, skip: Int = 0): PlpCollection
}

enum BannerLinkingCollectionsHomepageCreationLCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum BannerLinkingCollectionsHomepageSiehAnCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum BannerLinkingCollectionsPdpCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum BannerLinkingCollectionsPlpCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum BannerOrder {
  countdownStart_ASC

  countdownStart_DESC

  countdown_ASC

  countdown_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  trackingName_ASC

  trackingName_DESC
}

type BannerText {
  json: JSON!

  links: BannerTextLinks!
}

type BannerTextAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type BannerTextEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type BannerTextLinks {
  assets: BannerTextAssets!

  entries: BannerTextEntries!

  resources: BannerTextResources!
}

type BannerTextResources {
  block: [BannerTextResourcesBlock!]!

  hyperlink: [BannerTextResourcesHyperlink!]!

  inline: [BannerTextResourcesInline!]!
}

type BannerTextResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type BannerTextResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type BannerTextResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/box)
"""
type Box implements Entry & _Node {
  _id: ID!

  boxImage(locale: String, preview: Boolean): Asset

  boxImageCloudinary(locale: String): JSON

  boxLink(locale: String, preview: Boolean, where: BoxBoxLinkFilter): BoxBoxLink

  boxText(locale: String): String

  boxTitle(locale: String): String

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): BoxLinkingCollections

  sys: Sys!

  trackingName(locale: String): String
}

union BoxBoxLink = ExternalLink|InternalLink

input BoxBoxLinkFilter {
  AND: [BoxBoxLinkFilter]

  OR: [BoxBoxLinkFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

type BoxCollection {
  items: [Box]!

  limit: Int!

  skip: Int!

  total: Int!
}

input BoxFilter {
  AND: [BoxFilter]

  OR: [BoxFilter]

  boxImageCloudinary_exists: Boolean

  boxImage_exists: Boolean

  boxLink: cfboxLinkMultiTypeNestedFilter

  boxLink_exists: Boolean

  boxText: String

  boxText_contains: String

  boxText_exists: Boolean

  boxText_in: [String]

  boxText_not: String

  boxText_not_contains: String

  boxText_not_in: [String]

  boxTitle: String

  boxTitle_contains: String

  boxTitle_exists: Boolean

  boxTitle_in: [String]

  boxTitle_not: String

  boxTitle_not_contains: String

  boxTitle_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter

  trackingName: String

  trackingName_contains: String

  trackingName_exists: Boolean

  trackingName_in: [String]

  trackingName_not: String

  trackingName_not_contains: String

  trackingName_not_in: [String]
}

type BoxLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum BoxOrder {
  boxText_ASC

  boxText_DESC

  boxTitle_ASC

  boxTitle_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  trackingName_ASC

  trackingName_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/button)
"""
type Button implements Entry & _Node {
  _id: ID!

  color(locale: String): String

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  link(locale: String, preview: Boolean): ButtonLink

  linkedFrom(allowedLocales: [String]): ButtonLinkingCollections

  sys: Sys!

  trackingName(locale: String): String
}

type ButtonCollection {
  items: [Button]!

  limit: Int!

  skip: Int!

  total: Int!
}

input ButtonFilter {
  AND: [ButtonFilter]

  OR: [ButtonFilter]

  color: String

  color_contains: String

  color_exists: Boolean

  color_in: [String]

  color_not: String

  color_not_contains: String

  color_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  link_exists: Boolean

  sys: SysFilter

  trackingName: String

  trackingName_contains: String

  trackingName_exists: Boolean

  trackingName_in: [String]

  trackingName_not: String

  trackingName_not_contains: String

  trackingName_not_in: [String]
}

union ButtonLink = ExternalLink|InternalLink

type ButtonLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum ButtonOrder {
  color_ASC

  color_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  trackingName_ASC

  trackingName_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/card)
"""
type Card implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  displayType(locale: String): String

  imageAspectRatio(locale: String): String

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): CardLinkingCollections

  promotion(locale: String, preview: Boolean, where: PromotionFilter): Promotion

  sys: Sys!
}

type CardCollection {
  items: [Card]!

  limit: Int!

  skip: Int!

  total: Int!
}

input CardFilter {
  AND: [CardFilter]

  OR: [CardFilter]

  contentfulMetadata: ContentfulMetadataFilter

  displayType: String

  displayType_contains: String

  displayType_exists: Boolean

  displayType_in: [String]

  displayType_not: String

  displayType_not_contains: String

  displayType_not_in: [String]

  imageAspectRatio: String

  imageAspectRatio_contains: String

  imageAspectRatio_exists: Boolean

  imageAspectRatio_in: [String]

  imageAspectRatio_not: String

  imageAspectRatio_not_contains: String

  imageAspectRatio_not_in: [String]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  promotion: cfPromotionNestedFilter

  promotion_exists: Boolean

  sys: SysFilter
}

type CardLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum CardOrder {
  displayType_ASC

  displayType_DESC

  imageAspectRatio_ASC

  imageAspectRatio_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/categoryImageLink)
"""
type CategoryImageLink implements Entry & _Node {
  _id: ID!

  categoryId(locale: String): Int

  categoryImage(locale: String): String

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): CategoryImageLinkLinkingCollections

  sys: Sys!

  type(locale: String): String
}

type CategoryImageLinkCollection {
  items: [CategoryImageLink]!

  limit: Int!

  skip: Int!

  total: Int!
}

input CategoryImageLinkFilter {
  AND: [CategoryImageLinkFilter]

  OR: [CategoryImageLinkFilter]

  categoryId: Int

  categoryId_exists: Boolean

  categoryId_gt: Int

  categoryId_gte: Int

  categoryId_in: [Int]

  categoryId_lt: Int

  categoryId_lte: Int

  categoryId_not: Int

  categoryId_not_in: [Int]

  categoryImage: String

  categoryImage_contains: String

  categoryImage_exists: Boolean

  categoryImage_in: [String]

  categoryImage_not: String

  categoryImage_not_contains: String

  categoryImage_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter

  type: String

  type_contains: String

  type_exists: Boolean

  type_in: [String]

  type_not: String

  type_not_contains: String

  type_not_in: [String]
}

type CategoryImageLinkLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  promotionCollection(limit: Int = 100, locale: String, order: [CategoryImageLinkLinkingCollectionsPromotionCollectionOrder], preview: Boolean, skip: Int = 0): PromotionCollection
}

enum CategoryImageLinkLinkingCollectionsPromotionCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

enum CategoryImageLinkOrder {
  categoryId_ASC

  categoryId_DESC

  categoryImage_ASC

  categoryImage_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  type_ASC

  type_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/Checklist)
"""
type Checklist implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  internalTitel(locale: String): String

  linkedFrom(allowedLocales: [String]): ChecklistLinkingCollections

  sys: Sys!

  textList(locale: String): [String]

  title(locale: String): String
}

type ChecklistCollection {
  items: [Checklist]!

  limit: Int!

  skip: Int!

  total: Int!
}

input ChecklistFilter {
  AND: [ChecklistFilter]

  OR: [ChecklistFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitel: String

  internalTitel_contains: String

  internalTitel_exists: Boolean

  internalTitel_in: [String]

  internalTitel_not: String

  internalTitel_not_contains: String

  internalTitel_not_in: [String]

  sys: SysFilter

  textList_contains_all: [String]

  textList_contains_none: [String]

  textList_contains_some: [String]

  textList_exists: Boolean

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

type ChecklistLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum ChecklistOrder {
  internalTitel_ASC

  internalTitel_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/contactBox)
"""
type ContactBox implements Entry & _Node {
  _id: ID!

  button(locale: String, preview: Boolean): ContactBoxButton

  content(locale: String): ContactBoxContent

  contentfulMetadata: ContentfulMetadata!

  icon(locale: String): String

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): ContactBoxLinkingCollections

  sys: Sys!

  title(locale: String): String
}

union ContactBoxButton = ActionButton|ExternalLink|InternalLink

type ContactBoxCollection {
  items: [ContactBox]!

  limit: Int!

  skip: Int!

  total: Int!
}

type ContactBoxContent {
  json: JSON!

  links: ContactBoxContentLinks!
}

type ContactBoxContentAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type ContactBoxContentEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type ContactBoxContentLinks {
  assets: ContactBoxContentAssets!

  entries: ContactBoxContentEntries!

  resources: ContactBoxContentResources!
}

type ContactBoxContentResources {
  block: [ContactBoxContentResourcesBlock!]!

  hyperlink: [ContactBoxContentResourcesHyperlink!]!

  inline: [ContactBoxContentResourcesInline!]!
}

type ContactBoxContentResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type ContactBoxContentResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type ContactBoxContentResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

input ContactBoxFilter {
  AND: [ContactBoxFilter]

  OR: [ContactBoxFilter]

  button_exists: Boolean

  content_contains: String

  content_exists: Boolean

  content_not_contains: String

  contentfulMetadata: ContentfulMetadataFilter

  icon: String

  icon_contains: String

  icon_exists: Boolean

  icon_in: [String]

  icon_not: String

  icon_not_contains: String

  icon_not_in: [String]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

type ContactBoxLinkingCollections {
  contactPageCollection(limit: Int = 100, locale: String, order: [ContactBoxLinkingCollectionsContactPageCollectionOrder], preview: Boolean, skip: Int = 0): ContactPageCollection

  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  footerColumnCollection(limit: Int = 100, locale: String, order: [ContactBoxLinkingCollectionsFooterColumnCollectionOrder], preview: Boolean, skip: Int = 0): FooterColumnCollection

  noSearchResultCollection(limit: Int = 100, locale: String, order: [ContactBoxLinkingCollectionsNoSearchResultCollectionOrder], preview: Boolean, skip: Int = 0): NoSearchResultCollection
}

enum ContactBoxLinkingCollectionsContactPageCollectionOrder {
  headline_ASC

  headline_DESC

  id_ASC

  id_DESC

  subtitle_ASC

  subtitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum ContactBoxLinkingCollectionsFooterColumnCollectionOrder {
  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

enum ContactBoxLinkingCollectionsNoSearchResultCollectionOrder {
  helpHeading_ASC

  helpHeading_DESC

  id_ASC

  id_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum ContactBoxOrder {
  icon_ASC

  icon_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/contactPage)
"""
type ContactPage implements Entry & _Node {
  _id: ID!

  contactItemsCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0, where: ContactPageContactItemsFilter): ContactPageContactItemsCollection

  contentfulMetadata: ContentfulMetadata!

  headline(locale: String): String

  id(locale: String): String

  linkedFrom(allowedLocales: [String]): ContactPageLinkingCollections

  subtitle(locale: String): String

  sys: Sys!
}

type ContactPageCollection {
  items: [ContactPage]!

  limit: Int!

  skip: Int!

  total: Int!
}

type ContactPageContactItemsCollection {
  items: [ContactPageContactItemsItem]!

  limit: Int!

  skip: Int!

  total: Int!
}

input ContactPageContactItemsFilter {
  AND: [ContactPageContactItemsFilter]

  OR: [ContactPageContactItemsFilter]

  contentfulMetadata: ContentfulMetadataFilter

  icon: String

  icon_contains: String

  icon_exists: Boolean

  icon_in: [String]

  icon_not: String

  icon_not_contains: String

  icon_not_in: [String]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

union ContactPageContactItemsItem = ContactBox|PhoneBox

input ContactPageFilter {
  AND: [ContactPageFilter]

  OR: [ContactPageFilter]

  contactItems: cfcontactItemsMultiTypeNestedFilter

  contactItemsCollection_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  headline: String

  headline_contains: String

  headline_exists: Boolean

  headline_in: [String]

  headline_not: String

  headline_not_contains: String

  headline_not_in: [String]

  id: String

  id_contains: String

  id_exists: Boolean

  id_in: [String]

  id_not: String

  id_not_contains: String

  id_not_in: [String]

  subtitle: String

  subtitle_contains: String

  subtitle_exists: Boolean

  subtitle_in: [String]

  subtitle_not: String

  subtitle_not_contains: String

  subtitle_not_in: [String]

  sys: SysFilter
}

type ContactPageLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum ContactPageOrder {
  headline_ASC

  headline_DESC

  id_ASC

  id_DESC

  subtitle_ASC

  subtitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/contentItemAsset)
"""
type ContentItemAsset implements Entry & _Node {
  _id: ID!

  alignment(locale: String): String

  contentfulMetadata: ContentfulMetadata!

  imageCloudinary(locale: String): JSON

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): ContentItemAssetLinkingCollections

  sys: Sys!

  type(locale: String): String
}

type ContentItemAssetCollection {
  items: [ContentItemAsset]!

  limit: Int!

  skip: Int!

  total: Int!
}

input ContentItemAssetFilter {
  AND: [ContentItemAssetFilter]

  OR: [ContentItemAssetFilter]

  alignment: String

  alignment_contains: String

  alignment_exists: Boolean

  alignment_in: [String]

  alignment_not: String

  alignment_not_contains: String

  alignment_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  imageCloudinary_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter

  type: String

  type_contains: String

  type_exists: Boolean

  type_in: [String]

  type_not: String

  type_not_contains: String

  type_not_in: [String]
}

type ContentItemAssetLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum ContentItemAssetOrder {
  alignment_ASC

  alignment_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  type_ASC

  type_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/contentPage)
"""
type ContentPage implements Entry & _Node {
  _id: ID!

  backlink(locale: String, preview: Boolean, where: ContentPageBacklinkFilter): ContentPageBacklink

  content(locale: String): ContentPageContent

  contentfulMetadata: ContentfulMetadata!

  horizontalAlignment(locale: String): String

  linkedFrom(allowedLocales: [String]): ContentPageLinkingCollections

  name(locale: String): String

  seoContent(locale: String, preview: Boolean, where: SeoContentFilter): SeoContent

  slug(locale: String): String

  sys: Sys!

  trigger(locale: String, preview: Boolean, where: TopLevelTriggerFilter): TopLevelTrigger
}

union ContentPageBacklink = ExternalLink|InternalLink

input ContentPageBacklinkFilter {
  AND: [ContentPageBacklinkFilter]

  OR: [ContentPageBacklinkFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

type ContentPageCollection {
  items: [ContentPage]!

  limit: Int!

  skip: Int!

  total: Int!
}

type ContentPageContent {
  json: JSON!

  links: ContentPageContentLinks!
}

type ContentPageContentAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type ContentPageContentEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type ContentPageContentLinks {
  assets: ContentPageContentAssets!

  entries: ContentPageContentEntries!

  resources: ContentPageContentResources!
}

type ContentPageContentResources {
  block: [ContentPageContentResourcesBlock!]!

  hyperlink: [ContentPageContentResourcesHyperlink!]!

  inline: [ContentPageContentResourcesInline!]!
}

type ContentPageContentResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type ContentPageContentResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type ContentPageContentResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

input ContentPageFilter {
  AND: [ContentPageFilter]

  OR: [ContentPageFilter]

  backlink: cfbacklinkMultiTypeNestedFilter

  backlink_exists: Boolean

  content_contains: String

  content_exists: Boolean

  content_not_contains: String

  contentfulMetadata: ContentfulMetadataFilter

  horizontalAlignment: String

  horizontalAlignment_contains: String

  horizontalAlignment_exists: Boolean

  horizontalAlignment_in: [String]

  horizontalAlignment_not: String

  horizontalAlignment_not_contains: String

  horizontalAlignment_not_in: [String]

  name: String

  name_contains: String

  name_exists: Boolean

  name_in: [String]

  name_not: String

  name_not_contains: String

  name_not_in: [String]

  seoContent: cfSeoContentNestedFilter

  seoContent_exists: Boolean

  slug: String

  slug_contains: String

  slug_exists: Boolean

  slug_in: [String]

  slug_not: String

  slug_not_contains: String

  slug_not_in: [String]

  sys: SysFilter

  trigger: cfTopLevelTriggerNestedFilter

  trigger_exists: Boolean
}

type ContentPageLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  internalLinkCollection(limit: Int = 100, locale: String, order: [ContentPageLinkingCollectionsInternalLinkCollectionOrder], preview: Boolean, skip: Int = 0): InternalLinkCollection
}

enum ContentPageLinkingCollectionsInternalLinkCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  label_ASC

  label_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum ContentPageOrder {
  horizontalAlignment_ASC

  horizontalAlignment_DESC

  name_ASC

  name_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

type ContentfulMetadata {
  tags: [ContentfulTag]!

  concepts: [TaxonomyConcept]!
}

"""
Represents a taxonomy concept entity for finding and organizing content easily.
        Find out more here: https://www.contentful.com/developers/docs/references/content-delivery-api/#/reference/content-concepts
"""
type TaxonomyConcept {
  id: String
}

input ContentfulMetadataFilter {
  tags: ContentfulMetadataTagsFilter

  tags_exists: Boolean

  concepts_exists: Boolean

  concepts: ContentfulMetadataConceptsFilter
}

input ContentfulMetadataConceptsFilter {
  id_contains_all: [String]

  id_contains_some: [String]

  id_contains_none: [String]

  descendants: ContentfulMetadataConceptsDescendantsFilter
}

input ContentfulMetadataConceptsDescendantsFilter {
  id_contains_all: [String]

  id_contains_some: [String]

  id_contains_none: [String]
}

input ContentfulMetadataTagsFilter {
  id_contains_all: [String]

  id_contains_none: [String]

  id_contains_some: [String]
}

"""
Represents a tag entity for finding and organizing content easily.
      Find out more here: https://www.contentful.com/developers/docs/references/content-delivery-api/#/reference/content-tags
"""
type ContentfulTag {
  id: String

  name: String
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/cookieList)
"""
type CookieList implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): CookieListLinkingCollections

  sys: Sys!
}

type CookieListCollection {
  items: [CookieList]!

  limit: Int!

  skip: Int!

  total: Int!
}

input CookieListFilter {
  AND: [CookieListFilter]

  OR: [CookieListFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter
}

type CookieListLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum CookieListOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/coupon)
"""
type Coupon implements Entry & _Node {
  _id: ID!

  code(locale: String): String

  code2(locale: String): String

  contentfulMetadata: ContentfulMetadata!

  descriptionText(locale: String): String

  hint(locale: String): String

  internalTitle(locale: String): String

  layoutColor(locale: String): JSON

  linkedFrom(allowedLocales: [String]): CouponLinkingCollections

  promotionText(locale: String): CouponPromotionText

  showQrCode(locale: String): Boolean

  sys: Sys!
}

type CouponCollection {
  items: [Coupon]!

  limit: Int!

  skip: Int!

  total: Int!
}

input CouponFilter {
  AND: [CouponFilter]

  OR: [CouponFilter]

  code: String

  code2: String

  code2_contains: String

  code2_exists: Boolean

  code2_in: [String]

  code2_not: String

  code2_not_contains: String

  code2_not_in: [String]

  code_contains: String

  code_exists: Boolean

  code_in: [String]

  code_not: String

  code_not_contains: String

  code_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  descriptionText: String

  descriptionText_contains: String

  descriptionText_exists: Boolean

  descriptionText_in: [String]

  descriptionText_not: String

  descriptionText_not_contains: String

  descriptionText_not_in: [String]

  hint: String

  hint_contains: String

  hint_exists: Boolean

  hint_in: [String]

  hint_not: String

  hint_not_contains: String

  hint_not_in: [String]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  layoutColor_exists: Boolean

  promotionText_contains: String

  promotionText_exists: Boolean

  promotionText_not_contains: String

  showQrCode: Boolean

  showQrCode_exists: Boolean

  showQrCode_not: Boolean

  sys: SysFilter
}

type CouponLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  modalCollection(limit: Int = 100, locale: String, order: [CouponLinkingCollectionsModalCollectionOrder], preview: Boolean, skip: Int = 0): ModalCollection
}

enum CouponLinkingCollectionsModalCollectionOrder {
  headline_ASC

  headline_DESC

  id_ASC

  id_DESC

  linkText_ASC

  linkText_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum CouponOrder {
  code2_ASC

  code2_DESC

  code_ASC

  code_DESC

  internalTitle_ASC

  internalTitle_DESC

  showQrCode_ASC

  showQrCode_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

type CouponPromotionText {
  json: JSON!

  links: CouponPromotionTextLinks!
}

type CouponPromotionTextAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type CouponPromotionTextEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type CouponPromotionTextLinks {
  assets: CouponPromotionTextAssets!

  entries: CouponPromotionTextEntries!

  resources: CouponPromotionTextResources!
}

type CouponPromotionTextResources {
  block: [CouponPromotionTextResourcesBlock!]!

  hyperlink: [CouponPromotionTextResourcesHyperlink!]!

  inline: [CouponPromotionTextResourcesInline!]!
}

type CouponPromotionTextResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type CouponPromotionTextResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type CouponPromotionTextResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/dataBankDetails)
"""
type DataBankDetails implements Entry & _Node {
  _id: ID!

  bankName(locale: String): String

  bic(locale: String): String

  contentfulMetadata: ContentfulMetadata!

  iban(locale: String): String

  linkedFrom(allowedLocales: [String]): DataBankDetailsLinkingCollections

  sys: Sys!
}

type DataBankDetailsCollection {
  items: [DataBankDetails]!

  limit: Int!

  skip: Int!

  total: Int!
}

input DataBankDetailsFilter {
  AND: [DataBankDetailsFilter]

  OR: [DataBankDetailsFilter]

  bankName: String

  bankName_contains: String

  bankName_exists: Boolean

  bankName_in: [String]

  bankName_not: String

  bankName_not_contains: String

  bankName_not_in: [String]

  bic: String

  bic_contains: String

  bic_exists: Boolean

  bic_in: [String]

  bic_not: String

  bic_not_contains: String

  bic_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  iban: String

  iban_contains: String

  iban_exists: Boolean

  iban_in: [String]

  iban_not: String

  iban_not_contains: String

  iban_not_in: [String]

  sys: SysFilter
}

type DataBankDetailsLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum DataBankDetailsOrder {
  bankName_ASC

  bankName_DESC

  bic_ASC

  bic_DESC

  iban_ASC

  iban_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/dataProvider)
"""
type DataProvider implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  data(locale: String): JSON

  linkedFrom(allowedLocales: [String]): DataProviderLinkingCollections

  name(locale: String): String

  sys: Sys!

  text(locale: String): DataProviderText
}

type DataProviderCollection {
  items: [DataProvider]!

  limit: Int!

  skip: Int!

  total: Int!
}

input DataProviderFilter {
  AND: [DataProviderFilter]

  OR: [DataProviderFilter]

  contentfulMetadata: ContentfulMetadataFilter

  data_exists: Boolean

  name: String

  name_contains: String

  name_exists: Boolean

  name_in: [String]

  name_not: String

  name_not_contains: String

  name_not_in: [String]

  sys: SysFilter

  text_contains: String

  text_exists: Boolean

  text_not_contains: String
}

type DataProviderLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum DataProviderOrder {
  name_ASC

  name_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

type DataProviderText {
  json: JSON!

  links: DataProviderTextLinks!
}

type DataProviderTextAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type DataProviderTextEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type DataProviderTextLinks {
  assets: DataProviderTextAssets!

  entries: DataProviderTextEntries!

  resources: DataProviderTextResources!
}

type DataProviderTextResources {
  block: [DataProviderTextResourcesBlock!]!

  hyperlink: [DataProviderTextResourcesHyperlink!]!

  inline: [DataProviderTextResourcesInline!]!
}

type DataProviderTextResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type DataProviderTextResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type DataProviderTextResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

"""
A date-time string at UTC, such as 2007-12-03T10:15:30Z,
    compliant with the 'date-time' format outlined in section 5.6 of
    the RFC 3339 profile of the ISO 8601 standard for representation
    of dates and times using the Gregorian calendar.
"""
scalar DateTime

"""
The 'Dimension' type represents dimensions as whole numeric values between `1` and `4000`.
"""
scalar Dimension

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/dosDonts)
"""
type DosDonts implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): DosDontsLinkingCollections

  sys: Sys!

  textListDonts(locale: String): [String]

  textListDos(locale: String): [String]

  titleDonts(locale: String): String

  titleDos(locale: String): String
}

type DosDontsCollection {
  items: [DosDonts]!

  limit: Int!

  skip: Int!

  total: Int!
}

input DosDontsFilter {
  AND: [DosDontsFilter]

  OR: [DosDontsFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter

  textListDonts_contains_all: [String]

  textListDonts_contains_none: [String]

  textListDonts_contains_some: [String]

  textListDonts_exists: Boolean

  textListDos_contains_all: [String]

  textListDos_contains_none: [String]

  textListDos_contains_some: [String]

  textListDos_exists: Boolean

  titleDonts: String

  titleDonts_contains: String

  titleDonts_exists: Boolean

  titleDonts_in: [String]

  titleDonts_not: String

  titleDonts_not_contains: String

  titleDonts_not_in: [String]

  titleDos: String

  titleDos_contains: String

  titleDos_exists: Boolean

  titleDos_in: [String]

  titleDos_not: String

  titleDos_not_contains: String

  titleDos_not_in: [String]
}

type DosDontsLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum DosDontsOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  titleDonts_ASC

  titleDonts_DESC

  titleDos_ASC

  titleDos_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/eccItem)
"""
type EccItem implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  eccNumber(locale: String): Int

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): EccItemLinkingCollections

  pageType(locale: String): String

  referrer(locale: String): String

  sys: Sys!

  url(locale: String): String
}

type EccItemCollection {
  items: [EccItem]!

  limit: Int!

  skip: Int!

  total: Int!
}

input EccItemFilter {
  AND: [EccItemFilter]

  OR: [EccItemFilter]

  contentfulMetadata: ContentfulMetadataFilter

  eccNumber: Int

  eccNumber_exists: Boolean

  eccNumber_gt: Int

  eccNumber_gte: Int

  eccNumber_in: [Int]

  eccNumber_lt: Int

  eccNumber_lte: Int

  eccNumber_not: Int

  eccNumber_not_in: [Int]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  pageType: String

  pageType_contains: String

  pageType_exists: Boolean

  pageType_in: [String]

  pageType_not: String

  pageType_not_contains: String

  pageType_not_in: [String]

  referrer: String

  referrer_contains: String

  referrer_exists: Boolean

  referrer_in: [String]

  referrer_not: String

  referrer_not_contains: String

  referrer_not_in: [String]

  sys: SysFilter

  url: String

  url_contains: String

  url_exists: Boolean

  url_in: [String]

  url_not: String

  url_not_contains: String

  url_not_in: [String]
}

type EccItemLinkingCollections {
  eccListCollection(limit: Int = 100, locale: String, order: [EccItemLinkingCollectionsEccListCollectionOrder], preview: Boolean, skip: Int = 0): EccListCollection

  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum EccItemLinkingCollectionsEccListCollectionOrder {
  directApp_ASC

  directApp_DESC

  direct_ASC

  direct_DESC

  fallbackApp_ASC

  fallbackApp_DESC

  fallback_ASC

  fallback_DESC

  id_ASC

  id_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum EccItemOrder {
  eccNumber_ASC

  eccNumber_DESC

  internalTitle_ASC

  internalTitle_DESC

  pageType_ASC

  pageType_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  url_ASC

  url_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/eccList)
"""
type EccList implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  direct(locale: String): Int

  directApp(locale: String): Int

  fallback(locale: String): Int

  fallbackApp(locale: String): Int

  id(locale: String): String

  itemsCollection(limit: Int = 100, locale: String, order: [EccListItemsCollectionOrder], preview: Boolean, skip: Int = 0, where: EccItemFilter): EccListItemsCollection

  linkedFrom(allowedLocales: [String]): EccListLinkingCollections

  sys: Sys!
}

type EccListCollection {
  items: [EccList]!

  limit: Int!

  skip: Int!

  total: Int!
}

input EccListFilter {
  AND: [EccListFilter]

  OR: [EccListFilter]

  contentfulMetadata: ContentfulMetadataFilter

  direct: Int

  directApp: Int

  directApp_exists: Boolean

  directApp_gt: Int

  directApp_gte: Int

  directApp_in: [Int]

  directApp_lt: Int

  directApp_lte: Int

  directApp_not: Int

  directApp_not_in: [Int]

  direct_exists: Boolean

  direct_gt: Int

  direct_gte: Int

  direct_in: [Int]

  direct_lt: Int

  direct_lte: Int

  direct_not: Int

  direct_not_in: [Int]

  fallback: Int

  fallbackApp: Int

  fallbackApp_exists: Boolean

  fallbackApp_gt: Int

  fallbackApp_gte: Int

  fallbackApp_in: [Int]

  fallbackApp_lt: Int

  fallbackApp_lte: Int

  fallbackApp_not: Int

  fallbackApp_not_in: [Int]

  fallback_exists: Boolean

  fallback_gt: Int

  fallback_gte: Int

  fallback_in: [Int]

  fallback_lt: Int

  fallback_lte: Int

  fallback_not: Int

  fallback_not_in: [Int]

  id: String

  id_contains: String

  id_exists: Boolean

  id_in: [String]

  id_not: String

  id_not_contains: String

  id_not_in: [String]

  items: cfEccItemNestedFilter

  itemsCollection_exists: Boolean

  sys: SysFilter
}

type EccListItemsCollection {
  items: [EccItem]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum EccListItemsCollectionOrder {
  eccNumber_ASC

  eccNumber_DESC

  internalTitle_ASC

  internalTitle_DESC

  pageType_ASC

  pageType_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  url_ASC

  url_DESC
}

type EccListLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum EccListOrder {
  directApp_ASC

  directApp_DESC

  direct_ASC

  direct_DESC

  fallbackApp_ASC

  fallbackApp_DESC

  fallback_ASC

  fallback_DESC

  id_ASC

  id_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

interface Entry {
  contentfulMetadata: ContentfulMetadata!

  sys: Sys!
}

type EntryCollection {
  items: [Entry]!

  limit: Int!

  skip: Int!

  total: Int!
}

input EntryFilter {
  AND: [EntryFilter]

  OR: [EntryFilter]

  contentfulMetadata: ContentfulMetadataFilter

  sys: SysFilter
}

enum EntryOrder {
  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/errorPage)
"""
type ErrorPage implements Entry & _Node {
  _id: ID!

  content(locale: String): ErrorPageContent

  contentfulMetadata: ContentfulMetadata!

  id(locale: String): String

  image(locale: String, preview: Boolean): Asset

  linkedFrom(allowedLocales: [String]): ErrorPageLinkingCollections

  linksCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0, where: ErrorPageLinksFilter): ErrorPageLinksCollection

  sys: Sys!

  text(locale: String): String

  title(locale: String): String
}

type ErrorPageCollection {
  items: [ErrorPage]!

  limit: Int!

  skip: Int!

  total: Int!
}

type ErrorPageContent {
  json: JSON!

  links: ErrorPageContentLinks!
}

type ErrorPageContentAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type ErrorPageContentEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type ErrorPageContentLinks {
  assets: ErrorPageContentAssets!

  entries: ErrorPageContentEntries!

  resources: ErrorPageContentResources!
}

type ErrorPageContentResources {
  block: [ErrorPageContentResourcesBlock!]!

  hyperlink: [ErrorPageContentResourcesHyperlink!]!

  inline: [ErrorPageContentResourcesInline!]!
}

type ErrorPageContentResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type ErrorPageContentResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type ErrorPageContentResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

input ErrorPageFilter {
  AND: [ErrorPageFilter]

  OR: [ErrorPageFilter]

  content_contains: String

  content_exists: Boolean

  content_not_contains: String

  contentfulMetadata: ContentfulMetadataFilter

  id: String

  id_contains: String

  id_exists: Boolean

  id_in: [String]

  id_not: String

  id_not_contains: String

  id_not_in: [String]

  image_exists: Boolean

  links: cflinksMultiTypeNestedFilter

  linksCollection_exists: Boolean

  sys: SysFilter

  text: String

  text_contains: String

  text_exists: Boolean

  text_in: [String]

  text_not: String

  text_not_contains: String

  text_not_in: [String]

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

type ErrorPageLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

type ErrorPageLinksCollection {
  items: [ErrorPageLinksItem]!

  limit: Int!

  skip: Int!

  total: Int!
}

input ErrorPageLinksFilter {
  AND: [ErrorPageLinksFilter]

  OR: [ErrorPageLinksFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

union ErrorPageLinksItem = ExternalLink|InternalLink

enum ErrorPageOrder {
  id_ASC

  id_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/externalLink)
"""
type ExternalLink implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  externalTarget(locale: String): String

  internalTitle(locale: String): String

  label(locale: String): String

  linkedFrom(allowedLocales: [String]): ExternalLinkLinkingCollections

  openInNewTab(locale: String): Boolean

  sys: Sys!
}

type ExternalLinkCollection {
  items: [ExternalLink]!

  limit: Int!

  skip: Int!

  total: Int!
}

input ExternalLinkFilter {
  AND: [ExternalLinkFilter]

  OR: [ExternalLinkFilter]

  contentfulMetadata: ContentfulMetadataFilter

  externalTarget: String

  externalTarget_contains: String

  externalTarget_exists: Boolean

  externalTarget_in: [String]

  externalTarget_not: String

  externalTarget_not_contains: String

  externalTarget_not_in: [String]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  openInNewTab: Boolean

  openInNewTab_exists: Boolean

  openInNewTab_not: Boolean

  sys: SysFilter
}

type ExternalLinkLinkingCollections {
  bannerCollection(limit: Int = 100, locale: String, order: [ExternalLinkLinkingCollectionsBannerCollectionOrder], preview: Boolean, skip: Int = 0): BannerCollection

  boxCollection(limit: Int = 100, locale: String, order: [ExternalLinkLinkingCollectionsBoxCollectionOrder], preview: Boolean, skip: Int = 0): BoxCollection

  buttonCollection(limit: Int = 100, locale: String, order: [ExternalLinkLinkingCollectionsButtonCollectionOrder], preview: Boolean, skip: Int = 0): ButtonCollection

  contactBoxCollection(limit: Int = 100, locale: String, order: [ExternalLinkLinkingCollectionsContactBoxCollectionOrder], preview: Boolean, skip: Int = 0): ContactBoxCollection

  contentPageCollection(limit: Int = 100, locale: String, order: [ExternalLinkLinkingCollectionsContentPageCollectionOrder], preview: Boolean, skip: Int = 0): ContentPageCollection

  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  errorPageCollection(limit: Int = 100, locale: String, order: [ExternalLinkLinkingCollectionsErrorPageCollectionOrder], preview: Boolean, skip: Int = 0): ErrorPageCollection

  footerColumnCollection(limit: Int = 100, locale: String, order: [ExternalLinkLinkingCollectionsFooterColumnCollectionOrder], preview: Boolean, skip: Int = 0): FooterColumnCollection

  inspirationItemCollection(limit: Int = 100, locale: String, order: [ExternalLinkLinkingCollectionsInspirationItemCollectionOrder], preview: Boolean, skip: Int = 0): InspirationItemCollection

  modalCollection(limit: Int = 100, locale: String, order: [ExternalLinkLinkingCollectionsModalCollectionOrder], preview: Boolean, skip: Int = 0): ModalCollection

  outfitPageCollection(limit: Int = 100, locale: String, order: [ExternalLinkLinkingCollectionsOutfitPageCollectionOrder], preview: Boolean, skip: Int = 0): OutfitPageCollection

  pageFooterCollection(limit: Int = 100, locale: String, order: [ExternalLinkLinkingCollectionsPageFooterCollectionOrder], preview: Boolean, skip: Int = 0): PageFooterCollection

  promotionCollection(limit: Int = 100, locale: String, order: [ExternalLinkLinkingCollectionsPromotionCollectionOrder], preview: Boolean, skip: Int = 0): PromotionCollection

  seoAdviceCollection(limit: Int = 100, locale: String, order: [ExternalLinkLinkingCollectionsSeoAdviceCollectionOrder], preview: Boolean, skip: Int = 0): SeoAdviceCollection
}

enum ExternalLinkLinkingCollectionsBannerCollectionOrder {
  countdownStart_ASC

  countdownStart_DESC

  countdown_ASC

  countdown_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  trackingName_ASC

  trackingName_DESC
}

enum ExternalLinkLinkingCollectionsBoxCollectionOrder {
  boxText_ASC

  boxText_DESC

  boxTitle_ASC

  boxTitle_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  trackingName_ASC

  trackingName_DESC
}

enum ExternalLinkLinkingCollectionsButtonCollectionOrder {
  color_ASC

  color_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  trackingName_ASC

  trackingName_DESC
}

enum ExternalLinkLinkingCollectionsContactBoxCollectionOrder {
  icon_ASC

  icon_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

enum ExternalLinkLinkingCollectionsContentPageCollectionOrder {
  horizontalAlignment_ASC

  horizontalAlignment_DESC

  name_ASC

  name_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum ExternalLinkLinkingCollectionsErrorPageCollectionOrder {
  id_ASC

  id_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

enum ExternalLinkLinkingCollectionsFooterColumnCollectionOrder {
  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

enum ExternalLinkLinkingCollectionsInspirationItemCollectionOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum ExternalLinkLinkingCollectionsModalCollectionOrder {
  headline_ASC

  headline_DESC

  id_ASC

  id_DESC

  linkText_ASC

  linkText_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum ExternalLinkLinkingCollectionsOutfitPageCollectionOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  moreOutfitsHeadline_ASC

  moreOutfitsHeadline_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum ExternalLinkLinkingCollectionsPageFooterCollectionOrder {
  id_ASC

  id_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum ExternalLinkLinkingCollectionsPromotionCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

enum ExternalLinkLinkingCollectionsSeoAdviceCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

enum ExternalLinkOrder {
  externalTarget_ASC

  externalTarget_DESC

  internalTitle_ASC

  internalTitle_DESC

  label_ASC

  label_DESC

  openInNewTab_ASC

  openInNewTab_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/footerColumn)
"""
type FooterColumn implements Entry & _Node {
  _id: ID!

  contentCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0, where: FooterColumnContentFilter): FooterColumnContentCollection

  contentfulMetadata: ContentfulMetadata!

  linkedFrom(allowedLocales: [String]): FooterColumnLinkingCollections

  sys: Sys!

  title(locale: String): String

  titleLink(locale: String, preview: Boolean, where: FooterColumnTitleLinkFilter): FooterColumnTitleLink
}

type FooterColumnCollection {
  items: [FooterColumn]!

  limit: Int!

  skip: Int!

  total: Int!
}

type FooterColumnContentCollection {
  items: [FooterColumnContentItem]!

  limit: Int!

  skip: Int!

  total: Int!
}

input FooterColumnContentFilter {
  AND: [FooterColumnContentFilter]

  OR: [FooterColumnContentFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter
}

union FooterColumnContentItem = ContactBox|ExternalLink|InternalLink|PhoneBox|Promotion

input FooterColumnFilter {
  AND: [FooterColumnFilter]

  OR: [FooterColumnFilter]

  content: cfcontentMultiTypeNestedFilter

  contentCollection_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  sys: SysFilter

  title: String

  titleLink: cftitleLinkMultiTypeNestedFilter

  titleLink_exists: Boolean

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

type FooterColumnLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  pageFooterCollection(limit: Int = 100, locale: String, order: [FooterColumnLinkingCollectionsPageFooterCollectionOrder], preview: Boolean, skip: Int = 0): PageFooterCollection
}

enum FooterColumnLinkingCollectionsPageFooterCollectionOrder {
  id_ASC

  id_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum FooterColumnOrder {
  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

union FooterColumnTitleLink = ExternalLink|InternalLink

input FooterColumnTitleLinkFilter {
  AND: [FooterColumnTitleLinkFilter]

  OR: [FooterColumnTitleLinkFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/gkAirOutputConfig)
"""
type GkAirOutputConfig implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  imageType(locale: String): String

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): GkAirOutputConfigLinkingCollections

  outputId(locale: String): String

  sys: Sys!
}

type GkAirOutputConfigCollection {
  items: [GkAirOutputConfig]!

  limit: Int!

  skip: Int!

  total: Int!
}

input GkAirOutputConfigFilter {
  AND: [GkAirOutputConfigFilter]

  OR: [GkAirOutputConfigFilter]

  contentfulMetadata: ContentfulMetadataFilter

  imageType: String

  imageType_contains: String

  imageType_exists: Boolean

  imageType_in: [String]

  imageType_not: String

  imageType_not_contains: String

  imageType_not_in: [String]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  outputId: String

  outputId_contains: String

  outputId_exists: Boolean

  outputId_in: [String]

  outputId_not: String

  outputId_not_contains: String

  outputId_not_in: [String]

  sys: SysFilter
}

type GkAirOutputConfigLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  gkAirServiceCollection(limit: Int = 100, locale: String, order: [GkAirOutputConfigLinkingCollectionsGkAirServiceCollectionOrder], preview: Boolean, skip: Int = 0): GkAirServiceCollection
}

enum GkAirOutputConfigLinkingCollectionsGkAirServiceCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  serviceId_ASC

  serviceId_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum GkAirOutputConfigOrder {
  imageType_ASC

  imageType_DESC

  internalTitle_ASC

  internalTitle_DESC

  outputId_ASC

  outputId_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/gkAirParameter)
"""
type GkAirParameter implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): GkAirParameterLinkingCollections

  name(locale: String): String

  sys: Sys!

  value(locale: String): String
}

type GkAirParameterCollection {
  items: [GkAirParameter]!

  limit: Int!

  skip: Int!

  total: Int!
}

input GkAirParameterFilter {
  AND: [GkAirParameterFilter]

  OR: [GkAirParameterFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  name: String

  name_contains: String

  name_exists: Boolean

  name_in: [String]

  name_not: String

  name_not_contains: String

  name_not_in: [String]

  sys: SysFilter

  value: String

  value_contains: String

  value_exists: Boolean

  value_in: [String]

  value_not: String

  value_not_contains: String

  value_not_in: [String]
}

type GkAirParameterLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  gkAirServiceCollection(limit: Int = 100, locale: String, order: [GkAirParameterLinkingCollectionsGkAirServiceCollectionOrder], preview: Boolean, skip: Int = 0): GkAirServiceCollection
}

enum GkAirParameterLinkingCollectionsGkAirServiceCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  serviceId_ASC

  serviceId_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum GkAirParameterOrder {
  internalTitle_ASC

  internalTitle_DESC

  name_ASC

  name_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  value_ASC

  value_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/gkAirService)
"""
type GkAirService implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): GkAirServiceLinkingCollections

  outputConfigCollection(limit: Int = 100, locale: String, order: [GkAirServiceOutputConfigCollectionOrder], preview: Boolean, skip: Int = 0, where: GkAirOutputConfigFilter): GkAirServiceOutputConfigCollection

  parametersCollection(limit: Int = 100, locale: String, order: [GkAirServiceParametersCollectionOrder], preview: Boolean, skip: Int = 0, where: GkAirParameterFilter): GkAirServiceParametersCollection

  serviceId(locale: String): String

  sys: Sys!
}

type GkAirServiceCollection {
  items: [GkAirService]!

  limit: Int!

  skip: Int!

  total: Int!
}

input GkAirServiceFilter {
  AND: [GkAirServiceFilter]

  OR: [GkAirServiceFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  outputConfig: cfGkAirOutputConfigNestedFilter

  outputConfigCollection_exists: Boolean

  parameters: cfGkAirParameterNestedFilter

  parametersCollection_exists: Boolean

  serviceId: String

  serviceId_contains: String

  serviceId_exists: Boolean

  serviceId_in: [String]

  serviceId_not: String

  serviceId_not_contains: String

  serviceId_not_in: [String]

  sys: SysFilter
}

type GkAirServiceLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  recommendationGkAirCollection(limit: Int = 100, locale: String, order: [GkAirServiceLinkingCollectionsRecommendationGkAirCollectionOrder], preview: Boolean, skip: Int = 0): RecommendationGkAirCollection
}

enum GkAirServiceLinkingCollectionsRecommendationGkAirCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  outputId_ASC

  outputId_DESC

  recommendationType_ASC

  recommendationType_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum GkAirServiceOrder {
  internalTitle_ASC

  internalTitle_DESC

  serviceId_ASC

  serviceId_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

type GkAirServiceOutputConfigCollection {
  items: [GkAirOutputConfig]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum GkAirServiceOutputConfigCollectionOrder {
  imageType_ASC

  imageType_DESC

  internalTitle_ASC

  internalTitle_DESC

  outputId_ASC

  outputId_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

type GkAirServiceParametersCollection {
  items: [GkAirParameter]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum GkAirServiceParametersCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  name_ASC

  name_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  value_ASC

  value_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/globalContent)
"""
type GlobalContent implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): GlobalContentLinkingCollections

  paymentLogo1Cloudinary(locale: String): JSON

  paymentLogo2Cloudinary(locale: String): JSON

  paymentLogo3Cloudinary(locale: String): JSON

  paymentLogo4Cloudinary(locale: String): JSON

  paymentLogo5Cloudinary(locale: String): JSON

  paymentLogo6Cloudinary(locale: String): JSON

  paymentLogo7Cloudinary(locale: String): JSON

  paymentLogo8Cloudinary(locale: String): JSON

  paymentLogosCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): AssetCollection

  sys: Sys!
}

type GlobalContentCollection {
  items: [GlobalContent]!

  limit: Int!

  skip: Int!

  total: Int!
}

input GlobalContentFilter {
  AND: [GlobalContentFilter]

  OR: [GlobalContentFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  paymentLogo1Cloudinary_exists: Boolean

  paymentLogo2Cloudinary_exists: Boolean

  paymentLogo3Cloudinary_exists: Boolean

  paymentLogo4Cloudinary_exists: Boolean

  paymentLogo5Cloudinary_exists: Boolean

  paymentLogo6Cloudinary_exists: Boolean

  paymentLogo7Cloudinary_exists: Boolean

  paymentLogo8Cloudinary_exists: Boolean

  paymentLogosCollection_exists: Boolean

  sys: SysFilter
}

type GlobalContentLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum GlobalContentOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/healthCheck)
"""
type HealthCheck implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  key(locale: String): String

  linkedFrom(allowedLocales: [String]): HealthCheckLinkingCollections

  status(locale: String): Boolean

  sys: Sys!
}

type HealthCheckCollection {
  items: [HealthCheck]!

  limit: Int!

  skip: Int!

  total: Int!
}

input HealthCheckFilter {
  AND: [HealthCheckFilter]

  OR: [HealthCheckFilter]

  contentfulMetadata: ContentfulMetadataFilter

  key: String

  key_contains: String

  key_exists: Boolean

  key_in: [String]

  key_not: String

  key_not_contains: String

  key_not_in: [String]

  status: Boolean

  status_exists: Boolean

  status_not: Boolean

  sys: SysFilter
}

type HealthCheckLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum HealthCheckOrder {
  key_ASC

  key_DESC

  status_ASC

  status_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
The 'HexColor' type represents color in `rgb:ffffff` string format.
"""
scalar HexColor

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/homepageCreationL)
"""
type HomepageCreationL implements Entry & _Node {
  _id: ID!

  assortmentRecommendation(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  bannerV2(locale: String, preview: Boolean, where: BannerFilter): Banner

  card21Collection(limit: Int = 100, locale: String, order: [HomepageCreationLCard21CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageCreationLCard21Collection

  card24Collection(limit: Int = 100, locale: String, order: [HomepageCreationLCard24CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageCreationLCard24Collection

  card221Collection(limit: Int = 100, locale: String, order: [HomepageCreationLCard221CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageCreationLCard221Collection

  card222Collection(limit: Int = 100, locale: String, order: [HomepageCreationLCard222CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageCreationLCard222Collection

  card231Collection(limit: Int = 100, locale: String, order: [HomepageCreationLCard231CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageCreationLCard231Collection

  card232Collection(limit: Int = 100, locale: String, order: [HomepageCreationLCard232CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageCreationLCard232Collection

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): HomepageCreationLLinkingCollections

  recoCollection(limit: Int = 100, locale: String, order: [HomepageCreationLRecoCollectionOrder], preview: Boolean, skip: Int = 0, where: RecommendationGkAirFilter): HomepageCreationLRecoCollection

  seoContent(locale: String, preview: Boolean, where: SeoContentFilter): SeoContent

  sliderCollection(limit: Int = 100, locale: String, order: [HomepageCreationLSliderCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageCreationLSliderCollection

  sys: Sys!

  trigger(locale: String, preview: Boolean, where: TopLevelTriggerFilter): TopLevelTrigger

  trustmarksCollection(limit: Int = 100, locale: String, order: [HomepageCreationLTrustmarksCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageCreationLTrustmarksCollection
}

type HomepageCreationLCard21Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageCreationLCard21CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageCreationLCard24Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageCreationLCard24CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageCreationLCard221Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageCreationLCard221CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageCreationLCard222Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageCreationLCard222CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageCreationLCard231Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageCreationLCard231CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageCreationLCard232Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageCreationLCard232CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageCreationLCollection {
  items: [HomepageCreationL]!

  limit: Int!

  skip: Int!

  total: Int!
}

input HomepageCreationLFilter {
  AND: [HomepageCreationLFilter]

  OR: [HomepageCreationLFilter]

  assortmentRecommendation: cfRecommendationGkAirNestedFilter

  assortmentRecommendation_exists: Boolean

  bannerV2: cfBannerNestedFilter

  bannerV2_exists: Boolean

  card21: cfPromotionNestedFilter

  card21Collection_exists: Boolean

  card24: cfPromotionNestedFilter

  card24Collection_exists: Boolean

  card221: cfPromotionNestedFilter

  card221Collection_exists: Boolean

  card222: cfPromotionNestedFilter

  card222Collection_exists: Boolean

  card231: cfPromotionNestedFilter

  card231Collection_exists: Boolean

  card232: cfPromotionNestedFilter

  card232Collection_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  reco: cfRecommendationGkAirNestedFilter

  recoCollection_exists: Boolean

  seoContent: cfSeoContentNestedFilter

  seoContent_exists: Boolean

  slider: cfPromotionNestedFilter

  sliderCollection_exists: Boolean

  sys: SysFilter

  trigger: cfTopLevelTriggerNestedFilter

  trigger_exists: Boolean

  trustmarks: cfPromotionNestedFilter

  trustmarksCollection_exists: Boolean
}

type HomepageCreationLLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum HomepageCreationLOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

type HomepageCreationLRecoCollection {
  items: [RecommendationGkAir]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageCreationLRecoCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  outputId_ASC

  outputId_DESC

  recommendationType_ASC

  recommendationType_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

type HomepageCreationLSliderCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageCreationLSliderCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageCreationLTrustmarksCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageCreationLTrustmarksCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/homepageHeineV2)
"""
type HomepageHeineV2 implements Entry & _Node {
  _id: ID!

  assortmentRecommendation(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  cardForLivingRecommendation(locale: String, preview: Boolean, where: PromotionFilter): Promotion

  cardForOptionalRecommendation(locale: String, preview: Boolean, where: PromotionFilter): Promotion

  cardsWithOptionalTitleCollection(limit: Int = 100, locale: String, order: [HomepageHeineV2CardsWithOptionalTitleCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageHeineV2CardsWithOptionalTitleCollection

  contentfulMetadata: ContentfulMetadata!

  hero(locale: String, preview: Boolean, where: PromotionFilter): Promotion

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): HomepageHeineV2LinkingCollections

  livingRecommendationWithCard(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  optionalRecommendationWithCard(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  promotionBarCollection(limit: Int = 100, locale: String, order: [HomepageHeineV2PromotionBarCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageHeineV2PromotionBarCollection

  promotionGridCollection(limit: Int = 100, locale: String, order: [HomepageHeineV2PromotionGridCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageHeineV2PromotionGridCollection

  promotionGridTitle(locale: String): String

  seoContent(locale: String, preview: Boolean, where: SeoContentFilter): SeoContent

  serviceCardsCollection(limit: Int = 100, locale: String, order: [HomepageHeineV2ServiceCardsCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageHeineV2ServiceCardsCollection

  simpleRecommendation(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  sys: Sys!

  textCategoriesCollection(limit: Int = 100, locale: String, order: [HomepageHeineV2TextCategoriesCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageHeineV2TextCategoriesCollection

  titleTextCategories(locale: String): String

  trigger(locale: String, preview: Boolean, where: TopLevelTriggerFilter): TopLevelTrigger
}

type HomepageHeineV2CardsWithOptionalTitleCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageHeineV2CardsWithOptionalTitleCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageHeineV2Collection {
  items: [HomepageHeineV2]!

  limit: Int!

  skip: Int!

  total: Int!
}

input HomepageHeineV2Filter {
  AND: [HomepageHeineV2Filter]

  OR: [HomepageHeineV2Filter]

  assortmentRecommendation: cfRecommendationGkAirNestedFilter

  assortmentRecommendation_exists: Boolean

  cardForLivingRecommendation: cfPromotionNestedFilter

  cardForLivingRecommendation_exists: Boolean

  cardForOptionalRecommendation: cfPromotionNestedFilter

  cardForOptionalRecommendation_exists: Boolean

  cardsWithOptionalTitle: cfPromotionNestedFilter

  cardsWithOptionalTitleCollection_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  hero: cfPromotionNestedFilter

  hero_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  livingRecommendationWithCard: cfRecommendationGkAirNestedFilter

  livingRecommendationWithCard_exists: Boolean

  optionalRecommendationWithCard: cfRecommendationGkAirNestedFilter

  optionalRecommendationWithCard_exists: Boolean

  promotionBar: cfPromotionNestedFilter

  promotionBarCollection_exists: Boolean

  promotionGrid: cfPromotionNestedFilter

  promotionGridCollection_exists: Boolean

  promotionGridTitle: String

  promotionGridTitle_contains: String

  promotionGridTitle_exists: Boolean

  promotionGridTitle_in: [String]

  promotionGridTitle_not: String

  promotionGridTitle_not_contains: String

  promotionGridTitle_not_in: [String]

  seoContent: cfSeoContentNestedFilter

  seoContent_exists: Boolean

  serviceCards: cfPromotionNestedFilter

  serviceCardsCollection_exists: Boolean

  simpleRecommendation: cfRecommendationGkAirNestedFilter

  simpleRecommendation_exists: Boolean

  sys: SysFilter

  textCategories: cfPromotionNestedFilter

  textCategoriesCollection_exists: Boolean

  titleTextCategories: String

  titleTextCategories_contains: String

  titleTextCategories_exists: Boolean

  titleTextCategories_in: [String]

  titleTextCategories_not: String

  titleTextCategories_not_contains: String

  titleTextCategories_not_in: [String]

  trigger: cfTopLevelTriggerNestedFilter

  trigger_exists: Boolean
}

type HomepageHeineV2LinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum HomepageHeineV2Order {
  internalTitle_ASC

  internalTitle_DESC

  promotionGridTitle_ASC

  promotionGridTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  titleTextCategories_ASC

  titleTextCategories_DESC
}

type HomepageHeineV2PromotionBarCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageHeineV2PromotionBarCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageHeineV2PromotionGridCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageHeineV2PromotionGridCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageHeineV2ServiceCardsCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageHeineV2ServiceCardsCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageHeineV2TextCategoriesCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageHeineV2TextCategoriesCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/homepageSheego)
"""
type HomepageSheego implements Entry & _Node {
  _id: ID!

  assortmentRecommendation(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  compactPromotionBarCollection(limit: Int = 100, locale: String, order: [HomepageSheegoCompactPromotionBarCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSheegoCompactPromotionBarCollection

  contentfulMetadata: ContentfulMetadata!

  hero(locale: String, preview: Boolean, where: PromotionFilter): Promotion

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): HomepageSheegoLinkingCollections

  optionalPromotionBarCollection(limit: Int = 100, locale: String, order: [HomepageSheegoOptionalPromotionBarCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSheegoOptionalPromotionBarCollection

  optionalPromotionBarTitle(locale: String): String

  promotionBar2Collection(limit: Int = 100, locale: String, order: [HomepageSheegoPromotionBar2CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSheegoPromotionBar2Collection

  promotionBar2Title(locale: String): String

  promotionBar3Collection(limit: Int = 100, locale: String, order: [HomepageSheegoPromotionBar3CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSheegoPromotionBar3Collection

  promotionBar3Title(locale: String): String

  promotionBarCollection(limit: Int = 100, locale: String, order: [HomepageSheegoPromotionBarCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSheegoPromotionBarCollection

  promotionBarTitle(locale: String): String

  promotionForRecommendation(locale: String, preview: Boolean, where: PromotionFilter): Promotion

  recommendationWithText(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  seoContent(locale: String, preview: Boolean, where: SeoContentFilter): SeoContent

  serviceBarCollection(limit: Int = 100, locale: String, order: [HomepageSheegoServiceBarCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSheegoServiceBarCollection

  simpleRecommendation(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  sys: Sys!

  teaserPromotion(locale: String, preview: Boolean, where: PromotionFilter): Promotion

  teaserPromotion2(locale: String, preview: Boolean, where: PromotionFilter): Promotion

  trigger(locale: String, preview: Boolean, where: TopLevelTriggerFilter): TopLevelTrigger
}

type HomepageSheegoCollection {
  items: [HomepageSheego]!

  limit: Int!

  skip: Int!

  total: Int!
}

type HomepageSheegoCompactPromotionBarCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSheegoCompactPromotionBarCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

input HomepageSheegoFilter {
  AND: [HomepageSheegoFilter]

  OR: [HomepageSheegoFilter]

  assortmentRecommendation: cfRecommendationGkAirNestedFilter

  assortmentRecommendation_exists: Boolean

  compactPromotionBar: cfPromotionNestedFilter

  compactPromotionBarCollection_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  hero: cfPromotionNestedFilter

  hero_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  optionalPromotionBar: cfPromotionNestedFilter

  optionalPromotionBarCollection_exists: Boolean

  optionalPromotionBarTitle: String

  optionalPromotionBarTitle_contains: String

  optionalPromotionBarTitle_exists: Boolean

  optionalPromotionBarTitle_in: [String]

  optionalPromotionBarTitle_not: String

  optionalPromotionBarTitle_not_contains: String

  optionalPromotionBarTitle_not_in: [String]

  promotionBar: cfPromotionNestedFilter

  promotionBar2: cfPromotionNestedFilter

  promotionBar2Collection_exists: Boolean

  promotionBar2Title: String

  promotionBar2Title_contains: String

  promotionBar2Title_exists: Boolean

  promotionBar2Title_in: [String]

  promotionBar2Title_not: String

  promotionBar2Title_not_contains: String

  promotionBar2Title_not_in: [String]

  promotionBar3: cfPromotionNestedFilter

  promotionBar3Collection_exists: Boolean

  promotionBar3Title: String

  promotionBar3Title_contains: String

  promotionBar3Title_exists: Boolean

  promotionBar3Title_in: [String]

  promotionBar3Title_not: String

  promotionBar3Title_not_contains: String

  promotionBar3Title_not_in: [String]

  promotionBarCollection_exists: Boolean

  promotionBarTitle: String

  promotionBarTitle_contains: String

  promotionBarTitle_exists: Boolean

  promotionBarTitle_in: [String]

  promotionBarTitle_not: String

  promotionBarTitle_not_contains: String

  promotionBarTitle_not_in: [String]

  promotionForRecommendation: cfPromotionNestedFilter

  promotionForRecommendation_exists: Boolean

  recommendationWithText: cfRecommendationGkAirNestedFilter

  recommendationWithText_exists: Boolean

  seoContent: cfSeoContentNestedFilter

  seoContent_exists: Boolean

  serviceBar: cfPromotionNestedFilter

  serviceBarCollection_exists: Boolean

  simpleRecommendation: cfRecommendationGkAirNestedFilter

  simpleRecommendation_exists: Boolean

  sys: SysFilter

  teaserPromotion: cfPromotionNestedFilter

  teaserPromotion2: cfPromotionNestedFilter

  teaserPromotion2_exists: Boolean

  teaserPromotion_exists: Boolean

  trigger: cfTopLevelTriggerNestedFilter

  trigger_exists: Boolean
}

type HomepageSheegoLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

type HomepageSheegoOptionalPromotionBarCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSheegoOptionalPromotionBarCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

enum HomepageSheegoOrder {
  internalTitle_ASC

  internalTitle_DESC

  optionalPromotionBarTitle_ASC

  optionalPromotionBarTitle_DESC

  promotionBar2Title_ASC

  promotionBar2Title_DESC

  promotionBar3Title_ASC

  promotionBar3Title_DESC

  promotionBarTitle_ASC

  promotionBarTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

type HomepageSheegoPromotionBar2Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSheegoPromotionBar2CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSheegoPromotionBar3Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSheegoPromotionBar3CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSheegoPromotionBarCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSheegoPromotionBarCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSheegoServiceBarCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSheegoServiceBarCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/homepageSiehAn)
"""
type HomepageSiehAn implements Entry & _Node {
  _id: ID!

  assortmentRecommendation(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  bannerV2(locale: String, preview: Boolean, where: BannerFilter): Banner

  card21Collection(limit: Int = 100, locale: String, order: [HomepageSiehAnCard21CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSiehAnCard21Collection

  card22Collection(limit: Int = 100, locale: String, order: [HomepageSiehAnCard22CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSiehAnCard22Collection

  card23Collection(limit: Int = 100, locale: String, order: [HomepageSiehAnCard23CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSiehAnCard23Collection

  card24Collection(limit: Int = 100, locale: String, order: [HomepageSiehAnCard24CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSiehAnCard24Collection

  card31Collection(limit: Int = 100, locale: String, order: [HomepageSiehAnCard31CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSiehAnCard31Collection

  card32Collection(limit: Int = 100, locale: String, order: [HomepageSiehAnCard32CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSiehAnCard32Collection

  card33Collection(limit: Int = 100, locale: String, order: [HomepageSiehAnCard33CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSiehAnCard33Collection

  card51Collection(limit: Int = 100, locale: String, order: [HomepageSiehAnCard51CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSiehAnCard51Collection

  card52Collection(limit: Int = 100, locale: String, order: [HomepageSiehAnCard52CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSiehAnCard52Collection

  card53Collection(limit: Int = 100, locale: String, order: [HomepageSiehAnCard53CollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSiehAnCard53Collection

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): HomepageSiehAnLinkingCollections

  recoCollection(limit: Int = 100, locale: String, order: [HomepageSiehAnRecoCollectionOrder], preview: Boolean, skip: Int = 0, where: RecommendationGkAirFilter): HomepageSiehAnRecoCollection

  seoContent(locale: String, preview: Boolean, where: SeoContentFilter): SeoContent

  sliderCollection(limit: Int = 100, locale: String, order: [HomepageSiehAnSliderCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSiehAnSliderCollection

  sys: Sys!

  trigger(locale: String, preview: Boolean, where: TopLevelTriggerFilter): TopLevelTrigger

  trustmarksCollection(limit: Int = 100, locale: String, order: [HomepageSiehAnTrustmarksCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSiehAnTrustmarksCollection
}

type HomepageSiehAnCard21Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnCard21CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSiehAnCard22Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnCard22CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSiehAnCard23Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnCard23CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSiehAnCard24Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnCard24CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSiehAnCard31Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnCard31CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSiehAnCard32Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnCard32CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSiehAnCard33Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnCard33CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSiehAnCard51Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnCard51CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSiehAnCard52Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnCard52CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSiehAnCard53Collection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnCard53CollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSiehAnCollection {
  items: [HomepageSiehAn]!

  limit: Int!

  skip: Int!

  total: Int!
}

input HomepageSiehAnFilter {
  AND: [HomepageSiehAnFilter]

  OR: [HomepageSiehAnFilter]

  assortmentRecommendation: cfRecommendationGkAirNestedFilter

  assortmentRecommendation_exists: Boolean

  bannerV2: cfBannerNestedFilter

  bannerV2_exists: Boolean

  card21: cfPromotionNestedFilter

  card21Collection_exists: Boolean

  card22: cfPromotionNestedFilter

  card22Collection_exists: Boolean

  card23: cfPromotionNestedFilter

  card23Collection_exists: Boolean

  card24: cfPromotionNestedFilter

  card24Collection_exists: Boolean

  card31: cfPromotionNestedFilter

  card31Collection_exists: Boolean

  card32: cfPromotionNestedFilter

  card32Collection_exists: Boolean

  card33: cfPromotionNestedFilter

  card33Collection_exists: Boolean

  card51: cfPromotionNestedFilter

  card51Collection_exists: Boolean

  card52: cfPromotionNestedFilter

  card52Collection_exists: Boolean

  card53: cfPromotionNestedFilter

  card53Collection_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  reco: cfRecommendationGkAirNestedFilter

  recoCollection_exists: Boolean

  seoContent: cfSeoContentNestedFilter

  seoContent_exists: Boolean

  slider: cfPromotionNestedFilter

  sliderCollection_exists: Boolean

  sys: SysFilter

  trigger: cfTopLevelTriggerNestedFilter

  trigger_exists: Boolean

  trustmarks: cfPromotionNestedFilter

  trustmarksCollection_exists: Boolean
}

type HomepageSiehAnLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum HomepageSiehAnOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

type HomepageSiehAnRecoCollection {
  items: [RecommendationGkAir]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnRecoCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  outputId_ASC

  outputId_DESC

  recommendationType_ASC

  recommendationType_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

type HomepageSiehAnSliderCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnSliderCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSiehAnTrustmarksCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnTrustmarksCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/homepageSiehAnV2)
"""
type HomepageSiehAnV2 implements Entry & _Node {
  _id: ID!

  assortmentPromotionsCollection(limit: Int = 100, locale: String, order: [HomepageSiehAnV2AssortmentPromotionsCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSiehAnV2AssortmentPromotionsCollection

  assortmentPromotionsGkAir(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  bottomRecommendation(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  brandFormulaPart1(locale: String): String

  brandFormulaPart2(locale: String): String

  brandFormulaResult(locale: String): String

  contentfulMetadata: ContentfulMetadata!

  heroDoodles(locale: String): String

  heroPromotion(locale: String, preview: Boolean, where: PromotionFilter): Promotion

  heroRecommendation(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  iconPromotionsCollection(limit: Int = 100, locale: String, order: [HomepageSiehAnV2IconPromotionsCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSiehAnV2IconPromotionsCollection

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): HomepageSiehAnV2LinkingCollections

  prioDoodles1(locale: String): String

  prioDoodles2(locale: String): String

  prioPromotion1(locale: String, preview: Boolean, where: PromotionFilter): Promotion

  prioPromotion2(locale: String, preview: Boolean, where: PromotionFilter): Promotion

  prioRecommendation1(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  prioRecommendation2(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  promotionBarCollection(limit: Int = 100, locale: String, order: [HomepageSiehAnV2PromotionBarCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSiehAnV2PromotionBarCollection

  seoContent(locale: String, preview: Boolean, where: SeoContentFilter): SeoContent

  serviceBarCollection(limit: Int = 100, locale: String, order: [HomepageSiehAnV2ServiceBarCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSiehAnV2ServiceBarCollection

  sys: Sys!

  teaserPromotion(locale: String, preview: Boolean, where: PromotionFilter): Promotion

  titleAssortmentPromotions(locale: String): String

  titleCards(locale: String): String

  titleIconPromotions(locale: String): String

  trigger(locale: String, preview: Boolean, where: TopLevelTriggerFilter): TopLevelTrigger

  uspBarCollection(limit: Int = 100, locale: String, order: [HomepageSiehAnV2UspBarCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageSiehAnV2UspBarCollection
}

type HomepageSiehAnV2AssortmentPromotionsCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnV2AssortmentPromotionsCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSiehAnV2Collection {
  items: [HomepageSiehAnV2]!

  limit: Int!

  skip: Int!

  total: Int!
}

input HomepageSiehAnV2Filter {
  AND: [HomepageSiehAnV2Filter]

  OR: [HomepageSiehAnV2Filter]

  assortmentPromotions: cfPromotionNestedFilter

  assortmentPromotionsCollection_exists: Boolean

  assortmentPromotionsGkAir: cfRecommendationGkAirNestedFilter

  assortmentPromotionsGkAir_exists: Boolean

  bottomRecommendation: cfRecommendationGkAirNestedFilter

  bottomRecommendation_exists: Boolean

  brandFormulaPart1: String

  brandFormulaPart1_contains: String

  brandFormulaPart1_exists: Boolean

  brandFormulaPart1_in: [String]

  brandFormulaPart1_not: String

  brandFormulaPart1_not_contains: String

  brandFormulaPart1_not_in: [String]

  brandFormulaPart2: String

  brandFormulaPart2_contains: String

  brandFormulaPart2_exists: Boolean

  brandFormulaPart2_in: [String]

  brandFormulaPart2_not: String

  brandFormulaPart2_not_contains: String

  brandFormulaPart2_not_in: [String]

  brandFormulaResult: String

  brandFormulaResult_contains: String

  brandFormulaResult_exists: Boolean

  brandFormulaResult_in: [String]

  brandFormulaResult_not: String

  brandFormulaResult_not_contains: String

  brandFormulaResult_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  heroDoodles: String

  heroDoodles_contains: String

  heroDoodles_exists: Boolean

  heroDoodles_in: [String]

  heroDoodles_not: String

  heroDoodles_not_contains: String

  heroDoodles_not_in: [String]

  heroPromotion: cfPromotionNestedFilter

  heroPromotion_exists: Boolean

  heroRecommendation: cfRecommendationGkAirNestedFilter

  heroRecommendation_exists: Boolean

  iconPromotions: cfPromotionNestedFilter

  iconPromotionsCollection_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  prioDoodles1: String

  prioDoodles1_contains: String

  prioDoodles1_exists: Boolean

  prioDoodles1_in: [String]

  prioDoodles1_not: String

  prioDoodles1_not_contains: String

  prioDoodles1_not_in: [String]

  prioDoodles2: String

  prioDoodles2_contains: String

  prioDoodles2_exists: Boolean

  prioDoodles2_in: [String]

  prioDoodles2_not: String

  prioDoodles2_not_contains: String

  prioDoodles2_not_in: [String]

  prioPromotion1: cfPromotionNestedFilter

  prioPromotion1_exists: Boolean

  prioPromotion2: cfPromotionNestedFilter

  prioPromotion2_exists: Boolean

  prioRecommendation1: cfRecommendationGkAirNestedFilter

  prioRecommendation1_exists: Boolean

  prioRecommendation2: cfRecommendationGkAirNestedFilter

  prioRecommendation2_exists: Boolean

  promotionBar: cfPromotionNestedFilter

  promotionBarCollection_exists: Boolean

  seoContent: cfSeoContentNestedFilter

  seoContent_exists: Boolean

  serviceBar: cfPromotionNestedFilter

  serviceBarCollection_exists: Boolean

  sys: SysFilter

  teaserPromotion: cfPromotionNestedFilter

  teaserPromotion_exists: Boolean

  titleAssortmentPromotions: String

  titleAssortmentPromotions_contains: String

  titleAssortmentPromotions_exists: Boolean

  titleAssortmentPromotions_in: [String]

  titleAssortmentPromotions_not: String

  titleAssortmentPromotions_not_contains: String

  titleAssortmentPromotions_not_in: [String]

  titleCards: String

  titleCards_contains: String

  titleCards_exists: Boolean

  titleCards_in: [String]

  titleCards_not: String

  titleCards_not_contains: String

  titleCards_not_in: [String]

  titleIconPromotions: String

  titleIconPromotions_contains: String

  titleIconPromotions_exists: Boolean

  titleIconPromotions_in: [String]

  titleIconPromotions_not: String

  titleIconPromotions_not_contains: String

  titleIconPromotions_not_in: [String]

  trigger: cfTopLevelTriggerNestedFilter

  trigger_exists: Boolean

  uspBar: cfPromotionNestedFilter

  uspBarCollection_exists: Boolean
}

type HomepageSiehAnV2IconPromotionsCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnV2IconPromotionsCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSiehAnV2LinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum HomepageSiehAnV2Order {
  brandFormulaPart1_ASC

  brandFormulaPart1_DESC

  brandFormulaPart2_ASC

  brandFormulaPart2_DESC

  brandFormulaResult_ASC

  brandFormulaResult_DESC

  heroDoodles_ASC

  heroDoodles_DESC

  internalTitle_ASC

  internalTitle_DESC

  prioDoodles1_ASC

  prioDoodles1_DESC

  prioDoodles2_ASC

  prioDoodles2_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  titleAssortmentPromotions_ASC

  titleAssortmentPromotions_DESC

  titleCards_ASC

  titleCards_DESC

  titleIconPromotions_ASC

  titleIconPromotions_DESC
}

type HomepageSiehAnV2PromotionBarCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnV2PromotionBarCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSiehAnV2ServiceBarCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnV2ServiceBarCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageSiehAnV2UspBarCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageSiehAnV2UspBarCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/homepageWittV2)
"""
type HomepageWittV2 implements Entry & _Node {
  _id: ID!

  assortmentRecommendation(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  cardForRecommendation(locale: String, preview: Boolean, where: PromotionFilter): Promotion

  cardForRecommendation2(locale: String, preview: Boolean, where: PromotionFilter): Promotion

  contentfulMetadata: ContentfulMetadata!

  hero(locale: String, preview: Boolean, where: PromotionFilter): Promotion

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): HomepageWittV2LinkingCollections

  promotionBarCollection(limit: Int = 100, locale: String, order: [HomepageWittV2PromotionBarCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageWittV2PromotionBarCollection

  promotionForRecommendation(locale: String, preview: Boolean, where: PromotionFilter): Promotion

  recommendationWithCard(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  recommendationWithCard2(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  recommendationWithText(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  seoContent(locale: String, preview: Boolean, where: SeoContentFilter): SeoContent

  serviceBarCollection(limit: Int = 100, locale: String, order: [HomepageWittV2ServiceBarCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageWittV2ServiceBarCollection

  simpleRecommendation(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  sys: Sys!

  teaser(locale: String, preview: Boolean, where: PromotionFilter): Promotion

  trigger(locale: String, preview: Boolean, where: TopLevelTriggerFilter): TopLevelTrigger

  trustmarksCollection(limit: Int = 100, locale: String, order: [HomepageWittV2TrustmarksCollectionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): HomepageWittV2TrustmarksCollection
}

type HomepageWittV2Collection {
  items: [HomepageWittV2]!

  limit: Int!

  skip: Int!

  total: Int!
}

input HomepageWittV2Filter {
  AND: [HomepageWittV2Filter]

  OR: [HomepageWittV2Filter]

  assortmentRecommendation: cfRecommendationGkAirNestedFilter

  assortmentRecommendation_exists: Boolean

  cardForRecommendation: cfPromotionNestedFilter

  cardForRecommendation2: cfPromotionNestedFilter

  cardForRecommendation2_exists: Boolean

  cardForRecommendation_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  hero: cfPromotionNestedFilter

  hero_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  promotionBar: cfPromotionNestedFilter

  promotionBarCollection_exists: Boolean

  promotionForRecommendation: cfPromotionNestedFilter

  promotionForRecommendation_exists: Boolean

  recommendationWithCard: cfRecommendationGkAirNestedFilter

  recommendationWithCard2: cfRecommendationGkAirNestedFilter

  recommendationWithCard2_exists: Boolean

  recommendationWithCard_exists: Boolean

  recommendationWithText: cfRecommendationGkAirNestedFilter

  recommendationWithText_exists: Boolean

  seoContent: cfSeoContentNestedFilter

  seoContent_exists: Boolean

  serviceBar: cfPromotionNestedFilter

  serviceBarCollection_exists: Boolean

  simpleRecommendation: cfRecommendationGkAirNestedFilter

  simpleRecommendation_exists: Boolean

  sys: SysFilter

  teaser: cfPromotionNestedFilter

  teaser_exists: Boolean

  trigger: cfTopLevelTriggerNestedFilter

  trigger_exists: Boolean

  trustmarks: cfPromotionNestedFilter

  trustmarksCollection_exists: Boolean
}

type HomepageWittV2LinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum HomepageWittV2Order {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

type HomepageWittV2PromotionBarCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageWittV2PromotionBarCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageWittV2ServiceBarCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageWittV2ServiceBarCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type HomepageWittV2TrustmarksCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum HomepageWittV2TrustmarksCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/iFrame)
"""
type IFrame implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): IFrameLinkingCollections

  sys: Sys!

  title(locale: String): String

  trackingName(locale: String): String

  url(locale: String): String
}

type IFrameCollection {
  items: [IFrame]!

  limit: Int!

  skip: Int!

  total: Int!
}

input IFrameFilter {
  AND: [IFrameFilter]

  OR: [IFrameFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]

  trackingName: String

  trackingName_contains: String

  trackingName_exists: Boolean

  trackingName_in: [String]

  trackingName_not: String

  trackingName_not_contains: String

  trackingName_not_in: [String]

  url: String

  url_contains: String

  url_exists: Boolean

  url_in: [String]

  url_not: String

  url_not_contains: String

  url_not_in: [String]
}

type IFrameLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum IFrameOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC

  url_ASC

  url_DESC
}

enum ImageFormat {
  """
  AVIF image format.
  """
  AVIF

  """
  JPG image format.
  """
  JPG

  """
  Progressive JPG format stores multiple passes of an image in progressively higher detail.
          When a progressive image is loading, the viewer will first see a lower quality pixelated version which
          will gradually improve in detail, until the image is fully downloaded. This is to display an image as
          early as possible to make the layout look as designed.
  """
  JPG_PROGRESSIVE

  """
  PNG image format
  """
  PNG

  """
  8-bit PNG images support up to 256 colors and weigh less than the standard 24-bit PNG equivalent.
          The 8-bit PNG format is mostly used for simple images, such as icons or logos.
  """
  PNG8

  """
  WebP image format.
  """
  WEBP
}

enum ImageResizeFocus {
  """
  Focus the resizing on the bottom.
  """
  BOTTOM

  """
  Focus the resizing on the bottom left.
  """
  BOTTOM_LEFT

  """
  Focus the resizing on the bottom right.
  """
  BOTTOM_RIGHT

  """
  Focus the resizing on the center.
  """
  CENTER

  """
  Focus the resizing on the largest face.
  """
  FACE

  """
  Focus the resizing on the area containing all the faces.
  """
  FACES

  """
  Focus the resizing on the left.
  """
  LEFT

  """
  Focus the resizing on the right.
  """
  RIGHT

  """
  Focus the resizing on the top.
  """
  TOP

  """
  Focus the resizing on the top left.
  """
  TOP_LEFT

  """
  Focus the resizing on the top right.
  """
  TOP_RIGHT
}

enum ImageResizeStrategy {
  """
  Crops a part of the original image to fit into the specified dimensions.
  """
  CROP

  """
  Resizes the image to the specified dimensions, cropping the image if needed.
  """
  FILL

  """
  Resizes the image to fit into the specified dimensions.
  """
  FIT

  """
  Resizes the image to the specified dimensions, padding the image if needed.
          Uses desired background color as padding color.
  """
  PAD

  """
  Resizes the image to the specified dimensions, changing the original aspect ratio if needed.
  """
  SCALE

  """
  Creates a thumbnail from the image.
  """
  THUMB
}

input ImageTransformOptions {
  """
  Desired background color, used with corner radius or `PAD` resize strategy.
          Defaults to transparent (for `PNG`, `PNG8` and `WEBP`) or white (for `JPG` and `JPG_PROGRESSIVE`).
  """
  backgroundColor: HexColor

  """
  Desired corner radius in pixels.
          Results in an image with rounded corners (pass `-1` for a full circle/ellipse).
          Defaults to `0`. Uses desired background color as padding color,
          unless the format is `JPG` or `JPG_PROGRESSIVE` and resize strategy is `PAD`, then defaults to white.
  """
  cornerRadius: Int

  """
  Desired image format. Defaults to the original image format.
  """
  format: ImageFormat

  """
  Desired height in pixels. Defaults to the original image height.
  """
  height: Dimension

  """
  Desired quality of the image in percents.
          Used for `PNG8`, `JPG`, `JPG_PROGRESSIVE` and `WEBP` formats.
  """
  quality: Quality

  """
  Desired resize focus area. Defaults to `CENTER`.
  """
  resizeFocus: ImageResizeFocus

  """
  Desired resize strategy. Defaults to `FIT`.
  """
  resizeStrategy: ImageResizeStrategy

  """
  Desired width in pixels. Defaults to the original image width.
  """
  width: Dimension
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/inlineAsset)
"""
type InlineAsset implements Entry & _Node {
  _id: ID!

  alignment(locale: String): String

  contentfulMetadata: ContentfulMetadata!

  image(locale: String, preview: Boolean): Asset

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): InlineAssetLinkingCollections

  sys: Sys!
}

type InlineAssetCollection {
  items: [InlineAsset]!

  limit: Int!

  skip: Int!

  total: Int!
}

input InlineAssetFilter {
  AND: [InlineAssetFilter]

  OR: [InlineAssetFilter]

  alignment: String

  alignment_contains: String

  alignment_exists: Boolean

  alignment_in: [String]

  alignment_not: String

  alignment_not_contains: String

  alignment_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  image_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter
}

type InlineAssetLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum InlineAssetOrder {
  alignment_ASC

  alignment_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/inspirationItem)
"""
type InspirationItem implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  headline(locale: String): String

  image1Cloudinary(locale: String): JSON

  image2Cloudinary(locale: String): JSON

  image3Cloudinary(locale: String): JSON

  imagesCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): AssetCollection

  internalTitle(locale: String): String

  layoutColorPicker(locale: String): JSON

  link(locale: String, preview: Boolean, where: InspirationItemLinkFilter): InspirationItemLink

  linkedFrom(allowedLocales: [String]): InspirationItemLinkingCollections

  recommendationGkAir(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  sys: Sys!

  text(locale: String): InspirationItemText
}

type InspirationItemCollection {
  items: [InspirationItem]!

  limit: Int!

  skip: Int!

  total: Int!
}

input InspirationItemFilter {
  AND: [InspirationItemFilter]

  OR: [InspirationItemFilter]

  contentfulMetadata: ContentfulMetadataFilter

  headline: String

  headline_contains: String

  headline_exists: Boolean

  headline_in: [String]

  headline_not: String

  headline_not_contains: String

  headline_not_in: [String]

  image1Cloudinary_exists: Boolean

  image2Cloudinary_exists: Boolean

  image3Cloudinary_exists: Boolean

  imagesCollection_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  layoutColorPicker_exists: Boolean

  link: cflinkMultiTypeNestedFilter

  link_exists: Boolean

  recommendationGkAir: cfRecommendationGkAirNestedFilter

  recommendationGkAir_exists: Boolean

  sys: SysFilter

  text_contains: String

  text_exists: Boolean

  text_not_contains: String
}

union InspirationItemLink = ExternalLink|InternalLink

input InspirationItemLinkFilter {
  AND: [InspirationItemLinkFilter]

  OR: [InspirationItemLinkFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

type InspirationItemLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  inspirationPageCollection(limit: Int = 100, locale: String, order: [InspirationItemLinkingCollectionsInspirationPageCollectionOrder], preview: Boolean, skip: Int = 0): InspirationPageCollection
}

enum InspirationItemLinkingCollectionsInspirationPageCollectionOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  overviewHeadline_ASC

  overviewHeadline_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  transitionalHeadline_ASC

  transitionalHeadline_DESC
}

enum InspirationItemOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

type InspirationItemText {
  json: JSON!

  links: InspirationItemTextLinks!
}

type InspirationItemTextAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type InspirationItemTextEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type InspirationItemTextLinks {
  assets: InspirationItemTextAssets!

  entries: InspirationItemTextEntries!

  resources: InspirationItemTextResources!
}

type InspirationItemTextResources {
  block: [InspirationItemTextResourcesBlock!]!

  hyperlink: [InspirationItemTextResourcesHyperlink!]!

  inline: [InspirationItemTextResourcesInline!]!
}

type InspirationItemTextResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type InspirationItemTextResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type InspirationItemTextResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/inspirationPage)
"""
type InspirationPage implements Entry & _Node {
  _id: ID!

  anchorLinksCollection(limit: Int = 100, locale: String, order: [InspirationPageAnchorLinksCollectionOrder], preview: Boolean, skip: Int = 0, where: AnchorLinkFilter): InspirationPageAnchorLinksCollection

  contentfulMetadata: ContentfulMetadata!

  headline(locale: String): String

  image(locale: String, preview: Boolean): Asset

  imageCloudinary(locale: String): JSON

  inspirationItemCollection(limit: Int = 100, locale: String, order: [InspirationPageInspirationItemCollectionOrder], preview: Boolean, skip: Int = 0, where: InspirationItemFilter): InspirationPageInspirationItemCollection

  internalTitle(locale: String): String

  introductionText(locale: String): InspirationPageIntroductionText

  linkedFrom(allowedLocales: [String]): InspirationPageLinkingCollections

  overviewHeadline(locale: String): String

  seoContent(locale: String, preview: Boolean, where: SeoContentFilter): SeoContent

  slug(locale: String): String

  sys: Sys!

  transitionalHeadline(locale: String): String

  transitionalText(locale: String): InspirationPageTransitionalText
}

type InspirationPageAnchorLinksCollection {
  items: [AnchorLink]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum InspirationPageAnchorLinksCollectionOrder {
  anchor_ASC

  anchor_DESC

  label_ASC

  label_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

type InspirationPageCollection {
  items: [InspirationPage]!

  limit: Int!

  skip: Int!

  total: Int!
}

input InspirationPageFilter {
  AND: [InspirationPageFilter]

  OR: [InspirationPageFilter]

  anchorLinks: cfAnchorLinkNestedFilter

  anchorLinksCollection_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  headline: String

  headline_contains: String

  headline_exists: Boolean

  headline_in: [String]

  headline_not: String

  headline_not_contains: String

  headline_not_in: [String]

  imageCloudinary_exists: Boolean

  image_exists: Boolean

  inspirationItem: cfInspirationItemNestedFilter

  inspirationItemCollection_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  introductionText_contains: String

  introductionText_exists: Boolean

  introductionText_not_contains: String

  overviewHeadline: String

  overviewHeadline_contains: String

  overviewHeadline_exists: Boolean

  overviewHeadline_in: [String]

  overviewHeadline_not: String

  overviewHeadline_not_contains: String

  overviewHeadline_not_in: [String]

  seoContent: cfSeoContentNestedFilter

  seoContent_exists: Boolean

  slug: String

  slug_contains: String

  slug_exists: Boolean

  slug_in: [String]

  slug_not: String

  slug_not_contains: String

  slug_not_in: [String]

  sys: SysFilter

  transitionalHeadline: String

  transitionalHeadline_contains: String

  transitionalHeadline_exists: Boolean

  transitionalHeadline_in: [String]

  transitionalHeadline_not: String

  transitionalHeadline_not_contains: String

  transitionalHeadline_not_in: [String]

  transitionalText_contains: String

  transitionalText_exists: Boolean

  transitionalText_not_contains: String
}

type InspirationPageInspirationItemCollection {
  items: [InspirationItem]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum InspirationPageInspirationItemCollectionOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

type InspirationPageIntroductionText {
  json: JSON!

  links: InspirationPageIntroductionTextLinks!
}

type InspirationPageIntroductionTextAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type InspirationPageIntroductionTextEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type InspirationPageIntroductionTextLinks {
  assets: InspirationPageIntroductionTextAssets!

  entries: InspirationPageIntroductionTextEntries!

  resources: InspirationPageIntroductionTextResources!
}

type InspirationPageIntroductionTextResources {
  block: [InspirationPageIntroductionTextResourcesBlock!]!

  hyperlink: [InspirationPageIntroductionTextResourcesHyperlink!]!

  inline: [InspirationPageIntroductionTextResourcesInline!]!
}

type InspirationPageIntroductionTextResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type InspirationPageIntroductionTextResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type InspirationPageIntroductionTextResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

type InspirationPageLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  internalLinkCollection(limit: Int = 100, locale: String, order: [InspirationPageLinkingCollectionsInternalLinkCollectionOrder], preview: Boolean, skip: Int = 0): InternalLinkCollection
}

enum InspirationPageLinkingCollectionsInternalLinkCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  label_ASC

  label_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum InspirationPageOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  overviewHeadline_ASC

  overviewHeadline_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  transitionalHeadline_ASC

  transitionalHeadline_DESC
}

type InspirationPageTransitionalText {
  json: JSON!

  links: InspirationPageTransitionalTextLinks!
}

type InspirationPageTransitionalTextAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type InspirationPageTransitionalTextEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type InspirationPageTransitionalTextLinks {
  assets: InspirationPageTransitionalTextAssets!

  entries: InspirationPageTransitionalTextEntries!

  resources: InspirationPageTransitionalTextResources!
}

type InspirationPageTransitionalTextResources {
  block: [InspirationPageTransitionalTextResourcesBlock!]!

  hyperlink: [InspirationPageTransitionalTextResourcesHyperlink!]!

  inline: [InspirationPageTransitionalTextResourcesInline!]!
}

type InspirationPageTransitionalTextResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type InspirationPageTransitionalTextResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type InspirationPageTransitionalTextResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/internalLink)
"""
type InternalLink implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  internalTarget(locale: String, preview: Boolean): InternalLinkInternalTarget

  internalTitle(locale: String): String

  label(locale: String): String

  linkedFrom(allowedLocales: [String]): InternalLinkLinkingCollections

  sys: Sys!
}

type InternalLinkCollection {
  items: [InternalLink]!

  limit: Int!

  skip: Int!

  total: Int!
}

input InternalLinkFilter {
  AND: [InternalLinkFilter]

  OR: [InternalLinkFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTarget_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

union InternalLinkInternalTarget = ContentPage|InspirationPage|OutfitPage

type InternalLinkLinkingCollections {
  bannerCollection(limit: Int = 100, locale: String, order: [InternalLinkLinkingCollectionsBannerCollectionOrder], preview: Boolean, skip: Int = 0): BannerCollection

  boxCollection(limit: Int = 100, locale: String, order: [InternalLinkLinkingCollectionsBoxCollectionOrder], preview: Boolean, skip: Int = 0): BoxCollection

  buttonCollection(limit: Int = 100, locale: String, order: [InternalLinkLinkingCollectionsButtonCollectionOrder], preview: Boolean, skip: Int = 0): ButtonCollection

  contactBoxCollection(limit: Int = 100, locale: String, order: [InternalLinkLinkingCollectionsContactBoxCollectionOrder], preview: Boolean, skip: Int = 0): ContactBoxCollection

  contentPageCollection(limit: Int = 100, locale: String, order: [InternalLinkLinkingCollectionsContentPageCollectionOrder], preview: Boolean, skip: Int = 0): ContentPageCollection

  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  errorPageCollection(limit: Int = 100, locale: String, order: [InternalLinkLinkingCollectionsErrorPageCollectionOrder], preview: Boolean, skip: Int = 0): ErrorPageCollection

  footerColumnCollection(limit: Int = 100, locale: String, order: [InternalLinkLinkingCollectionsFooterColumnCollectionOrder], preview: Boolean, skip: Int = 0): FooterColumnCollection

  inspirationItemCollection(limit: Int = 100, locale: String, order: [InternalLinkLinkingCollectionsInspirationItemCollectionOrder], preview: Boolean, skip: Int = 0): InspirationItemCollection

  modalCollection(limit: Int = 100, locale: String, order: [InternalLinkLinkingCollectionsModalCollectionOrder], preview: Boolean, skip: Int = 0): ModalCollection

  outfitPageCollection(limit: Int = 100, locale: String, order: [InternalLinkLinkingCollectionsOutfitPageCollectionOrder], preview: Boolean, skip: Int = 0): OutfitPageCollection

  pageFooterCollection(limit: Int = 100, locale: String, order: [InternalLinkLinkingCollectionsPageFooterCollectionOrder], preview: Boolean, skip: Int = 0): PageFooterCollection

  promotionCollection(limit: Int = 100, locale: String, order: [InternalLinkLinkingCollectionsPromotionCollectionOrder], preview: Boolean, skip: Int = 0): PromotionCollection

  seoAdviceCollection(limit: Int = 100, locale: String, order: [InternalLinkLinkingCollectionsSeoAdviceCollectionOrder], preview: Boolean, skip: Int = 0): SeoAdviceCollection
}

enum InternalLinkLinkingCollectionsBannerCollectionOrder {
  countdownStart_ASC

  countdownStart_DESC

  countdown_ASC

  countdown_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  trackingName_ASC

  trackingName_DESC
}

enum InternalLinkLinkingCollectionsBoxCollectionOrder {
  boxText_ASC

  boxText_DESC

  boxTitle_ASC

  boxTitle_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  trackingName_ASC

  trackingName_DESC
}

enum InternalLinkLinkingCollectionsButtonCollectionOrder {
  color_ASC

  color_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  trackingName_ASC

  trackingName_DESC
}

enum InternalLinkLinkingCollectionsContactBoxCollectionOrder {
  icon_ASC

  icon_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

enum InternalLinkLinkingCollectionsContentPageCollectionOrder {
  horizontalAlignment_ASC

  horizontalAlignment_DESC

  name_ASC

  name_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum InternalLinkLinkingCollectionsErrorPageCollectionOrder {
  id_ASC

  id_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

enum InternalLinkLinkingCollectionsFooterColumnCollectionOrder {
  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

enum InternalLinkLinkingCollectionsInspirationItemCollectionOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum InternalLinkLinkingCollectionsModalCollectionOrder {
  headline_ASC

  headline_DESC

  id_ASC

  id_DESC

  linkText_ASC

  linkText_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum InternalLinkLinkingCollectionsOutfitPageCollectionOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  moreOutfitsHeadline_ASC

  moreOutfitsHeadline_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum InternalLinkLinkingCollectionsPageFooterCollectionOrder {
  id_ASC

  id_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum InternalLinkLinkingCollectionsPromotionCollectionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

enum InternalLinkLinkingCollectionsSeoAdviceCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

enum InternalLinkOrder {
  internalTitle_ASC

  internalTitle_DESC

  label_ASC

  label_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/layout)
"""
type Layout implements Entry & _Node {
  _id: ID!

  columnsCollection(limit: Int = 100, locale: String, order: [LayoutColumnsCollectionOrder], preview: Boolean, skip: Int = 0, where: LayoutColumnFilter): LayoutColumnsCollection

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): LayoutLinkingCollections

  ratio(locale: String): String

  sys: Sys!

  verticalAlignment(locale: String): String
}

type LayoutCollection {
  items: [Layout]!

  limit: Int!

  skip: Int!

  total: Int!
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/layoutColumn)
"""
type LayoutColumn implements Entry & _Node {
  _id: ID!

  content(locale: String): LayoutColumnContent

  contentfulMetadata: ContentfulMetadata!

  horizontalAlignment(locale: String): String

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): LayoutColumnLinkingCollections

  sys: Sys!
}

type LayoutColumnCollection {
  items: [LayoutColumn]!

  limit: Int!

  skip: Int!

  total: Int!
}

type LayoutColumnContent {
  json: JSON!

  links: LayoutColumnContentLinks!
}

type LayoutColumnContentAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type LayoutColumnContentEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type LayoutColumnContentLinks {
  assets: LayoutColumnContentAssets!

  entries: LayoutColumnContentEntries!

  resources: LayoutColumnContentResources!
}

type LayoutColumnContentResources {
  block: [LayoutColumnContentResourcesBlock!]!

  hyperlink: [LayoutColumnContentResourcesHyperlink!]!

  inline: [LayoutColumnContentResourcesInline!]!
}

type LayoutColumnContentResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type LayoutColumnContentResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type LayoutColumnContentResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

input LayoutColumnFilter {
  AND: [LayoutColumnFilter]

  OR: [LayoutColumnFilter]

  content_contains: String

  content_exists: Boolean

  content_not_contains: String

  contentfulMetadata: ContentfulMetadataFilter

  horizontalAlignment: String

  horizontalAlignment_contains: String

  horizontalAlignment_exists: Boolean

  horizontalAlignment_in: [String]

  horizontalAlignment_not: String

  horizontalAlignment_not_contains: String

  horizontalAlignment_not_in: [String]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter
}

type LayoutColumnLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  layoutCollection(limit: Int = 100, locale: String, order: [LayoutColumnLinkingCollectionsLayoutCollectionOrder], preview: Boolean, skip: Int = 0): LayoutCollection
}

enum LayoutColumnLinkingCollectionsLayoutCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  ratio_ASC

  ratio_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  verticalAlignment_ASC

  verticalAlignment_DESC
}

enum LayoutColumnOrder {
  horizontalAlignment_ASC

  horizontalAlignment_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

type LayoutColumnsCollection {
  items: [LayoutColumn]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum LayoutColumnsCollectionOrder {
  horizontalAlignment_ASC

  horizontalAlignment_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

input LayoutFilter {
  AND: [LayoutFilter]

  OR: [LayoutFilter]

  columns: cfLayoutColumnNestedFilter

  columnsCollection_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  ratio: String

  ratio_contains: String

  ratio_exists: Boolean

  ratio_in: [String]

  ratio_not: String

  ratio_not_contains: String

  ratio_not_in: [String]

  sys: SysFilter

  verticalAlignment: String

  verticalAlignment_contains: String

  verticalAlignment_exists: Boolean

  verticalAlignment_in: [String]

  verticalAlignment_not: String

  verticalAlignment_not_contains: String

  verticalAlignment_not_in: [String]
}

type LayoutLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum LayoutOrder {
  internalTitle_ASC

  internalTitle_DESC

  ratio_ASC

  ratio_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  verticalAlignment_ASC

  verticalAlignment_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/linkBox)
"""
type LinkBox implements Entry & _Node {
  _id: ID!

  categoryIDs(locale: String): String

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): LinkBoxLinkingCollections

  links(locale: String): JSON

  pagetype(locale: String): [String]

  sys: Sys!

  title(locale: String): String
}

type LinkBoxCollection {
  items: [LinkBox]!

  limit: Int!

  skip: Int!

  total: Int!
}

input LinkBoxFilter {
  AND: [LinkBoxFilter]

  OR: [LinkBoxFilter]

  categoryIDs: String

  categoryIDs_contains: String

  categoryIDs_exists: Boolean

  categoryIDs_in: [String]

  categoryIDs_not: String

  categoryIDs_not_contains: String

  categoryIDs_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  links_exists: Boolean

  pagetype_contains_all: [String]

  pagetype_contains_none: [String]

  pagetype_contains_some: [String]

  pagetype_exists: Boolean

  sys: SysFilter

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

type LinkBoxLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum LinkBoxOrder {
  categoryIDs_ASC

  categoryIDs_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/localeContent)
"""
type LocaleContent implements Entry & _Node {
  _id: ID!

  content(locale: String): LocaleContentContent

  contentfulMetadata: ContentfulMetadata!

  key(locale: String): String

  linkedFrom(allowedLocales: [String]): LocaleContentLinkingCollections

  sys: Sys!
}

type LocaleContentCollection {
  items: [LocaleContent]!

  limit: Int!

  skip: Int!

  total: Int!
}

type LocaleContentContent {
  json: JSON!

  links: LocaleContentContentLinks!
}

type LocaleContentContentAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type LocaleContentContentEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type LocaleContentContentLinks {
  assets: LocaleContentContentAssets!

  entries: LocaleContentContentEntries!

  resources: LocaleContentContentResources!
}

type LocaleContentContentResources {
  block: [LocaleContentContentResourcesBlock!]!

  hyperlink: [LocaleContentContentResourcesHyperlink!]!

  inline: [LocaleContentContentResourcesInline!]!
}

type LocaleContentContentResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type LocaleContentContentResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type LocaleContentContentResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

input LocaleContentFilter {
  AND: [LocaleContentFilter]

  OR: [LocaleContentFilter]

  content_contains: String

  content_exists: Boolean

  content_not_contains: String

  contentfulMetadata: ContentfulMetadataFilter

  key: String

  key_contains: String

  key_exists: Boolean

  key_in: [String]

  key_not: String

  key_not_contains: String

  key_not_in: [String]

  sys: SysFilter
}

type LocaleContentLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum LocaleContentOrder {
  key_ASC

  key_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/localeImage)
"""
type LocaleImage implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  image(locale: String, preview: Boolean): Asset

  imageCloudinary(locale: String): JSON

  key(locale: String): String

  linkedFrom(allowedLocales: [String]): LocaleImageLinkingCollections

  sys: Sys!
}

type LocaleImageCollection {
  items: [LocaleImage]!

  limit: Int!

  skip: Int!

  total: Int!
}

input LocaleImageFilter {
  AND: [LocaleImageFilter]

  OR: [LocaleImageFilter]

  contentfulMetadata: ContentfulMetadataFilter

  imageCloudinary_exists: Boolean

  image_exists: Boolean

  key: String

  key_contains: String

  key_exists: Boolean

  key_in: [String]

  key_not: String

  key_not_contains: String

  key_not_in: [String]

  sys: SysFilter
}

type LocaleImageLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum LocaleImageOrder {
  key_ASC

  key_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/localeText)
"""
type LocaleText implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  defaultMessage(locale: String): String

  description(locale: String): String

  key(locale: String): String

  linkedFrom(allowedLocales: [String]): LocaleTextLinkingCollections

  localeLists(locale: String): [String]

  sys: Sys!

  value(locale: String): String
}

type LocaleTextCollection {
  items: [LocaleText]!

  limit: Int!

  skip: Int!

  total: Int!
}

input LocaleTextFilter {
  AND: [LocaleTextFilter]

  OR: [LocaleTextFilter]

  contentfulMetadata: ContentfulMetadataFilter

  defaultMessage: String

  defaultMessage_contains: String

  defaultMessage_exists: Boolean

  defaultMessage_in: [String]

  defaultMessage_not: String

  defaultMessage_not_contains: String

  defaultMessage_not_in: [String]

  description: String

  description_contains: String

  description_exists: Boolean

  description_in: [String]

  description_not: String

  description_not_contains: String

  description_not_in: [String]

  key: String

  key_contains: String

  key_exists: Boolean

  key_in: [String]

  key_not: String

  key_not_contains: String

  key_not_in: [String]

  localeLists_contains_all: [String]

  localeLists_contains_none: [String]

  localeLists_contains_some: [String]

  localeLists_exists: Boolean

  sys: SysFilter

  value: String

  value_contains: String

  value_exists: Boolean

  value_in: [String]

  value_not: String

  value_not_contains: String

  value_not_in: [String]
}

type LocaleTextLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  orderStateCollection(limit: Int = 100, locale: String, order: [LocaleTextLinkingCollectionsOrderStateCollectionOrder], preview: Boolean, skip: Int = 0): OrderStateCollection
}

enum LocaleTextLinkingCollectionsOrderStateCollectionOrder {
  color_ASC

  color_DESC

  key_ASC

  key_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum LocaleTextOrder {
  key_ASC

  key_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/modal)
"""
type Modal implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  coupon(locale: String, preview: Boolean, where: CouponFilter): Coupon

  headline(locale: String): String

  id(locale: String): String

  linkText(locale: String): String

  linkedFrom(allowedLocales: [String]): ModalLinkingCollections

  primaryButtonLink(locale: String, preview: Boolean, where: ModalPrimaryButtonLinkFilter): ModalPrimaryButtonLink

  richtext(locale: String): ModalRichtext

  sys: Sys!
}

type ModalCollection {
  items: [Modal]!

  limit: Int!

  skip: Int!

  total: Int!
}

input ModalFilter {
  AND: [ModalFilter]

  OR: [ModalFilter]

  contentfulMetadata: ContentfulMetadataFilter

  coupon: cfCouponNestedFilter

  coupon_exists: Boolean

  headline: String

  headline_contains: String

  headline_exists: Boolean

  headline_in: [String]

  headline_not: String

  headline_not_contains: String

  headline_not_in: [String]

  id: String

  id_contains: String

  id_exists: Boolean

  id_in: [String]

  id_not: String

  id_not_contains: String

  id_not_in: [String]

  linkText: String

  linkText_contains: String

  linkText_exists: Boolean

  linkText_in: [String]

  linkText_not: String

  linkText_not_contains: String

  linkText_not_in: [String]

  primaryButtonLink: cfprimaryButtonLinkMultiTypeNestedFilter

  primaryButtonLink_exists: Boolean

  richtext_contains: String

  richtext_exists: Boolean

  richtext_not_contains: String

  sys: SysFilter
}

type ModalLinkingCollections {
  bannerCollection(limit: Int = 100, locale: String, order: [ModalLinkingCollectionsBannerCollectionOrder], preview: Boolean, skip: Int = 0): BannerCollection

  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum ModalLinkingCollectionsBannerCollectionOrder {
  countdownStart_ASC

  countdownStart_DESC

  countdown_ASC

  countdown_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  trackingName_ASC

  trackingName_DESC
}

enum ModalOrder {
  headline_ASC

  headline_DESC

  id_ASC

  id_DESC

  linkText_ASC

  linkText_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

union ModalPrimaryButtonLink = ExternalLink|InternalLink

input ModalPrimaryButtonLinkFilter {
  AND: [ModalPrimaryButtonLinkFilter]

  OR: [ModalPrimaryButtonLinkFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

type ModalRichtext {
  json: JSON!

  links: ModalRichtextLinks!
}

type ModalRichtextAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type ModalRichtextEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type ModalRichtextLinks {
  assets: ModalRichtextAssets!

  entries: ModalRichtextEntries!

  resources: ModalRichtextResources!
}

type ModalRichtextResources {
  block: [ModalRichtextResourcesBlock!]!

  hyperlink: [ModalRichtextResourcesHyperlink!]!

  inline: [ModalRichtextResourcesInline!]!
}

type ModalRichtextResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type ModalRichtextResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type ModalRichtextResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/noSearchResult)
"""
type NoSearchResult implements Entry & _Node {
  _id: ID!

  contactItemsCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0, where: NoSearchResultContactItemsFilter): NoSearchResultContactItemsCollection

  contentfulMetadata: ContentfulMetadata!

  helpHeading(locale: String): String

  id(locale: String): String

  image(locale: String, preview: Boolean): Asset

  imageCloudinary(locale: String): JSON

  introductoryRichtext(locale: String): NoSearchResultIntroductoryRichtext

  linkedFrom(allowedLocales: [String]): NoSearchResultLinkingCollections

  sys: Sys!
}

type NoSearchResultCollection {
  items: [NoSearchResult]!

  limit: Int!

  skip: Int!

  total: Int!
}

type NoSearchResultContactItemsCollection {
  items: [NoSearchResultContactItemsItem]!

  limit: Int!

  skip: Int!

  total: Int!
}

input NoSearchResultContactItemsFilter {
  AND: [NoSearchResultContactItemsFilter]

  OR: [NoSearchResultContactItemsFilter]

  contentfulMetadata: ContentfulMetadataFilter

  icon: String

  icon_contains: String

  icon_exists: Boolean

  icon_in: [String]

  icon_not: String

  icon_not_contains: String

  icon_not_in: [String]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

union NoSearchResultContactItemsItem = ContactBox|PhoneBox

input NoSearchResultFilter {
  AND: [NoSearchResultFilter]

  OR: [NoSearchResultFilter]

  contactItems: cfcontactItemsMultiTypeNestedFilter

  contactItemsCollection_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  helpHeading: String

  helpHeading_contains: String

  helpHeading_exists: Boolean

  helpHeading_in: [String]

  helpHeading_not: String

  helpHeading_not_contains: String

  helpHeading_not_in: [String]

  id: String

  id_contains: String

  id_exists: Boolean

  id_in: [String]

  id_not: String

  id_not_contains: String

  id_not_in: [String]

  imageCloudinary_exists: Boolean

  image_exists: Boolean

  introductoryRichtext_contains: String

  introductoryRichtext_exists: Boolean

  introductoryRichtext_not_contains: String

  sys: SysFilter
}

type NoSearchResultIntroductoryRichtext {
  json: JSON!

  links: NoSearchResultIntroductoryRichtextLinks!
}

type NoSearchResultIntroductoryRichtextAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type NoSearchResultIntroductoryRichtextEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type NoSearchResultIntroductoryRichtextLinks {
  assets: NoSearchResultIntroductoryRichtextAssets!

  entries: NoSearchResultIntroductoryRichtextEntries!

  resources: NoSearchResultIntroductoryRichtextResources!
}

type NoSearchResultIntroductoryRichtextResources {
  block: [NoSearchResultIntroductoryRichtextResourcesBlock!]!

  hyperlink: [NoSearchResultIntroductoryRichtextResourcesHyperlink!]!

  inline: [NoSearchResultIntroductoryRichtextResourcesInline!]!
}

type NoSearchResultIntroductoryRichtextResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type NoSearchResultIntroductoryRichtextResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type NoSearchResultIntroductoryRichtextResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

type NoSearchResultLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum NoSearchResultOrder {
  helpHeading_ASC

  helpHeading_DESC

  id_ASC

  id_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/none)
"""
type None implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): NoneLinkingCollections

  sys: Sys!
}

type NoneCollection {
  items: [None]!

  limit: Int!

  skip: Int!

  total: Int!
}

input NoneFilter {
  AND: [NoneFilter]

  OR: [NoneFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter
}

type NoneLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  seoContentCollection(limit: Int = 100, locale: String, order: [NoneLinkingCollectionsSeoContentCollectionOrder], preview: Boolean, skip: Int = 0): SeoContentCollection
}

enum NoneLinkingCollectionsSeoContentCollectionOrder {
  automaticShopname_ASC

  automaticShopname_DESC

  categoryTitle_ASC

  categoryTitle_DESC

  description_ASC

  description_DESC

  indexDirective_ASC

  indexDirective_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

enum NoneOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/orderState)
"""
type OrderState implements Entry & _Node {
  _id: ID!

  color(locale: String): String

  contentfulMetadata: ContentfulMetadata!

  key(locale: String): String

  linkedFrom(allowedLocales: [String]): OrderStateLinkingCollections

  sys: Sys!

  value(locale: String, preview: Boolean, where: LocaleTextFilter): LocaleText
}

type OrderStateCollection {
  items: [OrderState]!

  limit: Int!

  skip: Int!

  total: Int!
}

input OrderStateFilter {
  AND: [OrderStateFilter]

  OR: [OrderStateFilter]

  color: String

  color_contains: String

  color_exists: Boolean

  color_in: [String]

  color_not: String

  color_not_contains: String

  color_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  key: String

  key_contains: String

  key_exists: Boolean

  key_in: [String]

  key_not: String

  key_not_contains: String

  key_not_in: [String]

  sys: SysFilter

  value: cfLocaleTextNestedFilter

  value_exists: Boolean
}

type OrderStateLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum OrderStateOrder {
  color_ASC

  color_DESC

  key_ASC

  key_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/outfitItem)
"""
type OutfitItem implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  description(locale: String): OutfitItemDescription

  headline(locale: String): String

  image(locale: String, preview: Boolean): Asset

  imageCloudinary(locale: String): JSON

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): OutfitItemLinkingCollections

  recommendationGkAir(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  sys: Sys!
}

type OutfitItemCollection {
  items: [OutfitItem]!

  limit: Int!

  skip: Int!

  total: Int!
}

type OutfitItemDescription {
  json: JSON!

  links: OutfitItemDescriptionLinks!
}

type OutfitItemDescriptionAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type OutfitItemDescriptionEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type OutfitItemDescriptionLinks {
  assets: OutfitItemDescriptionAssets!

  entries: OutfitItemDescriptionEntries!

  resources: OutfitItemDescriptionResources!
}

type OutfitItemDescriptionResources {
  block: [OutfitItemDescriptionResourcesBlock!]!

  hyperlink: [OutfitItemDescriptionResourcesHyperlink!]!

  inline: [OutfitItemDescriptionResourcesInline!]!
}

type OutfitItemDescriptionResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type OutfitItemDescriptionResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type OutfitItemDescriptionResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

input OutfitItemFilter {
  AND: [OutfitItemFilter]

  OR: [OutfitItemFilter]

  contentfulMetadata: ContentfulMetadataFilter

  description_contains: String

  description_exists: Boolean

  description_not_contains: String

  headline: String

  headline_contains: String

  headline_exists: Boolean

  headline_in: [String]

  headline_not: String

  headline_not_contains: String

  headline_not_in: [String]

  imageCloudinary_exists: Boolean

  image_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  recommendationGkAir: cfRecommendationGkAirNestedFilter

  recommendationGkAir_exists: Boolean

  sys: SysFilter
}

type OutfitItemLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  outfitPageCollection(limit: Int = 100, locale: String, order: [OutfitItemLinkingCollectionsOutfitPageCollectionOrder], preview: Boolean, skip: Int = 0): OutfitPageCollection
}

enum OutfitItemLinkingCollectionsOutfitPageCollectionOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  moreOutfitsHeadline_ASC

  moreOutfitsHeadline_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum OutfitItemOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/outfitPage)
"""
type OutfitPage implements Entry & _Node {
  _id: ID!

  backlink(locale: String, preview: Boolean, where: OutfitPageBacklinkFilter): OutfitPageBacklink

  contentfulMetadata: ContentfulMetadata!

  headline(locale: String): String

  internalTitle(locale: String): String

  introductionText(locale: String): OutfitPageIntroductionText

  itemsCollection(limit: Int = 100, locale: String, order: [OutfitPageItemsCollectionOrder], preview: Boolean, skip: Int = 0, where: OutfitItemFilter): OutfitPageItemsCollection

  linkedFrom(allowedLocales: [String]): OutfitPageLinkingCollections

  moreOutfitPagesCollection(limit: Int = 100, locale: String, order: [OutfitPageMoreOutfitPagesCollectionOrder], preview: Boolean, skip: Int = 0, where: OutfitPageFilter): OutfitPageMoreOutfitPagesCollection

  moreOutfitsHeadline(locale: String): String

  seoContent(locale: String, preview: Boolean, where: SeoContentFilter): SeoContent

  slug(locale: String): String

  sys: Sys!
}

union OutfitPageBacklink = ExternalLink|InternalLink

input OutfitPageBacklinkFilter {
  AND: [OutfitPageBacklinkFilter]

  OR: [OutfitPageBacklinkFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

type OutfitPageCollection {
  items: [OutfitPage]!

  limit: Int!

  skip: Int!

  total: Int!
}

input OutfitPageFilter {
  AND: [OutfitPageFilter]

  OR: [OutfitPageFilter]

  backlink: cfbacklinkMultiTypeNestedFilter

  backlink_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  headline: String

  headline_contains: String

  headline_exists: Boolean

  headline_in: [String]

  headline_not: String

  headline_not_contains: String

  headline_not_in: [String]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  introductionText_contains: String

  introductionText_exists: Boolean

  introductionText_not_contains: String

  items: cfOutfitItemNestedFilter

  itemsCollection_exists: Boolean

  moreOutfitPages: cfOutfitPageNestedFilter

  moreOutfitPagesCollection_exists: Boolean

  moreOutfitsHeadline: String

  moreOutfitsHeadline_contains: String

  moreOutfitsHeadline_exists: Boolean

  moreOutfitsHeadline_in: [String]

  moreOutfitsHeadline_not: String

  moreOutfitsHeadline_not_contains: String

  moreOutfitsHeadline_not_in: [String]

  seoContent: cfSeoContentNestedFilter

  seoContent_exists: Boolean

  slug: String

  slug_contains: String

  slug_exists: Boolean

  slug_in: [String]

  slug_not: String

  slug_not_contains: String

  slug_not_in: [String]

  sys: SysFilter
}

type OutfitPageIntroductionText {
  json: JSON!

  links: OutfitPageIntroductionTextLinks!
}

type OutfitPageIntroductionTextAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type OutfitPageIntroductionTextEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type OutfitPageIntroductionTextLinks {
  assets: OutfitPageIntroductionTextAssets!

  entries: OutfitPageIntroductionTextEntries!

  resources: OutfitPageIntroductionTextResources!
}

type OutfitPageIntroductionTextResources {
  block: [OutfitPageIntroductionTextResourcesBlock!]!

  hyperlink: [OutfitPageIntroductionTextResourcesHyperlink!]!

  inline: [OutfitPageIntroductionTextResourcesInline!]!
}

type OutfitPageIntroductionTextResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type OutfitPageIntroductionTextResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type OutfitPageIntroductionTextResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

type OutfitPageItemsCollection {
  items: [OutfitItem]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum OutfitPageItemsCollectionOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

type OutfitPageLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  internalLinkCollection(limit: Int = 100, locale: String, order: [OutfitPageLinkingCollectionsInternalLinkCollectionOrder], preview: Boolean, skip: Int = 0): InternalLinkCollection

  outfitPageCollection(limit: Int = 100, locale: String, order: [OutfitPageLinkingCollectionsOutfitPageCollectionOrder], preview: Boolean, skip: Int = 0): OutfitPageCollection
}

enum OutfitPageLinkingCollectionsInternalLinkCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  label_ASC

  label_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum OutfitPageLinkingCollectionsOutfitPageCollectionOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  moreOutfitsHeadline_ASC

  moreOutfitsHeadline_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

type OutfitPageMoreOutfitPagesCollection {
  items: [OutfitPage]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum OutfitPageMoreOutfitPagesCollectionOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  moreOutfitsHeadline_ASC

  moreOutfitsHeadline_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum OutfitPageOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  moreOutfitsHeadline_ASC

  moreOutfitsHeadline_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/pageFooter)
"""
type PageFooter implements Entry & _Node {
  _id: ID!

  appColumnsBottomCollection(limit: Int = 100, locale: String, order: [PageFooterAppColumnsBottomCollectionOrder], preview: Boolean, skip: Int = 0, where: FooterColumnFilter): PageFooterAppColumnsBottomCollection

  checkoutColumnsBottomCollection(limit: Int = 100, locale: String, order: [PageFooterCheckoutColumnsBottomCollectionOrder], preview: Boolean, skip: Int = 0, where: FooterColumnFilter): PageFooterCheckoutColumnsBottomCollection

  checkoutColumnsCollection(limit: Int = 100, locale: String, order: [PageFooterCheckoutColumnsCollectionOrder], preview: Boolean, skip: Int = 0, where: FooterColumnFilter): PageFooterCheckoutColumnsCollection

  columnsBottomCollection(limit: Int = 100, locale: String, order: [PageFooterColumnsBottomCollectionOrder], preview: Boolean, skip: Int = 0, where: FooterColumnFilter): PageFooterColumnsBottomCollection

  columnsCollection(limit: Int = 100, locale: String, order: [PageFooterColumnsCollectionOrder], preview: Boolean, skip: Int = 0, where: FooterColumnFilter): PageFooterColumnsCollection

  contentfulMetadata: ContentfulMetadata!

  id(locale: String): String

  legalLinksCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0, where: PageFooterLegalLinksFilter): PageFooterLegalLinksCollection

  linkedFrom(allowedLocales: [String]): PageFooterLinkingCollections

  sys: Sys!
}

type PageFooterAppColumnsBottomCollection {
  items: [FooterColumn]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum PageFooterAppColumnsBottomCollectionOrder {
  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

type PageFooterCheckoutColumnsBottomCollection {
  items: [FooterColumn]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum PageFooterCheckoutColumnsBottomCollectionOrder {
  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

type PageFooterCheckoutColumnsCollection {
  items: [FooterColumn]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum PageFooterCheckoutColumnsCollectionOrder {
  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

type PageFooterCollection {
  items: [PageFooter]!

  limit: Int!

  skip: Int!

  total: Int!
}

type PageFooterColumnsBottomCollection {
  items: [FooterColumn]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum PageFooterColumnsBottomCollectionOrder {
  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

type PageFooterColumnsCollection {
  items: [FooterColumn]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum PageFooterColumnsCollectionOrder {
  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

input PageFooterFilter {
  AND: [PageFooterFilter]

  OR: [PageFooterFilter]

  appColumnsBottom: cfFooterColumnNestedFilter

  appColumnsBottomCollection_exists: Boolean

  checkoutColumns: cfFooterColumnNestedFilter

  checkoutColumnsBottom: cfFooterColumnNestedFilter

  checkoutColumnsBottomCollection_exists: Boolean

  checkoutColumnsCollection_exists: Boolean

  columns: cfFooterColumnNestedFilter

  columnsBottom: cfFooterColumnNestedFilter

  columnsBottomCollection_exists: Boolean

  columnsCollection_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  id: String

  id_contains: String

  id_exists: Boolean

  id_in: [String]

  id_not: String

  id_not_contains: String

  id_not_in: [String]

  legalLinks: cflegalLinksMultiTypeNestedFilter

  legalLinksCollection_exists: Boolean

  sys: SysFilter
}

type PageFooterLegalLinksCollection {
  items: [PageFooterLegalLinksItem]!

  limit: Int!

  skip: Int!

  total: Int!
}

input PageFooterLegalLinksFilter {
  AND: [PageFooterLegalLinksFilter]

  OR: [PageFooterLegalLinksFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

union PageFooterLegalLinksItem = ExternalLink|InternalLink

type PageFooterLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum PageFooterOrder {
  id_ASC

  id_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/panel)
"""
type Panel implements Entry & _Node {
  _id: ID!

  content(locale: String): PanelContent

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): PanelLinkingCollections

  sys: Sys!
}

type PanelCollection {
  items: [Panel]!

  limit: Int!

  skip: Int!

  total: Int!
}

type PanelContent {
  json: JSON!

  links: PanelContentLinks!
}

type PanelContentAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type PanelContentEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type PanelContentLinks {
  assets: PanelContentAssets!

  entries: PanelContentEntries!

  resources: PanelContentResources!
}

type PanelContentResources {
  block: [PanelContentResourcesBlock!]!

  hyperlink: [PanelContentResourcesHyperlink!]!

  inline: [PanelContentResourcesInline!]!
}

type PanelContentResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type PanelContentResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type PanelContentResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

input PanelFilter {
  AND: [PanelFilter]

  OR: [PanelFilter]

  content_contains: String

  content_exists: Boolean

  content_not_contains: String

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter
}

type PanelLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum PanelOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/pdp)
"""
type Pdp implements Entry & _Node {
  _id: ID!

  banner(locale: String, preview: Boolean, where: BannerFilter): Banner

  categoryIds(locale: String): [String]

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): PdpLinkingCollections

  sys: Sys!

  trigger(locale: String, preview: Boolean, where: TopLevelTriggerFilter): TopLevelTrigger

  type(locale: String): [String]
}

type PdpCollection {
  items: [Pdp]!

  limit: Int!

  skip: Int!

  total: Int!
}

input PdpFilter {
  AND: [PdpFilter]

  OR: [PdpFilter]

  banner: cfBannerNestedFilter

  banner_exists: Boolean

  categoryIds_contains_all: [String]

  categoryIds_contains_none: [String]

  categoryIds_contains_some: [String]

  categoryIds_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter

  trigger: cfTopLevelTriggerNestedFilter

  trigger_exists: Boolean

  type_contains_all: [String]

  type_contains_none: [String]

  type_contains_some: [String]

  type_exists: Boolean
}

type PdpLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum PdpOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/phoneBox)
"""
type PhoneBox implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  disclaimer(locale: String): String

  disclaimerIcon(locale: String): Boolean

  icon(locale: String): String

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): PhoneBoxLinkingCollections

  number(locale: String): String

  subtitle(locale: String): String

  sys: Sys!

  title(locale: String): String

  useInOffCanvasNavigation(locale: String): [String]
}

type PhoneBoxCollection {
  items: [PhoneBox]!

  limit: Int!

  skip: Int!

  total: Int!
}

input PhoneBoxFilter {
  AND: [PhoneBoxFilter]

  OR: [PhoneBoxFilter]

  contentfulMetadata: ContentfulMetadataFilter

  disclaimer: String

  disclaimerIcon: Boolean

  disclaimerIcon_exists: Boolean

  disclaimerIcon_not: Boolean

  disclaimer_contains: String

  disclaimer_exists: Boolean

  disclaimer_in: [String]

  disclaimer_not: String

  disclaimer_not_contains: String

  disclaimer_not_in: [String]

  icon: String

  icon_contains: String

  icon_exists: Boolean

  icon_in: [String]

  icon_not: String

  icon_not_contains: String

  icon_not_in: [String]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  number: String

  number_contains: String

  number_exists: Boolean

  number_in: [String]

  number_not: String

  number_not_contains: String

  number_not_in: [String]

  subtitle: String

  subtitle_contains: String

  subtitle_exists: Boolean

  subtitle_in: [String]

  subtitle_not: String

  subtitle_not_contains: String

  subtitle_not_in: [String]

  sys: SysFilter

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]

  useInOffCanvasNavigation_contains_all: [String]

  useInOffCanvasNavigation_contains_none: [String]

  useInOffCanvasNavigation_contains_some: [String]

  useInOffCanvasNavigation_exists: Boolean
}

type PhoneBoxLinkingCollections {
  contactPageCollection(limit: Int = 100, locale: String, order: [PhoneBoxLinkingCollectionsContactPageCollectionOrder], preview: Boolean, skip: Int = 0): ContactPageCollection

  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  footerColumnCollection(limit: Int = 100, locale: String, order: [PhoneBoxLinkingCollectionsFooterColumnCollectionOrder], preview: Boolean, skip: Int = 0): FooterColumnCollection

  noSearchResultCollection(limit: Int = 100, locale: String, order: [PhoneBoxLinkingCollectionsNoSearchResultCollectionOrder], preview: Boolean, skip: Int = 0): NoSearchResultCollection
}

enum PhoneBoxLinkingCollectionsContactPageCollectionOrder {
  headline_ASC

  headline_DESC

  id_ASC

  id_DESC

  subtitle_ASC

  subtitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum PhoneBoxLinkingCollectionsFooterColumnCollectionOrder {
  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

enum PhoneBoxLinkingCollectionsNoSearchResultCollectionOrder {
  helpHeading_ASC

  helpHeading_DESC

  id_ASC

  id_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum PhoneBoxOrder {
  disclaimerIcon_ASC

  disclaimerIcon_DESC

  disclaimer_ASC

  disclaimer_DESC

  icon_ASC

  icon_DESC

  internalTitle_ASC

  internalTitle_DESC

  number_ASC

  number_DESC

  subtitle_ASC

  subtitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/plp)
"""
type Plp implements Entry & _Node {
  _id: ID!

  bottomRecommendation(locale: String, preview: Boolean, where: RecommendationGkAirFilter): RecommendationGkAir

  categoryId(locale: String): [String]

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): PlpLinkingCollections

  onsiteContentBannersV2Collection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0, where: PlpOnsiteContentBannersV2Filter): PlpOnsiteContentBannersV2Collection

  onsiteContentCardsCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0, where: PlpOnsiteContentCardsFilter): PlpOnsiteContentCardsCollection

  sys: Sys!

  trigger(locale: String, preview: Boolean, where: TopLevelTriggerFilter): TopLevelTrigger
}

type PlpCollection {
  items: [Plp]!

  limit: Int!

  skip: Int!

  total: Int!
}

input PlpFilter {
  AND: [PlpFilter]

  OR: [PlpFilter]

  bottomRecommendation: cfRecommendationGkAirNestedFilter

  bottomRecommendation_exists: Boolean

  categoryId_contains_all: [String]

  categoryId_contains_none: [String]

  categoryId_contains_some: [String]

  categoryId_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  onsiteContentBannersV2: cfonsiteContentBannersV2MultiTypeNestedFilter

  onsiteContentBannersV2Collection_exists: Boolean

  onsiteContentCards: cfonsiteContentCardsMultiTypeNestedFilter

  onsiteContentCardsCollection_exists: Boolean

  sys: SysFilter

  trigger: cfTopLevelTriggerNestedFilter

  trigger_exists: Boolean
}

type PlpLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

type PlpOnsiteContentBannersV2Collection {
  items: [PlpOnsiteContentBannersV2Item]!

  limit: Int!

  skip: Int!

  total: Int!
}

input PlpOnsiteContentBannersV2Filter {
  AND: [PlpOnsiteContentBannersV2Filter]

  OR: [PlpOnsiteContentBannersV2Filter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter
}

union PlpOnsiteContentBannersV2Item = Banner|Richtext

type PlpOnsiteContentCardsCollection {
  items: [PlpOnsiteContentCardsItem]!

  limit: Int!

  skip: Int!

  total: Int!
}

input PlpOnsiteContentCardsFilter {
  AND: [PlpOnsiteContentCardsFilter]

  OR: [PlpOnsiteContentCardsFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

union PlpOnsiteContentCardsItem = Promotion|Richtext

enum PlpOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/promotion)
"""
type Promotion implements Entry & _Node {
  _id: ID!

  additionalLinksCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0, where: PromotionAdditionalLinksFilter): PromotionAdditionalLinksCollection

  assetSet(locale: String, preview: Boolean, where: AssetSetFilter): AssetSet

  callToAction(locale: String): String

  categoryImageLink(locale: String, preview: Boolean, where: CategoryImageLinkFilter): CategoryImageLink

  contentfulMetadata: ContentfulMetadata!

  eyecatcherDesign(locale: String): String

  eyecatcherRichText(locale: String): PromotionEyecatcherRichText

  eyecatcherType(locale: String): String

  image(locale: String, preview: Boolean): Asset

  imageCloudinary(locale: String): JSON

  internalTitle(locale: String): String

  layoutColorPicker(locale: String): JSON

  link(locale: String, preview: Boolean, where: PromotionLinkFilter): PromotionLink

  linkedFrom(allowedLocales: [String]): PromotionLinkingCollections

  pinnedProductIDs(locale: String): [String]

  productId(locale: String): String

  promotionTarget(locale: String): String

  styledTitle(locale: String): PromotionStyledTitle

  sys: Sys!

  text(locale: String): String

  title(locale: String): String

  trackingName(locale: String): String
}

type PromotionAdditionalLinksCollection {
  items: [PromotionAdditionalLinksItem]!

  limit: Int!

  skip: Int!

  total: Int!
}

input PromotionAdditionalLinksFilter {
  AND: [PromotionAdditionalLinksFilter]

  OR: [PromotionAdditionalLinksFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

union PromotionAdditionalLinksItem = ExternalLink|InternalLink

type PromotionCollection {
  items: [Promotion]!

  limit: Int!

  skip: Int!

  total: Int!
}

type PromotionEyecatcherRichText {
  json: JSON!

  links: PromotionEyecatcherRichTextLinks!
}

type PromotionEyecatcherRichTextAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type PromotionEyecatcherRichTextEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type PromotionEyecatcherRichTextLinks {
  assets: PromotionEyecatcherRichTextAssets!

  entries: PromotionEyecatcherRichTextEntries!

  resources: PromotionEyecatcherRichTextResources!
}

type PromotionEyecatcherRichTextResources {
  block: [PromotionEyecatcherRichTextResourcesBlock!]!

  hyperlink: [PromotionEyecatcherRichTextResourcesHyperlink!]!

  inline: [PromotionEyecatcherRichTextResourcesInline!]!
}

type PromotionEyecatcherRichTextResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type PromotionEyecatcherRichTextResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type PromotionEyecatcherRichTextResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

input PromotionFilter {
  AND: [PromotionFilter]

  OR: [PromotionFilter]

  additionalLinks: cfadditionalLinksMultiTypeNestedFilter

  additionalLinksCollection_exists: Boolean

  assetSet: cfAssetSetNestedFilter

  assetSet_exists: Boolean

  callToAction: String

  callToAction_contains: String

  callToAction_exists: Boolean

  callToAction_in: [String]

  callToAction_not: String

  callToAction_not_contains: String

  callToAction_not_in: [String]

  categoryImageLink: cfCategoryImageLinkNestedFilter

  categoryImageLink_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  eyecatcherDesign: String

  eyecatcherDesign_contains: String

  eyecatcherDesign_exists: Boolean

  eyecatcherDesign_in: [String]

  eyecatcherDesign_not: String

  eyecatcherDesign_not_contains: String

  eyecatcherDesign_not_in: [String]

  eyecatcherRichText_contains: String

  eyecatcherRichText_exists: Boolean

  eyecatcherRichText_not_contains: String

  eyecatcherType: String

  eyecatcherType_contains: String

  eyecatcherType_exists: Boolean

  eyecatcherType_in: [String]

  eyecatcherType_not: String

  eyecatcherType_not_contains: String

  eyecatcherType_not_in: [String]

  imageCloudinary_exists: Boolean

  image_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  layoutColorPicker_exists: Boolean

  link: cflinkMultiTypeNestedFilter

  link_exists: Boolean

  pinnedProductIDs_contains_all: [String]

  pinnedProductIDs_contains_none: [String]

  pinnedProductIDs_contains_some: [String]

  pinnedProductIDs_exists: Boolean

  productId: String

  productId_contains: String

  productId_exists: Boolean

  productId_in: [String]

  productId_not: String

  productId_not_contains: String

  productId_not_in: [String]

  promotionTarget: String

  promotionTarget_contains: String

  promotionTarget_exists: Boolean

  promotionTarget_in: [String]

  promotionTarget_not: String

  promotionTarget_not_contains: String

  promotionTarget_not_in: [String]

  styledTitle_contains: String

  styledTitle_exists: Boolean

  styledTitle_not_contains: String

  sys: SysFilter

  text: String

  text_contains: String

  text_exists: Boolean

  text_in: [String]

  text_not: String

  text_not_contains: String

  text_not_in: [String]

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]

  trackingName: String

  trackingName_contains: String

  trackingName_exists: Boolean

  trackingName_in: [String]

  trackingName_not: String

  trackingName_not_contains: String

  trackingName_not_in: [String]
}

union PromotionLink = ExternalLink|InternalLink

input PromotionLinkFilter {
  AND: [PromotionLinkFilter]

  OR: [PromotionLinkFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

type PromotionLinkingCollections {
  cardCollection(limit: Int = 100, locale: String, order: [PromotionLinkingCollectionsCardCollectionOrder], preview: Boolean, skip: Int = 0): CardCollection

  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  footerColumnCollection(limit: Int = 100, locale: String, order: [PromotionLinkingCollectionsFooterColumnCollectionOrder], preview: Boolean, skip: Int = 0): FooterColumnCollection

  homepageCreationLCollection(limit: Int = 100, locale: String, order: [PromotionLinkingCollectionsHomepageCreationLCollectionOrder], preview: Boolean, skip: Int = 0): HomepageCreationLCollection

  homepageHeineV2Collection(limit: Int = 100, locale: String, order: [PromotionLinkingCollectionsHomepageHeineV2CollectionOrder], preview: Boolean, skip: Int = 0): HomepageHeineV2Collection

  homepageSheegoCollection(limit: Int = 100, locale: String, order: [PromotionLinkingCollectionsHomepageSheegoCollectionOrder], preview: Boolean, skip: Int = 0): HomepageSheegoCollection

  homepageSiehAnCollection(limit: Int = 100, locale: String, order: [PromotionLinkingCollectionsHomepageSiehAnCollectionOrder], preview: Boolean, skip: Int = 0): HomepageSiehAnCollection

  homepageSiehAnV2Collection(limit: Int = 100, locale: String, order: [PromotionLinkingCollectionsHomepageSiehAnV2CollectionOrder], preview: Boolean, skip: Int = 0): HomepageSiehAnV2Collection

  homepageWittV2Collection(limit: Int = 100, locale: String, order: [PromotionLinkingCollectionsHomepageWittV2CollectionOrder], preview: Boolean, skip: Int = 0): HomepageWittV2Collection

  plpCollection(limit: Int = 100, locale: String, order: [PromotionLinkingCollectionsPlpCollectionOrder], preview: Boolean, skip: Int = 0): PlpCollection
}

enum PromotionLinkingCollectionsCardCollectionOrder {
  displayType_ASC

  displayType_DESC

  imageAspectRatio_ASC

  imageAspectRatio_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum PromotionLinkingCollectionsFooterColumnCollectionOrder {
  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

enum PromotionLinkingCollectionsHomepageCreationLCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum PromotionLinkingCollectionsHomepageHeineV2CollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  promotionGridTitle_ASC

  promotionGridTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  titleTextCategories_ASC

  titleTextCategories_DESC
}

enum PromotionLinkingCollectionsHomepageSheegoCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  optionalPromotionBarTitle_ASC

  optionalPromotionBarTitle_DESC

  promotionBar2Title_ASC

  promotionBar2Title_DESC

  promotionBar3Title_ASC

  promotionBar3Title_DESC

  promotionBarTitle_ASC

  promotionBarTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum PromotionLinkingCollectionsHomepageSiehAnCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum PromotionLinkingCollectionsHomepageSiehAnV2CollectionOrder {
  brandFormulaPart1_ASC

  brandFormulaPart1_DESC

  brandFormulaPart2_ASC

  brandFormulaPart2_DESC

  brandFormulaResult_ASC

  brandFormulaResult_DESC

  heroDoodles_ASC

  heroDoodles_DESC

  internalTitle_ASC

  internalTitle_DESC

  prioDoodles1_ASC

  prioDoodles1_DESC

  prioDoodles2_ASC

  prioDoodles2_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  titleAssortmentPromotions_ASC

  titleAssortmentPromotions_DESC

  titleCards_ASC

  titleCards_DESC

  titleIconPromotions_ASC

  titleIconPromotions_DESC
}

enum PromotionLinkingCollectionsHomepageWittV2CollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum PromotionLinkingCollectionsPlpCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum PromotionOrder {
  callToAction_ASC

  callToAction_DESC

  eyecatcherDesign_ASC

  eyecatcherDesign_DESC

  eyecatcherType_ASC

  eyecatcherType_DESC

  internalTitle_ASC

  internalTitle_DESC

  productId_ASC

  productId_DESC

  promotionTarget_ASC

  promotionTarget_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  text_ASC

  text_DESC

  title_ASC

  title_DESC

  trackingName_ASC

  trackingName_DESC
}

type PromotionStyledTitle {
  json: JSON!

  links: PromotionStyledTitleLinks!
}

type PromotionStyledTitleAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type PromotionStyledTitleEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type PromotionStyledTitleLinks {
  assets: PromotionStyledTitleAssets!

  entries: PromotionStyledTitleEntries!

  resources: PromotionStyledTitleResources!
}

type PromotionStyledTitleResources {
  block: [PromotionStyledTitleResourcesBlock!]!

  hyperlink: [PromotionStyledTitleResourcesHyperlink!]!

  inline: [PromotionStyledTitleResourcesInline!]!
}

type PromotionStyledTitleResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type PromotionStyledTitleResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type PromotionStyledTitleResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

"""
The 'Quality' type represents quality as whole numeric values between `1` and `100`.
"""
scalar Quality

type Query {
  _node(id: ID!, locale: String, preview: Boolean): _Node

  _nodes(ids: [ID!]!, locale: String, preview: Boolean): [_Node]!

  accordion(id: String!, locale: String, preview: Boolean): Accordion

  accordionCollection(limit: Int = 100, locale: String, order: [AccordionOrder], preview: Boolean, skip: Int = 0, where: AccordionFilter): AccordionCollection

  actionButton(id: String!, locale: String, preview: Boolean): ActionButton

  actionButtonCollection(limit: Int = 100, locale: String, order: [ActionButtonOrder], preview: Boolean, skip: Int = 0, where: ActionButtonFilter): ActionButtonCollection

  anchorLink(id: String!, locale: String, preview: Boolean): AnchorLink

  anchorLinkCollection(limit: Int = 100, locale: String, order: [AnchorLinkOrder], preview: Boolean, skip: Int = 0, where: AnchorLinkFilter): AnchorLinkCollection

  asset(id: String!, locale: String, preview: Boolean): Asset

  assetCollection(limit: Int = 100, locale: String, order: [AssetOrder], preview: Boolean, skip: Int = 0, where: AssetFilter): AssetCollection

  assetSet(id: String!, locale: String, preview: Boolean): AssetSet

  assetSetCollection(limit: Int = 100, locale: String, order: [AssetSetOrder], preview: Boolean, skip: Int = 0, where: AssetSetFilter): AssetSetCollection

  banner(id: String!, locale: String, preview: Boolean): Banner

  bannerCollection(limit: Int = 100, locale: String, order: [BannerOrder], preview: Boolean, skip: Int = 0, where: BannerFilter): BannerCollection

  box(id: String!, locale: String, preview: Boolean): Box

  boxCollection(limit: Int = 100, locale: String, order: [BoxOrder], preview: Boolean, skip: Int = 0, where: BoxFilter): BoxCollection

  button(id: String!, locale: String, preview: Boolean): Button

  buttonCollection(limit: Int = 100, locale: String, order: [ButtonOrder], preview: Boolean, skip: Int = 0, where: ButtonFilter): ButtonCollection

  card(id: String!, locale: String, preview: Boolean): Card

  cardCollection(limit: Int = 100, locale: String, order: [CardOrder], preview: Boolean, skip: Int = 0, where: CardFilter): CardCollection

  categoryImageLink(id: String!, locale: String, preview: Boolean): CategoryImageLink

  categoryImageLinkCollection(limit: Int = 100, locale: String, order: [CategoryImageLinkOrder], preview: Boolean, skip: Int = 0, where: CategoryImageLinkFilter): CategoryImageLinkCollection

  checklist(id: String!, locale: String, preview: Boolean): Checklist

  checklistCollection(limit: Int = 100, locale: String, order: [ChecklistOrder], preview: Boolean, skip: Int = 0, where: ChecklistFilter): ChecklistCollection

  contactBox(id: String!, locale: String, preview: Boolean): ContactBox

  contactBoxCollection(limit: Int = 100, locale: String, order: [ContactBoxOrder], preview: Boolean, skip: Int = 0, where: ContactBoxFilter): ContactBoxCollection

  contactPage(id: String!, locale: String, preview: Boolean): ContactPage

  contactPageCollection(limit: Int = 100, locale: String, order: [ContactPageOrder], preview: Boolean, skip: Int = 0, where: ContactPageFilter): ContactPageCollection

  contentItemAsset(id: String!, locale: String, preview: Boolean): ContentItemAsset

  contentItemAssetCollection(limit: Int = 100, locale: String, order: [ContentItemAssetOrder], preview: Boolean, skip: Int = 0, where: ContentItemAssetFilter): ContentItemAssetCollection

  contentPage(id: String!, locale: String, preview: Boolean): ContentPage

  contentPageCollection(limit: Int = 100, locale: String, order: [ContentPageOrder], preview: Boolean, skip: Int = 0, where: ContentPageFilter): ContentPageCollection

  cookieList(id: String!, locale: String, preview: Boolean): CookieList

  cookieListCollection(limit: Int = 100, locale: String, order: [CookieListOrder], preview: Boolean, skip: Int = 0, where: CookieListFilter): CookieListCollection

  coupon(id: String!, locale: String, preview: Boolean): Coupon

  couponCollection(limit: Int = 100, locale: String, order: [CouponOrder], preview: Boolean, skip: Int = 0, where: CouponFilter): CouponCollection

  dataBankDetails(id: String!, locale: String, preview: Boolean): DataBankDetails

  dataBankDetailsCollection(limit: Int = 100, locale: String, order: [DataBankDetailsOrder], preview: Boolean, skip: Int = 0, where: DataBankDetailsFilter): DataBankDetailsCollection

  dataProvider(id: String!, locale: String, preview: Boolean): DataProvider

  dataProviderCollection(limit: Int = 100, locale: String, order: [DataProviderOrder], preview: Boolean, skip: Int = 0, where: DataProviderFilter): DataProviderCollection

  dosDonts(id: String!, locale: String, preview: Boolean): DosDonts

  dosDontsCollection(limit: Int = 100, locale: String, order: [DosDontsOrder], preview: Boolean, skip: Int = 0, where: DosDontsFilter): DosDontsCollection

  eccItem(id: String!, locale: String, preview: Boolean): EccItem

  eccItemCollection(limit: Int = 100, locale: String, order: [EccItemOrder], preview: Boolean, skip: Int = 0, where: EccItemFilter): EccItemCollection

  eccList(id: String!, locale: String, preview: Boolean): EccList

  eccListCollection(limit: Int = 100, locale: String, order: [EccListOrder], preview: Boolean, skip: Int = 0, where: EccListFilter): EccListCollection

  entryCollection(limit: Int = 100, locale: String, order: [EntryOrder], preview: Boolean, skip: Int = 0, where: EntryFilter): EntryCollection

  errorPage(id: String!, locale: String, preview: Boolean): ErrorPage

  errorPageCollection(limit: Int = 100, locale: String, order: [ErrorPageOrder], preview: Boolean, skip: Int = 0, where: ErrorPageFilter): ErrorPageCollection

  externalLink(id: String!, locale: String, preview: Boolean): ExternalLink

  externalLinkCollection(limit: Int = 100, locale: String, order: [ExternalLinkOrder], preview: Boolean, skip: Int = 0, where: ExternalLinkFilter): ExternalLinkCollection

  footerColumn(id: String!, locale: String, preview: Boolean): FooterColumn

  footerColumnCollection(limit: Int = 100, locale: String, order: [FooterColumnOrder], preview: Boolean, skip: Int = 0, where: FooterColumnFilter): FooterColumnCollection

  gkAirOutputConfig(id: String!, locale: String, preview: Boolean): GkAirOutputConfig

  gkAirOutputConfigCollection(limit: Int = 100, locale: String, order: [GkAirOutputConfigOrder], preview: Boolean, skip: Int = 0, where: GkAirOutputConfigFilter): GkAirOutputConfigCollection

  gkAirParameter(id: String!, locale: String, preview: Boolean): GkAirParameter

  gkAirParameterCollection(limit: Int = 100, locale: String, order: [GkAirParameterOrder], preview: Boolean, skip: Int = 0, where: GkAirParameterFilter): GkAirParameterCollection

  gkAirService(id: String!, locale: String, preview: Boolean): GkAirService

  gkAirServiceCollection(limit: Int = 100, locale: String, order: [GkAirServiceOrder], preview: Boolean, skip: Int = 0, where: GkAirServiceFilter): GkAirServiceCollection

  globalContent(id: String!, locale: String, preview: Boolean): GlobalContent

  globalContentCollection(limit: Int = 100, locale: String, order: [GlobalContentOrder], preview: Boolean, skip: Int = 0, where: GlobalContentFilter): GlobalContentCollection

  healthCheck(id: String!, locale: String, preview: Boolean): HealthCheck

  healthCheckCollection(limit: Int = 100, locale: String, order: [HealthCheckOrder], preview: Boolean, skip: Int = 0, where: HealthCheckFilter): HealthCheckCollection

  homepageCreationL(id: String!, locale: String, preview: Boolean): HomepageCreationL

  homepageCreationLCollection(limit: Int = 100, locale: String, order: [HomepageCreationLOrder], preview: Boolean, skip: Int = 0, where: HomepageCreationLFilter): HomepageCreationLCollection

  homepageHeineV2(id: String!, locale: String, preview: Boolean): HomepageHeineV2

  homepageHeineV2Collection(limit: Int = 100, locale: String, order: [HomepageHeineV2Order], preview: Boolean, skip: Int = 0, where: HomepageHeineV2Filter): HomepageHeineV2Collection

  homepageSheego(id: String!, locale: String, preview: Boolean): HomepageSheego

  homepageSheegoCollection(limit: Int = 100, locale: String, order: [HomepageSheegoOrder], preview: Boolean, skip: Int = 0, where: HomepageSheegoFilter): HomepageSheegoCollection

  homepageSiehAn(id: String!, locale: String, preview: Boolean): HomepageSiehAn

  homepageSiehAnCollection(limit: Int = 100, locale: String, order: [HomepageSiehAnOrder], preview: Boolean, skip: Int = 0, where: HomepageSiehAnFilter): HomepageSiehAnCollection

  homepageSiehAnV2(id: String!, locale: String, preview: Boolean): HomepageSiehAnV2

  homepageSiehAnV2Collection(limit: Int = 100, locale: String, order: [HomepageSiehAnV2Order], preview: Boolean, skip: Int = 0, where: HomepageSiehAnV2Filter): HomepageSiehAnV2Collection

  homepageWittV2(id: String!, locale: String, preview: Boolean): HomepageWittV2

  homepageWittV2Collection(limit: Int = 100, locale: String, order: [HomepageWittV2Order], preview: Boolean, skip: Int = 0, where: HomepageWittV2Filter): HomepageWittV2Collection

  iFrame(id: String!, locale: String, preview: Boolean): IFrame

  iFrameCollection(limit: Int = 100, locale: String, order: [IFrameOrder], preview: Boolean, skip: Int = 0, where: IFrameFilter): IFrameCollection

  inlineAsset(id: String!, locale: String, preview: Boolean): InlineAsset

  inlineAssetCollection(limit: Int = 100, locale: String, order: [InlineAssetOrder], preview: Boolean, skip: Int = 0, where: InlineAssetFilter): InlineAssetCollection

  inspirationItem(id: String!, locale: String, preview: Boolean): InspirationItem

  inspirationItemCollection(limit: Int = 100, locale: String, order: [InspirationItemOrder], preview: Boolean, skip: Int = 0, where: InspirationItemFilter): InspirationItemCollection

  inspirationPage(id: String!, locale: String, preview: Boolean): InspirationPage

  inspirationPageCollection(limit: Int = 100, locale: String, order: [InspirationPageOrder], preview: Boolean, skip: Int = 0, where: InspirationPageFilter): InspirationPageCollection

  internalLink(id: String!, locale: String, preview: Boolean): InternalLink

  internalLinkCollection(limit: Int = 100, locale: String, order: [InternalLinkOrder], preview: Boolean, skip: Int = 0, where: InternalLinkFilter): InternalLinkCollection

  layout(id: String!, locale: String, preview: Boolean): Layout

  layoutCollection(limit: Int = 100, locale: String, order: [LayoutOrder], preview: Boolean, skip: Int = 0, where: LayoutFilter): LayoutCollection

  layoutColumn(id: String!, locale: String, preview: Boolean): LayoutColumn

  layoutColumnCollection(limit: Int = 100, locale: String, order: [LayoutColumnOrder], preview: Boolean, skip: Int = 0, where: LayoutColumnFilter): LayoutColumnCollection

  linkBox(id: String!, locale: String, preview: Boolean): LinkBox

  linkBoxCollection(limit: Int = 100, locale: String, order: [LinkBoxOrder], preview: Boolean, skip: Int = 0, where: LinkBoxFilter): LinkBoxCollection

  localeContent(id: String!, locale: String, preview: Boolean): LocaleContent

  localeContentCollection(limit: Int = 100, locale: String, order: [LocaleContentOrder], preview: Boolean, skip: Int = 0, where: LocaleContentFilter): LocaleContentCollection

  localeImage(id: String!, locale: String, preview: Boolean): LocaleImage

  localeImageCollection(limit: Int = 100, locale: String, order: [LocaleImageOrder], preview: Boolean, skip: Int = 0, where: LocaleImageFilter): LocaleImageCollection

  localeText(id: String!, locale: String, preview: Boolean): LocaleText

  localeTextCollection(limit: Int = 100, locale: String, order: [LocaleTextOrder], preview: Boolean, skip: Int = 0, where: LocaleTextFilter): LocaleTextCollection

  modal(id: String!, locale: String, preview: Boolean): Modal

  modalCollection(limit: Int = 100, locale: String, order: [ModalOrder], preview: Boolean, skip: Int = 0, where: ModalFilter): ModalCollection

  noSearchResult(id: String!, locale: String, preview: Boolean): NoSearchResult

  noSearchResultCollection(limit: Int = 100, locale: String, order: [NoSearchResultOrder], preview: Boolean, skip: Int = 0, where: NoSearchResultFilter): NoSearchResultCollection

  none(id: String!, locale: String, preview: Boolean): None

  noneCollection(limit: Int = 100, locale: String, order: [NoneOrder], preview: Boolean, skip: Int = 0, where: NoneFilter): NoneCollection

  orderState(id: String!, locale: String, preview: Boolean): OrderState

  orderStateCollection(limit: Int = 100, locale: String, order: [OrderStateOrder], preview: Boolean, skip: Int = 0, where: OrderStateFilter): OrderStateCollection

  outfitItem(id: String!, locale: String, preview: Boolean): OutfitItem

  outfitItemCollection(limit: Int = 100, locale: String, order: [OutfitItemOrder], preview: Boolean, skip: Int = 0, where: OutfitItemFilter): OutfitItemCollection

  outfitPage(id: String!, locale: String, preview: Boolean): OutfitPage

  outfitPageCollection(limit: Int = 100, locale: String, order: [OutfitPageOrder], preview: Boolean, skip: Int = 0, where: OutfitPageFilter): OutfitPageCollection

  pageFooter(id: String!, locale: String, preview: Boolean): PageFooter

  pageFooterCollection(limit: Int = 100, locale: String, order: [PageFooterOrder], preview: Boolean, skip: Int = 0, where: PageFooterFilter): PageFooterCollection

  panel(id: String!, locale: String, preview: Boolean): Panel

  panelCollection(limit: Int = 100, locale: String, order: [PanelOrder], preview: Boolean, skip: Int = 0, where: PanelFilter): PanelCollection

  pdp(id: String!, locale: String, preview: Boolean): Pdp

  pdpCollection(limit: Int = 100, locale: String, order: [PdpOrder], preview: Boolean, skip: Int = 0, where: PdpFilter): PdpCollection

  phoneBox(id: String!, locale: String, preview: Boolean): PhoneBox

  phoneBoxCollection(limit: Int = 100, locale: String, order: [PhoneBoxOrder], preview: Boolean, skip: Int = 0, where: PhoneBoxFilter): PhoneBoxCollection

  plp(id: String!, locale: String, preview: Boolean): Plp

  plpCollection(limit: Int = 100, locale: String, order: [PlpOrder], preview: Boolean, skip: Int = 0, where: PlpFilter): PlpCollection

  promotion(id: String!, locale: String, preview: Boolean): Promotion

  promotionCollection(limit: Int = 100, locale: String, order: [PromotionOrder], preview: Boolean, skip: Int = 0, where: PromotionFilter): PromotionCollection

  recommendationGkAir(id: String!, locale: String, preview: Boolean): RecommendationGkAir

  recommendationGkAirCollection(limit: Int = 100, locale: String, order: [RecommendationGkAirOrder], preview: Boolean, skip: Int = 0, where: RecommendationGkAirFilter): RecommendationGkAirCollection

  richtext(id: String!, locale: String, preview: Boolean): Richtext

  richtextCollection(limit: Int = 100, locale: String, order: [RichtextOrder], preview: Boolean, skip: Int = 0, where: RichtextFilter): RichtextCollection

  seoAdvice(id: String!, locale: String, preview: Boolean): SeoAdvice

  seoAdviceCollection(limit: Int = 100, locale: String, order: [SeoAdviceOrder], preview: Boolean, skip: Int = 0, where: SeoAdviceFilter): SeoAdviceCollection

  seoContent(id: String!, locale: String, preview: Boolean): SeoContent

  seoContentCollection(limit: Int = 100, locale: String, order: [SeoContentOrder], preview: Boolean, skip: Int = 0, where: SeoContentFilter): SeoContentCollection

  sizeTable(id: String!, locale: String, preview: Boolean): SizeTable

  sizeTableCollection(limit: Int = 100, locale: String, order: [SizeTableOrder], preview: Boolean, skip: Int = 0, where: SizeTableFilter): SizeTableCollection

  survey(id: String!, locale: String, preview: Boolean): Survey

  surveyCollection(limit: Int = 100, locale: String, order: [SurveyOrder], preview: Boolean, skip: Int = 0, where: SurveyFilter): SurveyCollection

  tableOfContents(id: String!, locale: String, preview: Boolean): TableOfContents

  tableOfContentsCollection(limit: Int = 100, locale: String, order: [TableOfContentsOrder], preview: Boolean, skip: Int = 0, where: TableOfContentsFilter): TableOfContentsCollection

  tip(id: String!, locale: String, preview: Boolean): Tip

  tipCollection(limit: Int = 100, locale: String, order: [TipOrder], preview: Boolean, skip: Int = 0, where: TipFilter): TipCollection

  topLevelTrigger(id: String!, locale: String, preview: Boolean): TopLevelTrigger

  topLevelTriggerCollection(limit: Int = 100, locale: String, order: [TopLevelTriggerOrder], preview: Boolean, skip: Int = 0, where: TopLevelTriggerFilter): TopLevelTriggerCollection

  video(id: String!, locale: String, preview: Boolean): Video

  videoCollection(limit: Int = 100, locale: String, order: [VideoOrder], preview: Boolean, skip: Int = 0, where: VideoFilter): VideoCollection
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/recommendationGkAir)
"""
type RecommendationGkAir implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  gkAirService(locale: String, preview: Boolean, where: GkAirServiceFilter): GkAirService

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): RecommendationGkAirLinkingCollections

  outputId(locale: String): String

  recommendationType(locale: String): String

  sys: Sys!
}

type RecommendationGkAirCollection {
  items: [RecommendationGkAir]!

  limit: Int!

  skip: Int!

  total: Int!
}

input RecommendationGkAirFilter {
  AND: [RecommendationGkAirFilter]

  OR: [RecommendationGkAirFilter]

  contentfulMetadata: ContentfulMetadataFilter

  gkAirService: cfGkAirServiceNestedFilter

  gkAirService_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  outputId: String

  outputId_contains: String

  outputId_exists: Boolean

  outputId_in: [String]

  outputId_not: String

  outputId_not_contains: String

  outputId_not_in: [String]

  recommendationType: String

  recommendationType_contains: String

  recommendationType_exists: Boolean

  recommendationType_in: [String]

  recommendationType_not: String

  recommendationType_not_contains: String

  recommendationType_not_in: [String]

  sys: SysFilter
}

type RecommendationGkAirLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  homepageCreationLCollection(limit: Int = 100, locale: String, order: [RecommendationGkAirLinkingCollectionsHomepageCreationLCollectionOrder], preview: Boolean, skip: Int = 0): HomepageCreationLCollection

  homepageHeineV2Collection(limit: Int = 100, locale: String, order: [RecommendationGkAirLinkingCollectionsHomepageHeineV2CollectionOrder], preview: Boolean, skip: Int = 0): HomepageHeineV2Collection

  homepageSheegoCollection(limit: Int = 100, locale: String, order: [RecommendationGkAirLinkingCollectionsHomepageSheegoCollectionOrder], preview: Boolean, skip: Int = 0): HomepageSheegoCollection

  homepageSiehAnCollection(limit: Int = 100, locale: String, order: [RecommendationGkAirLinkingCollectionsHomepageSiehAnCollectionOrder], preview: Boolean, skip: Int = 0): HomepageSiehAnCollection

  homepageSiehAnV2Collection(limit: Int = 100, locale: String, order: [RecommendationGkAirLinkingCollectionsHomepageSiehAnV2CollectionOrder], preview: Boolean, skip: Int = 0): HomepageSiehAnV2Collection

  homepageWittV2Collection(limit: Int = 100, locale: String, order: [RecommendationGkAirLinkingCollectionsHomepageWittV2CollectionOrder], preview: Boolean, skip: Int = 0): HomepageWittV2Collection

  inspirationItemCollection(limit: Int = 100, locale: String, order: [RecommendationGkAirLinkingCollectionsInspirationItemCollectionOrder], preview: Boolean, skip: Int = 0): InspirationItemCollection

  outfitItemCollection(limit: Int = 100, locale: String, order: [RecommendationGkAirLinkingCollectionsOutfitItemCollectionOrder], preview: Boolean, skip: Int = 0): OutfitItemCollection

  plpCollection(limit: Int = 100, locale: String, order: [RecommendationGkAirLinkingCollectionsPlpCollectionOrder], preview: Boolean, skip: Int = 0): PlpCollection
}

enum RecommendationGkAirLinkingCollectionsHomepageCreationLCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum RecommendationGkAirLinkingCollectionsHomepageHeineV2CollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  promotionGridTitle_ASC

  promotionGridTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  titleTextCategories_ASC

  titleTextCategories_DESC
}

enum RecommendationGkAirLinkingCollectionsHomepageSheegoCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  optionalPromotionBarTitle_ASC

  optionalPromotionBarTitle_DESC

  promotionBar2Title_ASC

  promotionBar2Title_DESC

  promotionBar3Title_ASC

  promotionBar3Title_DESC

  promotionBarTitle_ASC

  promotionBarTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum RecommendationGkAirLinkingCollectionsHomepageSiehAnCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum RecommendationGkAirLinkingCollectionsHomepageSiehAnV2CollectionOrder {
  brandFormulaPart1_ASC

  brandFormulaPart1_DESC

  brandFormulaPart2_ASC

  brandFormulaPart2_DESC

  brandFormulaResult_ASC

  brandFormulaResult_DESC

  heroDoodles_ASC

  heroDoodles_DESC

  internalTitle_ASC

  internalTitle_DESC

  prioDoodles1_ASC

  prioDoodles1_DESC

  prioDoodles2_ASC

  prioDoodles2_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  titleAssortmentPromotions_ASC

  titleAssortmentPromotions_DESC

  titleCards_ASC

  titleCards_DESC

  titleIconPromotions_ASC

  titleIconPromotions_DESC
}

enum RecommendationGkAirLinkingCollectionsHomepageWittV2CollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum RecommendationGkAirLinkingCollectionsInspirationItemCollectionOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum RecommendationGkAirLinkingCollectionsOutfitItemCollectionOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum RecommendationGkAirLinkingCollectionsPlpCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum RecommendationGkAirOrder {
  internalTitle_ASC

  internalTitle_DESC

  outputId_ASC

  outputId_DESC

  recommendationType_ASC

  recommendationType_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

interface ResourceLink {
  sys: ResourceSys!
}

type ResourceSys {
  linkType: String!

  urn: String!
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/richtext)
"""
type Richtext implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): RichtextLinkingCollections

  richtext(locale: String): RichtextRichtext

  sys: Sys!

  title(locale: String): String
}

type RichtextCollection {
  items: [Richtext]!

  limit: Int!

  skip: Int!

  total: Int!
}

input RichtextFilter {
  AND: [RichtextFilter]

  OR: [RichtextFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  richtext_contains: String

  richtext_exists: Boolean

  richtext_not_contains: String

  sys: SysFilter

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

type RichtextLinkingCollections {
  accordionCollection(limit: Int = 100, locale: String, order: [RichtextLinkingCollectionsAccordionCollectionOrder], preview: Boolean, skip: Int = 0): AccordionCollection

  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  plpCollection(limit: Int = 100, locale: String, order: [RichtextLinkingCollectionsPlpCollectionOrder], preview: Boolean, skip: Int = 0): PlpCollection

  sizeTableCollection(limit: Int = 100, locale: String, order: [RichtextLinkingCollectionsSizeTableCollectionOrder], preview: Boolean, skip: Int = 0): SizeTableCollection
}

enum RichtextLinkingCollectionsAccordionCollectionOrder {
  defaultIndex_ASC

  defaultIndex_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum RichtextLinkingCollectionsPlpCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum RichtextLinkingCollectionsSizeTableCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  measuringHeadline_ASC

  measuringHeadline_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum RichtextOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

type RichtextRichtext {
  json: JSON!

  links: RichtextRichtextLinks!
}

type RichtextRichtextAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type RichtextRichtextEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type RichtextRichtextLinks {
  assets: RichtextRichtextAssets!

  entries: RichtextRichtextEntries!

  resources: RichtextRichtextResources!
}

type RichtextRichtextResources {
  block: [RichtextRichtextResourcesBlock!]!

  hyperlink: [RichtextRichtextResourcesHyperlink!]!

  inline: [RichtextRichtextResourcesInline!]!
}

type RichtextRichtextResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type RichtextRichtextResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type RichtextRichtextResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/seoAdvice)
"""
type SeoAdvice implements Entry & _Node {
  _id: ID!

  content(locale: String): SeoAdviceContent

  contentfulMetadata: ContentfulMetadata!

  image(locale: String, preview: Boolean): Asset

  imageCloudinary(locale: String): JSON

  internalTitle(locale: String): String

  link(locale: String, preview: Boolean): SeoAdviceLink

  linkedFrom(allowedLocales: [String]): SeoAdviceLinkingCollections

  sys: Sys!

  title(locale: String): String
}

type SeoAdviceCollection {
  items: [SeoAdvice]!

  limit: Int!

  skip: Int!

  total: Int!
}

type SeoAdviceContent {
  json: JSON!

  links: SeoAdviceContentLinks!
}

type SeoAdviceContentAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type SeoAdviceContentEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type SeoAdviceContentLinks {
  assets: SeoAdviceContentAssets!

  entries: SeoAdviceContentEntries!

  resources: SeoAdviceContentResources!
}

type SeoAdviceContentResources {
  block: [SeoAdviceContentResourcesBlock!]!

  hyperlink: [SeoAdviceContentResourcesHyperlink!]!

  inline: [SeoAdviceContentResourcesInline!]!
}

type SeoAdviceContentResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type SeoAdviceContentResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type SeoAdviceContentResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

input SeoAdviceFilter {
  AND: [SeoAdviceFilter]

  OR: [SeoAdviceFilter]

  content_contains: String

  content_exists: Boolean

  content_not_contains: String

  contentfulMetadata: ContentfulMetadataFilter

  imageCloudinary_exists: Boolean

  image_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  link_exists: Boolean

  sys: SysFilter

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

union SeoAdviceLink = AnchorLink|ExternalLink|InternalLink

type SeoAdviceLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  seoContentCollection(limit: Int = 100, locale: String, order: [SeoAdviceLinkingCollectionsSeoContentCollectionOrder], preview: Boolean, skip: Int = 0): SeoContentCollection
}

enum SeoAdviceLinkingCollectionsSeoContentCollectionOrder {
  automaticShopname_ASC

  automaticShopname_DESC

  categoryTitle_ASC

  categoryTitle_DESC

  description_ASC

  description_DESC

  indexDirective_ASC

  indexDirective_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

enum SeoAdviceOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/seoContent)
"""
type SeoContent implements Entry & _Node {
  _id: ID!

  automaticShopname(locale: String): String

  bottomContent(locale: String): SeoContentBottomContent

  categoryTitle(locale: String): String

  contentfulMetadata: ContentfulMetadata!

  description(locale: String): String

  indexDirective(locale: String): String

  linkedFrom(allowedLocales: [String]): SeoContentLinkingCollections

  onsiteContent(locale: String, preview: Boolean): SeoContentOnsiteContent

  seoContentList(locale: String): [String]

  slug(locale: String): String

  sys: Sys!

  title(locale: String): String
}

type SeoContentBottomContent {
  json: JSON!

  links: SeoContentBottomContentLinks!
}

type SeoContentBottomContentAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type SeoContentBottomContentEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type SeoContentBottomContentLinks {
  assets: SeoContentBottomContentAssets!

  entries: SeoContentBottomContentEntries!

  resources: SeoContentBottomContentResources!
}

type SeoContentBottomContentResources {
  block: [SeoContentBottomContentResourcesBlock!]!

  hyperlink: [SeoContentBottomContentResourcesHyperlink!]!

  inline: [SeoContentBottomContentResourcesInline!]!
}

type SeoContentBottomContentResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type SeoContentBottomContentResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type SeoContentBottomContentResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

type SeoContentCollection {
  items: [SeoContent]!

  limit: Int!

  skip: Int!

  total: Int!
}

input SeoContentFilter {
  AND: [SeoContentFilter]

  OR: [SeoContentFilter]

  automaticShopname: String

  automaticShopname_contains: String

  automaticShopname_exists: Boolean

  automaticShopname_in: [String]

  automaticShopname_not: String

  automaticShopname_not_contains: String

  automaticShopname_not_in: [String]

  bottomContent_contains: String

  bottomContent_exists: Boolean

  bottomContent_not_contains: String

  categoryTitle: String

  categoryTitle_contains: String

  categoryTitle_exists: Boolean

  categoryTitle_in: [String]

  categoryTitle_not: String

  categoryTitle_not_contains: String

  categoryTitle_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  description: String

  description_contains: String

  description_exists: Boolean

  description_in: [String]

  description_not: String

  description_not_contains: String

  description_not_in: [String]

  indexDirective: String

  indexDirective_contains: String

  indexDirective_exists: Boolean

  indexDirective_in: [String]

  indexDirective_not: String

  indexDirective_not_contains: String

  indexDirective_not_in: [String]

  onsiteContent_exists: Boolean

  seoContentList_contains_all: [String]

  seoContentList_contains_none: [String]

  seoContentList_contains_some: [String]

  seoContentList_exists: Boolean

  slug: String

  slug_contains: String

  slug_exists: Boolean

  slug_in: [String]

  slug_not: String

  slug_not_contains: String

  slug_not_in: [String]

  sys: SysFilter

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

type SeoContentLinkingCollections {
  contentPageCollection(limit: Int = 100, locale: String, order: [SeoContentLinkingCollectionsContentPageCollectionOrder], preview: Boolean, skip: Int = 0): ContentPageCollection

  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  homepageCreationLCollection(limit: Int = 100, locale: String, order: [SeoContentLinkingCollectionsHomepageCreationLCollectionOrder], preview: Boolean, skip: Int = 0): HomepageCreationLCollection

  homepageHeineV2Collection(limit: Int = 100, locale: String, order: [SeoContentLinkingCollectionsHomepageHeineV2CollectionOrder], preview: Boolean, skip: Int = 0): HomepageHeineV2Collection

  homepageSheegoCollection(limit: Int = 100, locale: String, order: [SeoContentLinkingCollectionsHomepageSheegoCollectionOrder], preview: Boolean, skip: Int = 0): HomepageSheegoCollection

  homepageSiehAnCollection(limit: Int = 100, locale: String, order: [SeoContentLinkingCollectionsHomepageSiehAnCollectionOrder], preview: Boolean, skip: Int = 0): HomepageSiehAnCollection

  homepageSiehAnV2Collection(limit: Int = 100, locale: String, order: [SeoContentLinkingCollectionsHomepageSiehAnV2CollectionOrder], preview: Boolean, skip: Int = 0): HomepageSiehAnV2Collection

  homepageWittV2Collection(limit: Int = 100, locale: String, order: [SeoContentLinkingCollectionsHomepageWittV2CollectionOrder], preview: Boolean, skip: Int = 0): HomepageWittV2Collection

  inspirationPageCollection(limit: Int = 100, locale: String, order: [SeoContentLinkingCollectionsInspirationPageCollectionOrder], preview: Boolean, skip: Int = 0): InspirationPageCollection

  outfitPageCollection(limit: Int = 100, locale: String, order: [SeoContentLinkingCollectionsOutfitPageCollectionOrder], preview: Boolean, skip: Int = 0): OutfitPageCollection
}

enum SeoContentLinkingCollectionsContentPageCollectionOrder {
  horizontalAlignment_ASC

  horizontalAlignment_DESC

  name_ASC

  name_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum SeoContentLinkingCollectionsHomepageCreationLCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum SeoContentLinkingCollectionsHomepageHeineV2CollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  promotionGridTitle_ASC

  promotionGridTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  titleTextCategories_ASC

  titleTextCategories_DESC
}

enum SeoContentLinkingCollectionsHomepageSheegoCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  optionalPromotionBarTitle_ASC

  optionalPromotionBarTitle_DESC

  promotionBar2Title_ASC

  promotionBar2Title_DESC

  promotionBar3Title_ASC

  promotionBar3Title_DESC

  promotionBarTitle_ASC

  promotionBarTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum SeoContentLinkingCollectionsHomepageSiehAnCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum SeoContentLinkingCollectionsHomepageSiehAnV2CollectionOrder {
  brandFormulaPart1_ASC

  brandFormulaPart1_DESC

  brandFormulaPart2_ASC

  brandFormulaPart2_DESC

  brandFormulaResult_ASC

  brandFormulaResult_DESC

  heroDoodles_ASC

  heroDoodles_DESC

  internalTitle_ASC

  internalTitle_DESC

  prioDoodles1_ASC

  prioDoodles1_DESC

  prioDoodles2_ASC

  prioDoodles2_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  titleAssortmentPromotions_ASC

  titleAssortmentPromotions_DESC

  titleCards_ASC

  titleCards_DESC

  titleIconPromotions_ASC

  titleIconPromotions_DESC
}

enum SeoContentLinkingCollectionsHomepageWittV2CollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum SeoContentLinkingCollectionsInspirationPageCollectionOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  overviewHeadline_ASC

  overviewHeadline_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  transitionalHeadline_ASC

  transitionalHeadline_DESC
}

enum SeoContentLinkingCollectionsOutfitPageCollectionOrder {
  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  moreOutfitsHeadline_ASC

  moreOutfitsHeadline_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

union SeoContentOnsiteContent = None|SeoAdvice

enum SeoContentOrder {
  automaticShopname_ASC

  automaticShopname_DESC

  categoryTitle_ASC

  categoryTitle_DESC

  description_ASC

  description_DESC

  indexDirective_ASC

  indexDirective_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/sizeTable)
"""
type SizeTable implements Entry & _Node {
  _id: ID!

  companyOfOrigin(locale: String): [String]

  content(locale: String): SizeTableContent

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): SizeTableLinkingCollections

  measuringAsset(locale: String, preview: Boolean): Asset

  measuringAssetCloudinary(locale: String): JSON

  measuringHeadline(locale: String): String

  measuringIntroductionText(locale: String): String

  measuringItemsCollection(limit: Int = 100, locale: String, order: [SizeTableMeasuringItemsCollectionOrder], preview: Boolean, skip: Int = 0, where: RichtextFilter): SizeTableMeasuringItemsCollection

  measuringTip(locale: String, preview: Boolean, where: TipFilter): Tip

  mkz(locale: String): [String]

  sys: Sys!
}

type SizeTableCollection {
  items: [SizeTable]!

  limit: Int!

  skip: Int!

  total: Int!
}

type SizeTableContent {
  json: JSON!

  links: SizeTableContentLinks!
}

type SizeTableContentAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type SizeTableContentEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type SizeTableContentLinks {
  assets: SizeTableContentAssets!

  entries: SizeTableContentEntries!

  resources: SizeTableContentResources!
}

type SizeTableContentResources {
  block: [SizeTableContentResourcesBlock!]!

  hyperlink: [SizeTableContentResourcesHyperlink!]!

  inline: [SizeTableContentResourcesInline!]!
}

type SizeTableContentResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type SizeTableContentResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type SizeTableContentResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

input SizeTableFilter {
  AND: [SizeTableFilter]

  OR: [SizeTableFilter]

  companyOfOrigin_contains_all: [String]

  companyOfOrigin_contains_none: [String]

  companyOfOrigin_contains_some: [String]

  companyOfOrigin_exists: Boolean

  content_contains: String

  content_exists: Boolean

  content_not_contains: String

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  measuringAssetCloudinary_exists: Boolean

  measuringAsset_exists: Boolean

  measuringHeadline: String

  measuringHeadline_contains: String

  measuringHeadline_exists: Boolean

  measuringHeadline_in: [String]

  measuringHeadline_not: String

  measuringHeadline_not_contains: String

  measuringHeadline_not_in: [String]

  measuringIntroductionText: String

  measuringIntroductionText_contains: String

  measuringIntroductionText_exists: Boolean

  measuringIntroductionText_in: [String]

  measuringIntroductionText_not: String

  measuringIntroductionText_not_contains: String

  measuringIntroductionText_not_in: [String]

  measuringItems: cfRichtextNestedFilter

  measuringItemsCollection_exists: Boolean

  measuringTip: cfTipNestedFilter

  measuringTip_exists: Boolean

  mkz_contains_all: [String]

  mkz_contains_none: [String]

  mkz_contains_some: [String]

  mkz_exists: Boolean

  sys: SysFilter
}

type SizeTableLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

type SizeTableMeasuringItemsCollection {
  items: [Richtext]!

  limit: Int!

  skip: Int!

  total: Int!
}

enum SizeTableMeasuringItemsCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

enum SizeTableOrder {
  internalTitle_ASC

  internalTitle_DESC

  measuringHeadline_ASC

  measuringHeadline_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/survey)
"""
type Survey implements Entry & _Node {
  _id: ID!

  buttonLabel(locale: String): String

  contentfulMetadata: ContentfulMetadata!

  description(locale: String): SurveyDescription

  headline(locale: String): String

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): SurveyLinkingCollections

  pages(locale: String): [String]

  sys: Sys!

  url(locale: String): String
}

type SurveyCollection {
  items: [Survey]!

  limit: Int!

  skip: Int!

  total: Int!
}

type SurveyDescription {
  json: JSON!

  links: SurveyDescriptionLinks!
}

type SurveyDescriptionAssets {
  block: [Asset]!

  hyperlink: [Asset]!
}

type SurveyDescriptionEntries {
  block: [Entry]!

  hyperlink: [Entry]!

  inline: [Entry]!
}

type SurveyDescriptionLinks {
  assets: SurveyDescriptionAssets!

  entries: SurveyDescriptionEntries!

  resources: SurveyDescriptionResources!
}

type SurveyDescriptionResources {
  block: [SurveyDescriptionResourcesBlock!]!

  hyperlink: [SurveyDescriptionResourcesHyperlink!]!

  inline: [SurveyDescriptionResourcesInline!]!
}

type SurveyDescriptionResourcesBlock implements ResourceLink {
  sys: ResourceSys!
}

type SurveyDescriptionResourcesHyperlink implements ResourceLink {
  sys: ResourceSys!
}

type SurveyDescriptionResourcesInline implements ResourceLink {
  sys: ResourceSys!
}

input SurveyFilter {
  AND: [SurveyFilter]

  OR: [SurveyFilter]

  buttonLabel: String

  buttonLabel_contains: String

  buttonLabel_exists: Boolean

  buttonLabel_in: [String]

  buttonLabel_not: String

  buttonLabel_not_contains: String

  buttonLabel_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  description_contains: String

  description_exists: Boolean

  description_not_contains: String

  headline: String

  headline_contains: String

  headline_exists: Boolean

  headline_in: [String]

  headline_not: String

  headline_not_contains: String

  headline_not_in: [String]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  pages_contains_all: [String]

  pages_contains_none: [String]

  pages_contains_some: [String]

  pages_exists: Boolean

  sys: SysFilter

  url: String

  url_contains: String

  url_exists: Boolean

  url_in: [String]

  url_not: String

  url_not_contains: String

  url_not_in: [String]
}

type SurveyLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum SurveyOrder {
  buttonLabel_ASC

  buttonLabel_DESC

  headline_ASC

  headline_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  url_ASC

  url_DESC
}

type Sys {
  environmentId: String!

  firstPublishedAt: DateTime

  id: String!

  """
  The locale that was requested.
  """
  locale: String

  publishedAt: DateTime

  publishedVersion: Int

  spaceId: String!
}

input SysFilter {
  firstPublishedAt: DateTime

  firstPublishedAt_exists: Boolean

  firstPublishedAt_gt: DateTime

  firstPublishedAt_gte: DateTime

  firstPublishedAt_in: [DateTime]

  firstPublishedAt_lt: DateTime

  firstPublishedAt_lte: DateTime

  firstPublishedAt_not: DateTime

  firstPublishedAt_not_in: [DateTime]

  id: String

  id_contains: String

  id_exists: Boolean

  id_in: [String]

  id_not: String

  id_not_contains: String

  id_not_in: [String]

  publishedAt: DateTime

  publishedAt_exists: Boolean

  publishedAt_gt: DateTime

  publishedAt_gte: DateTime

  publishedAt_in: [DateTime]

  publishedAt_lt: DateTime

  publishedAt_lte: DateTime

  publishedAt_not: DateTime

  publishedAt_not_in: [DateTime]

  publishedVersion: Float

  publishedVersion_exists: Boolean

  publishedVersion_gt: Float

  publishedVersion_gte: Float

  publishedVersion_in: [Float]

  publishedVersion_lt: Float

  publishedVersion_lte: Float

  publishedVersion_not: Float

  publishedVersion_not_in: [Float]
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/tableOfContents)
"""
type TableOfContents implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  levelsToShow(locale: String): Int

  linkedFrom(allowedLocales: [String]): TableOfContentsLinkingCollections

  sys: Sys!

  title(locale: String): String
}

type TableOfContentsCollection {
  items: [TableOfContents]!

  limit: Int!

  skip: Int!

  total: Int!
}

input TableOfContentsFilter {
  AND: [TableOfContentsFilter]

  OR: [TableOfContentsFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  levelsToShow: Int

  levelsToShow_exists: Boolean

  levelsToShow_gt: Int

  levelsToShow_gte: Int

  levelsToShow_in: [Int]

  levelsToShow_lt: Int

  levelsToShow_lte: Int

  levelsToShow_not: Int

  levelsToShow_not_in: [Int]

  sys: SysFilter

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

type TableOfContentsLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum TableOfContentsOrder {
  internalTitle_ASC

  internalTitle_DESC

  levelsToShow_ASC

  levelsToShow_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  title_ASC

  title_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/tip)
"""
type Tip implements Entry & _Node {
  _id: ID!

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): TipLinkingCollections

  sys: Sys!

  tipText(locale: String): String

  title(locale: String): String
}

type TipCollection {
  items: [Tip]!

  limit: Int!

  skip: Int!

  total: Int!
}

input TipFilter {
  AND: [TipFilter]

  OR: [TipFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter

  tipText: String

  tipText_contains: String

  tipText_exists: Boolean

  tipText_in: [String]

  tipText_not: String

  tipText_not_contains: String

  tipText_not_in: [String]

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

type TipLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  sizeTableCollection(limit: Int = 100, locale: String, order: [TipLinkingCollectionsSizeTableCollectionOrder], preview: Boolean, skip: Int = 0): SizeTableCollection
}

enum TipLinkingCollectionsSizeTableCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  measuringHeadline_ASC

  measuringHeadline_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum TipOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  tipText_ASC

  tipText_DESC

  title_ASC

  title_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/topLevelTrigger)
"""
type TopLevelTrigger implements Entry & _Node {
  _id: ID!

  app(locale: String): String

  campaignTrigger(locale: String): String

  contentfulMetadata: ContentfulMetadata!

  customerHasShippingCostsFlat(locale: String): Boolean

  customerIsNewsletterRecipient(locale: String): Boolean

  deviceType(locale: String): [String]

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): TopLevelTriggerLinkingCollections

  loginState(locale: String): Boolean

  optimizelyTrigger(locale: String): String

  sys: Sys!
}

type TopLevelTriggerCollection {
  items: [TopLevelTrigger]!

  limit: Int!

  skip: Int!

  total: Int!
}

input TopLevelTriggerFilter {
  AND: [TopLevelTriggerFilter]

  OR: [TopLevelTriggerFilter]

  app: String

  app_contains: String

  app_exists: Boolean

  app_in: [String]

  app_not: String

  app_not_contains: String

  app_not_in: [String]

  campaignTrigger: String

  campaignTrigger_contains: String

  campaignTrigger_exists: Boolean

  campaignTrigger_in: [String]

  campaignTrigger_not: String

  campaignTrigger_not_contains: String

  campaignTrigger_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  customerHasShippingCostsFlat: Boolean

  customerHasShippingCostsFlat_exists: Boolean

  customerHasShippingCostsFlat_not: Boolean

  customerIsNewsletterRecipient: Boolean

  customerIsNewsletterRecipient_exists: Boolean

  customerIsNewsletterRecipient_not: Boolean

  deviceType_contains_all: [String]

  deviceType_contains_none: [String]

  deviceType_contains_some: [String]

  deviceType_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  loginState: Boolean

  loginState_exists: Boolean

  loginState_not: Boolean

  optimizelyTrigger: String

  optimizelyTrigger_contains: String

  optimizelyTrigger_exists: Boolean

  optimizelyTrigger_in: [String]

  optimizelyTrigger_not: String

  optimizelyTrigger_not_contains: String

  optimizelyTrigger_not_in: [String]

  sys: SysFilter
}

type TopLevelTriggerLinkingCollections {
  bannerCollection(limit: Int = 100, locale: String, order: [TopLevelTriggerLinkingCollectionsBannerCollectionOrder], preview: Boolean, skip: Int = 0): BannerCollection

  contentPageCollection(limit: Int = 100, locale: String, order: [TopLevelTriggerLinkingCollectionsContentPageCollectionOrder], preview: Boolean, skip: Int = 0): ContentPageCollection

  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection

  homepageCreationLCollection(limit: Int = 100, locale: String, order: [TopLevelTriggerLinkingCollectionsHomepageCreationLCollectionOrder], preview: Boolean, skip: Int = 0): HomepageCreationLCollection

  homepageHeineV2Collection(limit: Int = 100, locale: String, order: [TopLevelTriggerLinkingCollectionsHomepageHeineV2CollectionOrder], preview: Boolean, skip: Int = 0): HomepageHeineV2Collection

  homepageSheegoCollection(limit: Int = 100, locale: String, order: [TopLevelTriggerLinkingCollectionsHomepageSheegoCollectionOrder], preview: Boolean, skip: Int = 0): HomepageSheegoCollection

  homepageSiehAnCollection(limit: Int = 100, locale: String, order: [TopLevelTriggerLinkingCollectionsHomepageSiehAnCollectionOrder], preview: Boolean, skip: Int = 0): HomepageSiehAnCollection

  homepageSiehAnV2Collection(limit: Int = 100, locale: String, order: [TopLevelTriggerLinkingCollectionsHomepageSiehAnV2CollectionOrder], preview: Boolean, skip: Int = 0): HomepageSiehAnV2Collection

  homepageWittV2Collection(limit: Int = 100, locale: String, order: [TopLevelTriggerLinkingCollectionsHomepageWittV2CollectionOrder], preview: Boolean, skip: Int = 0): HomepageWittV2Collection

  pdpCollection(limit: Int = 100, locale: String, order: [TopLevelTriggerLinkingCollectionsPdpCollectionOrder], preview: Boolean, skip: Int = 0): PdpCollection

  plpCollection(limit: Int = 100, locale: String, order: [TopLevelTriggerLinkingCollectionsPlpCollectionOrder], preview: Boolean, skip: Int = 0): PlpCollection
}

enum TopLevelTriggerLinkingCollectionsBannerCollectionOrder {
  countdownStart_ASC

  countdownStart_DESC

  countdown_ASC

  countdown_DESC

  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  trackingName_ASC

  trackingName_DESC
}

enum TopLevelTriggerLinkingCollectionsContentPageCollectionOrder {
  horizontalAlignment_ASC

  horizontalAlignment_DESC

  name_ASC

  name_DESC

  slug_ASC

  slug_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum TopLevelTriggerLinkingCollectionsHomepageCreationLCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum TopLevelTriggerLinkingCollectionsHomepageHeineV2CollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  promotionGridTitle_ASC

  promotionGridTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  titleTextCategories_ASC

  titleTextCategories_DESC
}

enum TopLevelTriggerLinkingCollectionsHomepageSheegoCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  optionalPromotionBarTitle_ASC

  optionalPromotionBarTitle_DESC

  promotionBar2Title_ASC

  promotionBar2Title_DESC

  promotionBar3Title_ASC

  promotionBar3Title_DESC

  promotionBarTitle_ASC

  promotionBarTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum TopLevelTriggerLinkingCollectionsHomepageSiehAnCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum TopLevelTriggerLinkingCollectionsHomepageSiehAnV2CollectionOrder {
  brandFormulaPart1_ASC

  brandFormulaPart1_DESC

  brandFormulaPart2_ASC

  brandFormulaPart2_DESC

  brandFormulaResult_ASC

  brandFormulaResult_DESC

  heroDoodles_ASC

  heroDoodles_DESC

  internalTitle_ASC

  internalTitle_DESC

  prioDoodles1_ASC

  prioDoodles1_DESC

  prioDoodles2_ASC

  prioDoodles2_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  titleAssortmentPromotions_ASC

  titleAssortmentPromotions_DESC

  titleCards_ASC

  titleCards_DESC

  titleIconPromotions_ASC

  titleIconPromotions_DESC
}

enum TopLevelTriggerLinkingCollectionsHomepageWittV2CollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum TopLevelTriggerLinkingCollectionsPdpCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum TopLevelTriggerLinkingCollectionsPlpCollectionOrder {
  internalTitle_ASC

  internalTitle_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

enum TopLevelTriggerOrder {
  app_ASC

  app_DESC

  campaignTrigger_ASC

  campaignTrigger_DESC

  customerHasShippingCostsFlat_ASC

  customerHasShippingCostsFlat_DESC

  customerIsNewsletterRecipient_ASC

  customerIsNewsletterRecipient_DESC

  internalTitle_ASC

  internalTitle_DESC

  loginState_ASC

  loginState_DESC

  optimizelyTrigger_ASC

  optimizelyTrigger_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC
}

"""
[See type definition](https://app.contentful.com/spaces/joape0egh3h4/content_types/video)
"""
type Video implements Entry & _Node {
  _id: ID!

  cloudinary(locale: String): JSON

  contentfulMetadata: ContentfulMetadata!

  internalTitle(locale: String): String

  linkedFrom(allowedLocales: [String]): VideoLinkingCollections

  responsive(locale: String): Boolean

  sys: Sys!

  url(locale: String): String
}

type VideoCollection {
  items: [Video]!

  limit: Int!

  skip: Int!

  total: Int!
}

input VideoFilter {
  AND: [VideoFilter]

  OR: [VideoFilter]

  cloudinary_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  responsive: Boolean

  responsive_exists: Boolean

  responsive_not: Boolean

  sys: SysFilter

  url: String

  url_contains: String

  url_exists: Boolean

  url_in: [String]

  url_not: String

  url_not_contains: String

  url_not_in: [String]
}

type VideoLinkingCollections {
  entryCollection(limit: Int = 100, locale: String, preview: Boolean, skip: Int = 0): EntryCollection
}

enum VideoOrder {
  internalTitle_ASC

  internalTitle_DESC

  responsive_ASC

  responsive_DESC

  sys_firstPublishedAt_ASC

  sys_firstPublishedAt_DESC

  sys_id_ASC

  sys_id_DESC

  sys_publishedAt_ASC

  sys_publishedAt_DESC

  sys_publishedVersion_ASC

  sys_publishedVersion_DESC

  url_ASC

  url_DESC
}

interface _Node {
  _id: ID!
}

input cfAnchorLinkNestedFilter {
  AND: [cfAnchorLinkNestedFilter]

  OR: [cfAnchorLinkNestedFilter]

  anchor: String

  anchor_contains: String

  anchor_exists: Boolean

  anchor_in: [String]

  anchor_not: String

  anchor_not_contains: String

  anchor_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

input cfAssetSetNestedFilter {
  AND: [cfAssetSetNestedFilter]

  OR: [cfAssetSetNestedFilter]

  alt: String

  alt_contains: String

  alt_exists: Boolean

  alt_in: [String]

  alt_not: String

  alt_not_contains: String

  alt_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  defaultImageCloudinary_exists: Boolean

  defaultImage_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  ratio1to1Cloudinary_exists: Boolean

  ratio1to1_exists: Boolean

  ratio2to1Cloudinary_exists: Boolean

  ratio2to1_exists: Boolean

  ratio3to1Cloudinary_exists: Boolean

  ratio3to1_exists: Boolean

  ratio3to4Cloudinary_exists: Boolean

  ratio3to4_exists: Boolean

  ratio4to3Cloudinary_exists: Boolean

  ratio4to3_exists: Boolean

  ratio7to1Cloudinary_exists: Boolean

  ratio7to1_exists: Boolean

  sys: SysFilter
}

input cfBannerNestedFilter {
  AND: [cfBannerNestedFilter]

  OR: [cfBannerNestedFilter]

  bannerImageLink_exists: Boolean

  bannerImage_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  countdown: DateTime

  countdownStart: DateTime

  countdownStart_exists: Boolean

  countdownStart_gt: DateTime

  countdownStart_gte: DateTime

  countdownStart_in: [DateTime]

  countdownStart_lt: DateTime

  countdownStart_lte: DateTime

  countdownStart_not: DateTime

  countdownStart_not_in: [DateTime]

  countdown_exists: Boolean

  countdown_gt: DateTime

  countdown_gte: DateTime

  countdown_in: [DateTime]

  countdown_lt: DateTime

  countdown_lte: DateTime

  countdown_not: DateTime

  countdown_not_in: [DateTime]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  layoutColor_exists: Boolean

  modalImageCloudinary_exists: Boolean

  modal_exists: Boolean

  pages_contains_all: [String]

  pages_contains_none: [String]

  pages_contains_some: [String]

  pages_exists: Boolean

  sys: SysFilter

  text_contains: String

  text_exists: Boolean

  text_not_contains: String

  trackingName: String

  trackingName_contains: String

  trackingName_exists: Boolean

  trackingName_in: [String]

  trackingName_not: String

  trackingName_not_contains: String

  trackingName_not_in: [String]

  trigger_exists: Boolean
}

input cfCategoryImageLinkNestedFilter {
  AND: [cfCategoryImageLinkNestedFilter]

  OR: [cfCategoryImageLinkNestedFilter]

  categoryId: Int

  categoryId_exists: Boolean

  categoryId_gt: Int

  categoryId_gte: Int

  categoryId_in: [Int]

  categoryId_lt: Int

  categoryId_lte: Int

  categoryId_not: Int

  categoryId_not_in: [Int]

  categoryImage: String

  categoryImage_contains: String

  categoryImage_exists: Boolean

  categoryImage_in: [String]

  categoryImage_not: String

  categoryImage_not_contains: String

  categoryImage_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter

  type: String

  type_contains: String

  type_exists: Boolean

  type_in: [String]

  type_not: String

  type_not_contains: String

  type_not_in: [String]
}

input cfCouponNestedFilter {
  AND: [cfCouponNestedFilter]

  OR: [cfCouponNestedFilter]

  code: String

  code2: String

  code2_contains: String

  code2_exists: Boolean

  code2_in: [String]

  code2_not: String

  code2_not_contains: String

  code2_not_in: [String]

  code_contains: String

  code_exists: Boolean

  code_in: [String]

  code_not: String

  code_not_contains: String

  code_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  descriptionText: String

  descriptionText_contains: String

  descriptionText_exists: Boolean

  descriptionText_in: [String]

  descriptionText_not: String

  descriptionText_not_contains: String

  descriptionText_not_in: [String]

  hint: String

  hint_contains: String

  hint_exists: Boolean

  hint_in: [String]

  hint_not: String

  hint_not_contains: String

  hint_not_in: [String]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  layoutColor_exists: Boolean

  promotionText_contains: String

  promotionText_exists: Boolean

  promotionText_not_contains: String

  showQrCode: Boolean

  showQrCode_exists: Boolean

  showQrCode_not: Boolean

  sys: SysFilter
}

input cfEccItemNestedFilter {
  AND: [cfEccItemNestedFilter]

  OR: [cfEccItemNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  eccNumber: Int

  eccNumber_exists: Boolean

  eccNumber_gt: Int

  eccNumber_gte: Int

  eccNumber_in: [Int]

  eccNumber_lt: Int

  eccNumber_lte: Int

  eccNumber_not: Int

  eccNumber_not_in: [Int]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  pageType: String

  pageType_contains: String

  pageType_exists: Boolean

  pageType_in: [String]

  pageType_not: String

  pageType_not_contains: String

  pageType_not_in: [String]

  referrer: String

  referrer_contains: String

  referrer_exists: Boolean

  referrer_in: [String]

  referrer_not: String

  referrer_not_contains: String

  referrer_not_in: [String]

  sys: SysFilter

  url: String

  url_contains: String

  url_exists: Boolean

  url_in: [String]

  url_not: String

  url_not_contains: String

  url_not_in: [String]
}

input cfFooterColumnNestedFilter {
  AND: [cfFooterColumnNestedFilter]

  OR: [cfFooterColumnNestedFilter]

  contentCollection_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  sys: SysFilter

  title: String

  titleLink_exists: Boolean

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

input cfGkAirOutputConfigNestedFilter {
  AND: [cfGkAirOutputConfigNestedFilter]

  OR: [cfGkAirOutputConfigNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  imageType: String

  imageType_contains: String

  imageType_exists: Boolean

  imageType_in: [String]

  imageType_not: String

  imageType_not_contains: String

  imageType_not_in: [String]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  outputId: String

  outputId_contains: String

  outputId_exists: Boolean

  outputId_in: [String]

  outputId_not: String

  outputId_not_contains: String

  outputId_not_in: [String]

  sys: SysFilter
}

input cfGkAirParameterNestedFilter {
  AND: [cfGkAirParameterNestedFilter]

  OR: [cfGkAirParameterNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  name: String

  name_contains: String

  name_exists: Boolean

  name_in: [String]

  name_not: String

  name_not_contains: String

  name_not_in: [String]

  sys: SysFilter

  value: String

  value_contains: String

  value_exists: Boolean

  value_in: [String]

  value_not: String

  value_not_contains: String

  value_not_in: [String]
}

input cfGkAirServiceNestedFilter {
  AND: [cfGkAirServiceNestedFilter]

  OR: [cfGkAirServiceNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  outputConfigCollection_exists: Boolean

  parametersCollection_exists: Boolean

  serviceId: String

  serviceId_contains: String

  serviceId_exists: Boolean

  serviceId_in: [String]

  serviceId_not: String

  serviceId_not_contains: String

  serviceId_not_in: [String]

  sys: SysFilter
}

input cfInspirationItemNestedFilter {
  AND: [cfInspirationItemNestedFilter]

  OR: [cfInspirationItemNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  headline: String

  headline_contains: String

  headline_exists: Boolean

  headline_in: [String]

  headline_not: String

  headline_not_contains: String

  headline_not_in: [String]

  image1Cloudinary_exists: Boolean

  image2Cloudinary_exists: Boolean

  image3Cloudinary_exists: Boolean

  imagesCollection_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  layoutColorPicker_exists: Boolean

  link_exists: Boolean

  recommendationGkAir_exists: Boolean

  sys: SysFilter

  text_contains: String

  text_exists: Boolean

  text_not_contains: String
}

input cfLayoutColumnNestedFilter {
  AND: [cfLayoutColumnNestedFilter]

  OR: [cfLayoutColumnNestedFilter]

  content_contains: String

  content_exists: Boolean

  content_not_contains: String

  contentfulMetadata: ContentfulMetadataFilter

  horizontalAlignment: String

  horizontalAlignment_contains: String

  horizontalAlignment_exists: Boolean

  horizontalAlignment_in: [String]

  horizontalAlignment_not: String

  horizontalAlignment_not_contains: String

  horizontalAlignment_not_in: [String]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter
}

input cfLocaleTextNestedFilter {
  AND: [cfLocaleTextNestedFilter]

  OR: [cfLocaleTextNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  defaultMessage: String

  defaultMessage_contains: String

  defaultMessage_exists: Boolean

  defaultMessage_in: [String]

  defaultMessage_not: String

  defaultMessage_not_contains: String

  defaultMessage_not_in: [String]

  description: String

  description_contains: String

  description_exists: Boolean

  description_in: [String]

  description_not: String

  description_not_contains: String

  description_not_in: [String]

  key: String

  key_contains: String

  key_exists: Boolean

  key_in: [String]

  key_not: String

  key_not_contains: String

  key_not_in: [String]

  localeLists_contains_all: [String]

  localeLists_contains_none: [String]

  localeLists_contains_some: [String]

  localeLists_exists: Boolean

  sys: SysFilter

  value: String

  value_contains: String

  value_exists: Boolean

  value_in: [String]

  value_not: String

  value_not_contains: String

  value_not_in: [String]
}

input cfModalNestedFilter {
  AND: [cfModalNestedFilter]

  OR: [cfModalNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  coupon_exists: Boolean

  headline: String

  headline_contains: String

  headline_exists: Boolean

  headline_in: [String]

  headline_not: String

  headline_not_contains: String

  headline_not_in: [String]

  id: String

  id_contains: String

  id_exists: Boolean

  id_in: [String]

  id_not: String

  id_not_contains: String

  id_not_in: [String]

  linkText: String

  linkText_contains: String

  linkText_exists: Boolean

  linkText_in: [String]

  linkText_not: String

  linkText_not_contains: String

  linkText_not_in: [String]

  primaryButtonLink_exists: Boolean

  richtext_contains: String

  richtext_exists: Boolean

  richtext_not_contains: String

  sys: SysFilter
}

input cfOutfitItemNestedFilter {
  AND: [cfOutfitItemNestedFilter]

  OR: [cfOutfitItemNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  description_contains: String

  description_exists: Boolean

  description_not_contains: String

  headline: String

  headline_contains: String

  headline_exists: Boolean

  headline_in: [String]

  headline_not: String

  headline_not_contains: String

  headline_not_in: [String]

  imageCloudinary_exists: Boolean

  image_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  recommendationGkAir_exists: Boolean

  sys: SysFilter
}

input cfOutfitPageNestedFilter {
  AND: [cfOutfitPageNestedFilter]

  OR: [cfOutfitPageNestedFilter]

  backlink_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  headline: String

  headline_contains: String

  headline_exists: Boolean

  headline_in: [String]

  headline_not: String

  headline_not_contains: String

  headline_not_in: [String]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  introductionText_contains: String

  introductionText_exists: Boolean

  introductionText_not_contains: String

  itemsCollection_exists: Boolean

  moreOutfitPagesCollection_exists: Boolean

  moreOutfitsHeadline: String

  moreOutfitsHeadline_contains: String

  moreOutfitsHeadline_exists: Boolean

  moreOutfitsHeadline_in: [String]

  moreOutfitsHeadline_not: String

  moreOutfitsHeadline_not_contains: String

  moreOutfitsHeadline_not_in: [String]

  seoContent_exists: Boolean

  slug: String

  slug_contains: String

  slug_exists: Boolean

  slug_in: [String]

  slug_not: String

  slug_not_contains: String

  slug_not_in: [String]

  sys: SysFilter
}

input cfPromotionNestedFilter {
  AND: [cfPromotionNestedFilter]

  OR: [cfPromotionNestedFilter]

  additionalLinksCollection_exists: Boolean

  assetSet_exists: Boolean

  callToAction: String

  callToAction_contains: String

  callToAction_exists: Boolean

  callToAction_in: [String]

  callToAction_not: String

  callToAction_not_contains: String

  callToAction_not_in: [String]

  categoryImageLink_exists: Boolean

  contentfulMetadata: ContentfulMetadataFilter

  eyecatcherDesign: String

  eyecatcherDesign_contains: String

  eyecatcherDesign_exists: Boolean

  eyecatcherDesign_in: [String]

  eyecatcherDesign_not: String

  eyecatcherDesign_not_contains: String

  eyecatcherDesign_not_in: [String]

  eyecatcherRichText_contains: String

  eyecatcherRichText_exists: Boolean

  eyecatcherRichText_not_contains: String

  eyecatcherType: String

  eyecatcherType_contains: String

  eyecatcherType_exists: Boolean

  eyecatcherType_in: [String]

  eyecatcherType_not: String

  eyecatcherType_not_contains: String

  eyecatcherType_not_in: [String]

  imageCloudinary_exists: Boolean

  image_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  layoutColorPicker_exists: Boolean

  link_exists: Boolean

  pinnedProductIDs_contains_all: [String]

  pinnedProductIDs_contains_none: [String]

  pinnedProductIDs_contains_some: [String]

  pinnedProductIDs_exists: Boolean

  productId: String

  productId_contains: String

  productId_exists: Boolean

  productId_in: [String]

  productId_not: String

  productId_not_contains: String

  productId_not_in: [String]

  promotionTarget: String

  promotionTarget_contains: String

  promotionTarget_exists: Boolean

  promotionTarget_in: [String]

  promotionTarget_not: String

  promotionTarget_not_contains: String

  promotionTarget_not_in: [String]

  styledTitle_contains: String

  styledTitle_exists: Boolean

  styledTitle_not_contains: String

  sys: SysFilter

  text: String

  text_contains: String

  text_exists: Boolean

  text_in: [String]

  text_not: String

  text_not_contains: String

  text_not_in: [String]

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]

  trackingName: String

  trackingName_contains: String

  trackingName_exists: Boolean

  trackingName_in: [String]

  trackingName_not: String

  trackingName_not_contains: String

  trackingName_not_in: [String]
}

input cfRecommendationGkAirNestedFilter {
  AND: [cfRecommendationGkAirNestedFilter]

  OR: [cfRecommendationGkAirNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  gkAirService_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  outputId: String

  outputId_contains: String

  outputId_exists: Boolean

  outputId_in: [String]

  outputId_not: String

  outputId_not_contains: String

  outputId_not_in: [String]

  recommendationType: String

  recommendationType_contains: String

  recommendationType_exists: Boolean

  recommendationType_in: [String]

  recommendationType_not: String

  recommendationType_not_contains: String

  recommendationType_not_in: [String]

  sys: SysFilter
}

input cfRichtextNestedFilter {
  AND: [cfRichtextNestedFilter]

  OR: [cfRichtextNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  richtext_contains: String

  richtext_exists: Boolean

  richtext_not_contains: String

  sys: SysFilter

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

input cfSeoContentNestedFilter {
  AND: [cfSeoContentNestedFilter]

  OR: [cfSeoContentNestedFilter]

  automaticShopname: String

  automaticShopname_contains: String

  automaticShopname_exists: Boolean

  automaticShopname_in: [String]

  automaticShopname_not: String

  automaticShopname_not_contains: String

  automaticShopname_not_in: [String]

  bottomContent_contains: String

  bottomContent_exists: Boolean

  bottomContent_not_contains: String

  categoryTitle: String

  categoryTitle_contains: String

  categoryTitle_exists: Boolean

  categoryTitle_in: [String]

  categoryTitle_not: String

  categoryTitle_not_contains: String

  categoryTitle_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  description: String

  description_contains: String

  description_exists: Boolean

  description_in: [String]

  description_not: String

  description_not_contains: String

  description_not_in: [String]

  indexDirective: String

  indexDirective_contains: String

  indexDirective_exists: Boolean

  indexDirective_in: [String]

  indexDirective_not: String

  indexDirective_not_contains: String

  indexDirective_not_in: [String]

  onsiteContent_exists: Boolean

  seoContentList_contains_all: [String]

  seoContentList_contains_none: [String]

  seoContentList_contains_some: [String]

  seoContentList_exists: Boolean

  slug: String

  slug_contains: String

  slug_exists: Boolean

  slug_in: [String]

  slug_not: String

  slug_not_contains: String

  slug_not_in: [String]

  sys: SysFilter

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

input cfTipNestedFilter {
  AND: [cfTipNestedFilter]

  OR: [cfTipNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter

  tipText: String

  tipText_contains: String

  tipText_exists: Boolean

  tipText_in: [String]

  tipText_not: String

  tipText_not_contains: String

  tipText_not_in: [String]

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

input cfTopLevelTriggerNestedFilter {
  AND: [cfTopLevelTriggerNestedFilter]

  OR: [cfTopLevelTriggerNestedFilter]

  app: String

  app_contains: String

  app_exists: Boolean

  app_in: [String]

  app_not: String

  app_not_contains: String

  app_not_in: [String]

  campaignTrigger: String

  campaignTrigger_contains: String

  campaignTrigger_exists: Boolean

  campaignTrigger_in: [String]

  campaignTrigger_not: String

  campaignTrigger_not_contains: String

  campaignTrigger_not_in: [String]

  contentfulMetadata: ContentfulMetadataFilter

  customerHasShippingCostsFlat: Boolean

  customerHasShippingCostsFlat_exists: Boolean

  customerHasShippingCostsFlat_not: Boolean

  customerIsNewsletterRecipient: Boolean

  customerIsNewsletterRecipient_exists: Boolean

  customerIsNewsletterRecipient_not: Boolean

  deviceType_contains_all: [String]

  deviceType_contains_none: [String]

  deviceType_contains_some: [String]

  deviceType_exists: Boolean

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  loginState: Boolean

  loginState_exists: Boolean

  loginState_not: Boolean

  optimizelyTrigger: String

  optimizelyTrigger_contains: String

  optimizelyTrigger_exists: Boolean

  optimizelyTrigger_in: [String]

  optimizelyTrigger_not: String

  optimizelyTrigger_not_contains: String

  optimizelyTrigger_not_in: [String]

  sys: SysFilter
}

input cfadditionalLinksMultiTypeNestedFilter {
  AND: [cfadditionalLinksMultiTypeNestedFilter]

  OR: [cfadditionalLinksMultiTypeNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

input cfbacklinkMultiTypeNestedFilter {
  AND: [cfbacklinkMultiTypeNestedFilter]

  OR: [cfbacklinkMultiTypeNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

input cfbannerImageLinkMultiTypeNestedFilter {
  AND: [cfbannerImageLinkMultiTypeNestedFilter]

  OR: [cfbannerImageLinkMultiTypeNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

input cfboxLinkMultiTypeNestedFilter {
  AND: [cfboxLinkMultiTypeNestedFilter]

  OR: [cfboxLinkMultiTypeNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

input cfcontactItemsMultiTypeNestedFilter {
  AND: [cfcontactItemsMultiTypeNestedFilter]

  OR: [cfcontactItemsMultiTypeNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  icon: String

  icon_contains: String

  icon_exists: Boolean

  icon_in: [String]

  icon_not: String

  icon_not_contains: String

  icon_not_in: [String]

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

input cfcontentMultiTypeNestedFilter {
  AND: [cfcontentMultiTypeNestedFilter]

  OR: [cfcontentMultiTypeNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter
}

input cflegalLinksMultiTypeNestedFilter {
  AND: [cflegalLinksMultiTypeNestedFilter]

  OR: [cflegalLinksMultiTypeNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

input cflinkMultiTypeNestedFilter {
  AND: [cflinkMultiTypeNestedFilter]

  OR: [cflinkMultiTypeNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

input cflinksMultiTypeNestedFilter {
  AND: [cflinksMultiTypeNestedFilter]

  OR: [cflinksMultiTypeNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

input cfonsiteContentBannersV2MultiTypeNestedFilter {
  AND: [cfonsiteContentBannersV2MultiTypeNestedFilter]

  OR: [cfonsiteContentBannersV2MultiTypeNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter
}

input cfonsiteContentCardsMultiTypeNestedFilter {
  AND: [cfonsiteContentCardsMultiTypeNestedFilter]

  OR: [cfonsiteContentCardsMultiTypeNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  sys: SysFilter

  title: String

  title_contains: String

  title_exists: Boolean

  title_in: [String]

  title_not: String

  title_not_contains: String

  title_not_in: [String]
}

input cfprimaryButtonLinkMultiTypeNestedFilter {
  AND: [cfprimaryButtonLinkMultiTypeNestedFilter]

  OR: [cfprimaryButtonLinkMultiTypeNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

input cftitleLinkMultiTypeNestedFilter {
  AND: [cftitleLinkMultiTypeNestedFilter]

  OR: [cftitleLinkMultiTypeNestedFilter]

  contentfulMetadata: ContentfulMetadataFilter

  internalTitle: String

  internalTitle_contains: String

  internalTitle_exists: Boolean

  internalTitle_in: [String]

  internalTitle_not: String

  internalTitle_not_contains: String

  internalTitle_not_in: [String]

  label: String

  label_contains: String

  label_exists: Boolean

  label_in: [String]

  label_not: String

  label_not_contains: String

  label_not_in: [String]

  sys: SysFilter
}

"""
A GraphQL Schema defines the capabilities of a GraphQL server. It exposes all available types and directives on the server, as well as the entry points for query, mutation, and subscription operations.
"""
type __Schema {
  description: String

  """
  A list of all types supported by this server.
  """
  types: [__Type!]!

  """
  The type that query operations will be rooted at.
  """
  queryType: __Type!

  """
  If this server supports mutation, the type that mutation operations will be rooted at.
  """
  mutationType: __Type

  """
  If this server support subscription, the type that subscription operations will be rooted at.
  """
  subscriptionType: __Type

  """
  A list of all directives supported by this server.
  """
  directives: [__Directive!]!
}

"""
The fundamental unit of any GraphQL Schema is the type. There are many kinds of types in GraphQL as represented by the `__TypeKind` enum.

Depending on the kind of a type, certain fields describe information about that type. Scalar types provide no information beyond a name, description and optional `specifiedByURL`, while Enum types provide their values. Object and Interface types provide the fields they describe. Abstract types, Union and Interface, provide the Object types possible at runtime. List and NonNull types compose other types.
"""
type __Type {
  kind: __TypeKind!

  name: String

  description: String

  specifiedByURL: String

  fields(includeDeprecated: Boolean = false): [__Field!]

  interfaces: [__Type!]

  possibleTypes: [__Type!]

  enumValues(includeDeprecated: Boolean = false): [__EnumValue!]

  inputFields(includeDeprecated: Boolean = false): [__InputValue!]

  ofType: __Type

  isOneOf: Boolean
}

"""
An enum describing what kind of type a given `__Type` is.
"""
enum __TypeKind {
  """
  Indicates this type is a scalar.
  """
  SCALAR

  """
  Indicates this type is an object. `fields` and `interfaces` are valid fields.
  """
  OBJECT

  """
  Indicates this type is an interface. `fields`, `interfaces`, and `possibleTypes` are valid fields.
  """
  INTERFACE

  """
  Indicates this type is a union. `possibleTypes` is a valid field.
  """
  UNION

  """
  Indicates this type is an enum. `enumValues` is a valid field.
  """
  ENUM

  """
  Indicates this type is an input object. `inputFields` is a valid field.
  """
  INPUT_OBJECT

  """
  Indicates this type is a list. `ofType` is a valid field.
  """
  LIST

  """
  Indicates this type is a non-null. `ofType` is a valid field.
  """
  NON_NULL
}

"""
Object and Interface types are described by a list of Fields, each of which has a name, potentially a list of arguments, and a return type.
"""
type __Field {
  name: String!

  description: String

  args(includeDeprecated: Boolean = false): [__InputValue!]!

  type: __Type!

  isDeprecated: Boolean!

  deprecationReason: String
}

"""
Arguments provided to Fields or Directives and the input fields of an InputObject are represented as Input Values which describe their type and optionally a default value.
"""
type __InputValue {
  name: String!

  description: String

  type: __Type!

  """
  A GraphQL-formatted string representing the default value for this input value.
  """
  defaultValue: String

  isDeprecated: Boolean!

  deprecationReason: String
}

"""
One possible value for a given Enum. Enum values are unique values, not a placeholder for a string or numeric value. However an Enum value is returned in a JSON response as a string.
"""
type __EnumValue {
  name: String!

  description: String

  isDeprecated: Boolean!

  deprecationReason: String
}

"""
A Directive provides a way to describe alternate runtime execution and type validation behavior in a GraphQL document.

In some cases, you need to provide options to alter GraphQL's execution behavior in ways field arguments will not suffice, such as conditionally including or skipping a field. Directives provide this by describing additional information to the executor.
"""
type __Directive {
  name: String!

  description: String

  isRepeatable: Boolean!

  locations: [__DirectiveLocation!]!

  args(includeDeprecated: Boolean = false): [__InputValue!]!
}

"""
A Directive can be adjacent to many parts of the GraphQL language, a __DirectiveLocation describes one such possible adjacencies.
"""
enum __DirectiveLocation {
  """
  Location adjacent to a query operation.
  """
  QUERY

  """
  Location adjacent to a mutation operation.
  """
  MUTATION

  """
  Location adjacent to a subscription operation.
  """
  SUBSCRIPTION

  """
  Location adjacent to a field.
  """
  FIELD

  """
  Location adjacent to a fragment definition.
  """
  FRAGMENT_DEFINITION

  """
  Location adjacent to a fragment spread.
  """
  FRAGMENT_SPREAD

  """
  Location adjacent to an inline fragment.
  """
  INLINE_FRAGMENT

  """
  Location adjacent to a variable definition.
  """
  VARIABLE_DEFINITION

  """
  Location adjacent to a schema definition.
  """
  SCHEMA

  """
  Location adjacent to a scalar definition.
  """
  SCALAR

  """
  Location adjacent to an object type definition.
  """
  OBJECT

  """
  Location adjacent to a field definition.
  """
  FIELD_DEFINITION

  """
  Location adjacent to an argument definition.
  """
  ARGUMENT_DEFINITION

  """
  Location adjacent to an interface definition.
  """
  INTERFACE

  """
  Location adjacent to a union definition.
  """
  UNION

  """
  Location adjacent to an enum definition.
  """
  ENUM

  """
  Location adjacent to an enum value definition.
  """
  ENUM_VALUE

  """
  Location adjacent to an input object type definition.
  """
  INPUT_OBJECT

  """
  Location adjacent to an input object field definition.
  """
  INPUT_FIELD_DEFINITION
}

directive @contentSourceMaps on QUERY

directive @delegatedResourceLink (contentTypeId: String, field: JSON) on FIELD_DEFINITION|OBJECT

directive @enumMapper (value: JSON) on ENUM_VALUE

directive @featureFlag (featureName: String) on FIELD

directive @fieldResolver (data: JSON, kind: String) on FIELD_DEFINITION

directive @typeIdentifier (contentTypeId: String) on OBJECT

"""
Directs the executor to include this field or fragment only when the `if` argument is true.
"""
directive @include ("Included when true." if: Boolean!) on FIELD|FRAGMENT_SPREAD|INLINE_FRAGMENT

"""
Directs the executor to skip this field or fragment when the `if` argument is true.
"""
directive @skip ("Skipped when true." if: Boolean!) on FIELD|FRAGMENT_SPREAD|INLINE_FRAGMENT

"""
Marks an element of a GraphQL schema as no longer supported.
"""
directive @deprecated ("Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax, as specified by [CommonMark](https://commonmark.org/)." reason: String = "No longer supported") on FIELD_DEFINITION|ARGUMENT_DEFINITION|INPUT_FIELD_DEFINITION|ENUM_VALUE

"""
Exposes a URL that specifies the behavior of this scalar.
"""
directive @specifiedBy ("The URL that specifies the behavior of this scalar." url: String!) on SCALAR

"""
Indicates exactly one field must be supplied and this field must not be `null`.
"""
directive @oneOf on INPUT_OBJECT

schema {
  query: Query
}
