query GkKAirRecosOnPdp(
    $productId: String!
    $categoryId: String!
    $sessionId: ID!
    $locale: String!
    $trackingConsent: Boolean!
) {
    gkAir(
        input: {
            locale: $locale
            parameters: [
                { name: "pid", value: $productId }
                { name: "cid", value: $categoryId }
            ]
            serviceId: "productDetailPage"
            sessionId: $sessionId
            trackingConsent: $trackingConsent
        }
    ) {
        ...GkAirOutputFields
    }
}

query GkKAirRecosAfterAddToBasket(
    $productId: String!
    $categoryId: String!
    $size: String!
    $sessionId: ID!
    $locale: String!
    $trackingConsent: Boolean!
) {
    gkAir(
        input: {
            locale: $locale
            parameters: [
                { name: "pid", value: $productId }
                { name: "cid", value: $categoryId }
                { name: "size", value: $size }
            ]
            serviceId: "addToBasket"
            sessionId: $sessionId
            trackingConsent: $trackingConsent
        }
    ) {
        ...GkAirOutputFields
    }
}

fragment GkAirOutputFields on GkAirOutput {
    outputId
    title
    products {
        product {
            id
            brand {
                name
            }
            name
            price {
                ...productPriceFields
            }
            images(input: {}) {
                ...imageFields
            }
        }
    }
}
