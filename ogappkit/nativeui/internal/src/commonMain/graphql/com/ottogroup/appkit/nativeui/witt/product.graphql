query ProductDetail($productID: ID!, $locale: String!) {
    productBy(input: { id: $productID, locale: $locale }) {
        id
        canonicalId # parent ID equivalent?
        # sku # in LAS for tracking
        mkz
        sustainabilityLogos(input: { limit: 5 })
        companyOfOrigin
        name
        mainTitle
        flag
        salesUnit
        brand {
            name
        }
        # distributingCompany for product safety? -> top level query euImporterByProduct
        # seo.url doesn't exist. Construct from ID?

        siblings(input: { includeSoldOut: true, locale: $locale }) {
            id
            label
            name
            price {
                ...productPriceFields
            }
            images(input: {}) {
                ...imageFields
            }
            availability {
                ...availabilityFields
            }
        }
        dimensions {
            id
            iid
            label
            promotion
            variants {
                id
                iid
                hint
                promotion
                size {
                    description
                    label
                }
                availability {
                    ...availabilityFields
                }
                price {
                    ...priceFields
                }
                attributes {
                    key
                    value
                }
            }
        }
        description
        # paybackPoints -> exist in web but where in API? -> floor(price/2)
        images(input: {}) {
            ...imageFields
        }
        rating # productRatingStatistics, productRatings top level queries
        attributes {
            # combine with dimension-level attributes
            key
            value
        }
        breadcrumb {
            id # actual category ID required for recos
        }
        disposalNote
    }
    productRatings(productId: $productID, locale: $locale, limit: 100, offset: 0) {
        ...ProductRatingFields
    }
    productRatingStatistics(productId: $productID, locale: $locale) {
        ...ProductRatingStatisticsFields
    }
}

fragment productPriceFields on ProductPrice {
    currency
    max
    min
    priceRange
    old
    saving
    savingRange
}

fragment priceFields on Price {
    currency
    withTax
    savings
    reductions {
        relative
        withTax
    }
}

fragment availabilityFields on Availability {
    state
    quantity
    key
}

fragment imageFields on ProductImage {
    alt
    hash
    type
}

fragment ProductRatingFields on ProductRatingReview {
    title
    userNickname
    text
    submissionTime
    rating {
        rating
    }
}

fragment ProductRatingStatisticsFields on ProductRatingReviewStatistic {
    totalReviewCount
    averageOverallRating
    ratingDistribution {
        rating
        count
        percentage
    }
}
