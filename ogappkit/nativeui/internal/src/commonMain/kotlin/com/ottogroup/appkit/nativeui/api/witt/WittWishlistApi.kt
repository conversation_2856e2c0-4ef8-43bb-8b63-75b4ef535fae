package com.ottogroup.appkit.nativeui.api.witt

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.map
import com.ottogroup.appkit.nativeui.api.ApolloRepository
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.config.config
import com.ottogroup.appkit.nativeui.config.safeLocale
import com.ottogroup.appkit.nativeui.model.domain.Wishlist
import com.ottogroup.appkit.nativeui.witt.AddItemToWishlistMutation
import com.ottogroup.appkit.nativeui.witt.GetWishlistQuery
import com.ottogroup.appkit.nativeui.witt.RemoveItemFromWishlistMutation

internal interface WittWishlistApi {
    suspend fun getWishlist(): Result<Wishlist>
    suspend fun addToWishlist(id: String): Result<Wishlist>
    suspend fun removeFromWishlist(id: String): Result<Wishlist>
}

internal class WittWishlistApiImpl(
    private val apolloRepository: ApolloRepository,
    private val configProvider: OGNativeConfigProvider,
) : WittWishlistApi {

    private val config get() = configProvider.config<OGNativeConfig.Witt>()

    override suspend fun getWishlist(): Result<Wishlist> {
        return when (val headers = config.bearerTokenHeaders()) {
            is Result.Failure -> Result.Failure(headers.failure)
            is Result.Success -> {
                apolloRepository.query(GetWishlistQuery(config.safeLocale), httpHeaders = headers.value).map { data ->
                    Wishlist(
                        data.wishlist.map { apiItem ->
                            Wishlist.Item(
                                id = apiItem.id,
                                productId = apiItem.product.id,
                            )
                        }
                    )
                }
            }
        }
    }

    override suspend fun addToWishlist(id: String): Result<Wishlist> {
        val parsedId = WittProductId.from(id)

        return when (val headers = config.bearerTokenHeaders()) {
            is Result.Failure -> Result.Failure(headers.failure)
            is Result.Success -> {
                val addResult = apolloRepository.mutate(
                    AddItemToWishlistMutation(
                        productId = parsedId.productId,
                        locale = config.safeLocale
                    ),
                    httpHeaders = headers.value
                )
                if (addResult is Result.Failure) {
                    return Result.Failure(addResult.failure)
                }
                return getWishlist()
            }
        }
    }

    override suspend fun removeFromWishlist(id: String): Result<Wishlist> {
        return when (val headers = config.bearerTokenHeaders()) {
            is Result.Failure -> Result.Failure(headers.failure)
            is Result.Success -> {
                val addResult = apolloRepository.mutate(
                    RemoveItemFromWishlistMutation(
                        itemKey = id,
                        locale = config.safeLocale
                    ),
                    httpHeaders = headers.value
                )
                if (addResult is Result.Failure) {
                    return Result.Failure(addResult.failure)
                }
                return getWishlist()
            }
        }
    }
}
