package com.ottogroup.appkit.nativeui.api.witt

import com.ottogroup.appkit.nativeui.witt.ProductDetailQuery

internal data class WittProductId(
    val productId: String,
    val variantId: String? = null,
    val displayNumber: String? = null,
    val promotion: String? = null,
) {
    constructor(productId: String, variant: ProductDetailQuery.Variant?) : this(
        productId = productId,
        variantId = variant?.id,
        displayNumber = variant?.displayNumber(),
        promotion = variant?.promotion,
    )

    override fun toString(): String {
        return listOf(
            productId,
            variantId.orEmpty(),
            displayNumber.orEmpty(),
            promotion.orEmpty(),
        ).joinToString(ID_SEPARATOR)
    }

    fun isComplete(): Boolean {
        return variantId != null && displayNumber != null && promotion != null
    }

    companion object {
        fun from(string: String): WittProductId {
            val segments = string.split(ID_SEPARATOR)
            if (segments.size == 1) {
                return WittProductId(
                    productId = segments[0],
                    variantId = null,
                    displayNumber = null,
                    promotion = null
                )
            }

            if (segments.size != 4) {
                throw IllegalArgumentException("Invalid product ID format: $string")
            }
            val (productId, variantId, displayNumber, promotion) = segments
            return WittProductId(
                productId = productId,
                variantId = variantId.takeIf { it.isNotEmpty() },
                displayNumber = displayNumber.takeIf { it.isNotEmpty() },
                promotion = promotion.takeIf { it.isNotEmpty() }
            )
        }

        private const val ID_SEPARATOR = "--"
    }
}
