package com.ottogroup.appkit.nativeui.api.witt

import kotlinx.serialization.Serializable
import kotlinx.serialization.json.Json

/* TODO this is not production ready!
 * This is based on a one-time pull of L10n data from the Witt website.
 */
internal object WittL10n {

    private val mapping = Json.decodeFromString<L10nMap>(JSON_CONTENT)

    fun get(key: String, vararg prefixes: String, variables: Map<String, String> = emptyMap()): String {
        val value: L10nValue? = prefixes.firstNotNullOfOrNull { prefix ->
            val fullKey = "$prefix.$key"
            mapping[fullKey]
        } ?: mapping[key]

        return value?.format(variables) ?: key
    }
}

private typealias L10nMap = Map<String, L10nValue>
private typealias L10nValue = List<L10nValueComponent>

private fun L10nValue.format(variables: Map<String, String> = emptyMap()): String {
    return joinToString("") { component ->
        when (component.type) {
            0 -> component.value // static text
            1 -> variables[component.value] ?: component.value // variable text
            2 -> variables[component.value] ?: component.value // variable text but with additional style not used here
            else -> component.value
        }
    }.unescapeUnicode()
}

@Serializable
private data class L10nValueComponent(
    val type: Int,
    val value: String,
    // style is not used in this implementation
    val style: String? = null
)

private fun String.unescapeUnicode(): String {
    return replace(unicodeRegex) {
        val code = it.groupValues[1].toInt(16)
        code.toChar().toString()
    }
}
private val unicodeRegex = Regex("""\\x([0-9a-fA-F]{2})""")

@Suppress("MayBeConstant") // making this const fails compilation with "UTF8 string too large"
private val JSON_CONTENT = """
{
  "backlink.backToOverview": [{ "type": 0, "value": "Zur \\xdcbersicht" }],
  "backlink.backToOverview.label": [
    { "type": 0, "value": "Zur\\xfcck zur " },
    { "type": 1, "value": "area" },
    { "type": 0, "value": " \\xdcbersicht" }
  ],
  "banner.button.open.ariaLabel": [{ "type": 0, "value": "\\xd6ffnen" }],
  "banner.modal.contentLabel": [
    { "type": 0, "value": "Entdecken Sie unsere Sonderangebote" }
  ],
  "basket.changeItem.quantityMoreThanStockError": [
    { "type": 0, "value": "Es sind nur noch " },
    { "type": 1, "value": "stock" },
    { "type": 0, "value": " Artikel auf Lager" }
  ],
  "basket.changeItemFailed": [
    {
      "type": 0,
      "value": "Der Artikel konnte nicht aktualisiert werden, bitte versuchen Sie es erneut."
    }
  ],
  "basket.checkoutButtonText": [{ "type": 0, "value": "Zur Kasse" }],
  "basket.dof.moreArticles": [
    { "type": 0, "value": "Weitere Artikel hinzuf\\xfcgen" }
  ],
  "basket.empty.headline": [{ "type": 0, "value": "Ihr Warenkorb ist leer" }],
  "basket.empty.leftButton": [
    { "type": 0, "value": "Damenbekleidung ansehen" }
  ],
  "basket.empty.leftButtonLink": [{ "type": 0, "value": "/damenmode" }],
  "basket.empty.rightButton": [
    { "type": 0, "value": "Herrenbekleidung ansehen" }
  ],
  "basket.empty.rightButtonLink": [{ "type": 0, "value": "/herrenmode" }],
  "basket.emptyHint": [
    { "type": 0, "value": "Es befinden sich keine Artikel im Warenkorb." }
  ],
  "basket.headline": [{ "type": 0, "value": "Warenkorb" }],
  "basket.hint.coupon": [
    {
      "type": 0,
      "value": "Gutscheincodes k\\xf6nnen Sie am Ende des Bestellprozesses einl\\xf6sen. "
    }
  ],
  "basket.hint.deliveryTime": [
    {
      "type": 0,
      "value": "Lieferbare Artikel sind innerhalb von 2-4 Werktagen bei Ihnen."
    }
  ],
  "basket.homepageButtonText": [{ "type": 0, "value": "Zur Startseite" }],
  "basket.item.changeLabel": [{ "type": 0, "value": "\\xc4ndern" }],
  "basket.item.colorLabel": [{ "type": 0, "value": "Farbe" }],
  "basket.item.sizeLabel": [{ "type": 0, "value": "Gr\\xf6\\xdfe" }],
  "basket.modal.cancellationRight.cancelButtonText": [
    { "type": 0, "value": "Schlie\\xdfen" }
  ],
  "basket.modal.cancellationRight.link": [
    { "type": 0, "value": "Bitte beachten Sie unser " },
    { "type": 1, "value": "link" },
    { "type": 0, "value": "." }
  ],
  "basket.modal.cancellationRight.linkText": [
    { "type": 0, "value": "Widerrufsrecht" }
  ],
  "basket.modal.changeItem.cancelButtonText": [
    { "type": 0, "value": "Schlie\\xdfen" }
  ],
  "basket.modal.changeItem.colorLabel": [{ "type": 0, "value": "Farbe" }],
  "basket.modal.changeItem.headline": [
    { "type": 0, "value": "Artikel \\xe4ndern" }
  ],
  "basket.modal.changeItem.introduction": [
    { "type": 0, "value": "Sie m\\xf6chten Ihren Artikel " },
    { "type": 1, "value": "itemName" },
    { "type": 0, "value": " \\xe4ndern? W\\xe4hlen Sie ..." }
  ],
  "basket.modal.changeItem.quantityLabel": [{ "type": 0, "value": "Menge" }],
  "basket.modal.changeItem.sizeLabel": [
    { "type": 0, "value": "Gr\\xf6\\xdfe" }
  ],
  "basket.modal.changeItem.submitButtonText": [
    { "type": 0, "value": "\\xdcbernehmen" }
  ],
  "basket.payback.text": [
    { "type": 0, "value": "Mit diesem Einkauf k\\xf6nnen Sie " },
    { "type": 1, "value": "points" },
    {
      "type": 0,
      "value": " PAYBACK \\xb0Punkte sammeln. Tragen Sie dazu Ihre PAYBACK Kundennummer im letzten Schritt ein."
    }
  ],
  "basket.quantityChange.minus": [
    { "type": 0, "value": "Menge verringern auf " },
    { "type": 1, "value": "newQuantity" }
  ],
  "basket.quantityChange.plus": [
    { "type": 0, "value": "Menge erh\\xf6hen auf " },
    { "type": 1, "value": "newQuantity" }
  ],
  "basket.recommendation.alternatives": [
    { "type": 0, "value": "Wir haben lieferbare Alternativen f\\xfcr Sie." }
  ],
  "basket.recommendation.headline": [
    { "type": 0, "value": "Sie haben sich anders entschieden?" }
  ],
  "basket.recommendation.itemRemoved": [
    { "type": 0, "value": "Dieser Artikel wurde entfernt:" }
  ],
  "basket.totalAmountLabel": [{ "type": 0, "value": "Gesamtbetrag" }],
  "basket.totalSavingsLabel": [{ "type": 0, "value": "Sie sparen" }],
  "basketOverlay.title": [
    { "type": 0, "value": "Der Artikel wurde hinzugef\\xfcgt" }
  ],
  "breadcrumb.backButton": [
    { "type": 0, "value": "Zur\\xfcck zur vorherigen Seite" }
  ],
  "breadcrumb.home": [{ "type": 0, "value": "Startseite" }],
  "button.categoryLink.label": [
    { "type": 1, "value": "category" },
    { "type": 0, "value": " shoppen" }
  ],
  "button.close.label": [{ "type": 0, "value": "Schlie\\xdfen" }],
  "button.copy.text": [{ "type": 0, "value": "kopiert" }],
  "bypassBlock.skipCategoriesRecommendations": [
    { "type": 0, "value": "Kategorie-Empfehlungen \\xfcberspringen" }
  ],
  "bypassBlock.skipInspirationalText": [
    { "type": 0, "value": "Inspirationstext \\xfcberspringen" }
  ],
  "bypassBlock.skipProductCategories": [
    { "type": 0, "value": "Produktkategorien \\xfcberspringen" }
  ],
  "bypassBlock.skipProductRecommendations": [
    { "type": 0, "value": "Produktempfehlungen \\xfcberspringen" }
  ],
  "bypassBlock.skipToMainContent": [
    { "type": 0, "value": "Zum Hauptinhalt springen" }
  ],
  "carousel.button.next.ariaLabel": [
    { "type": 0, "value": "N\\xe4chster Artikel" }
  ],
  "carousel.button.previous.ariaLabel": [
    { "type": 0, "value": "Vorheriger Artikel" }
  ],
  "carousel.navigation.button.next.ariaLabel": [
    { "type": 0, "value": "N\\xe4chste Kategorie" }
  ],
  "carousel.navigation.button.previous.ariaLabel": [
    { "type": 0, "value": "Vorherige Kategorie" }
  ],
  "carousel.sorting.button.next.ariaLabel": [
    { "type": 0, "value": "N\\xe4chste Sortieroption" }
  ],
  "carousel.sorting.button.previous.ariaLabel": [
    { "type": 0, "value": "Vorherige Sortieroption" }
  ],
  "catalogForm.catalogDescriptionText": [
    {
      "type": 0,
      "value": "Bestellen Sie einfach und bequem Ihre kostenlose Katalogauswahl."
    }
  ],
  "catalogForm.headline": [{ "type": 0, "value": "Lieferadresse" }],
  "catalogForm.noCatalogsSelected": [
    { "type": 0, "value": "Bitte w\\xe4hlen Sie mindestens einen Katalog aus" }
  ],
  "catalogForm.submit": [{ "type": 0, "value": "Bestellen" }],
  "catalogForm.submitSuccessful": [
    { "type": 0, "value": "Bestellung erfolgreich" }
  ],
  "catalogPage.headline": [{ "type": 0, "value": "Katalogbestellung" }],
  "checkout.billingAddressAdditionalNote": [
    {
      "type": 0,
      "value": "Wenn Sie die Rechnungsadresse anpassen, wird diese auch f\\xfcr zuk\\xfcnftige Bestellungen in Ihrem Kundenkonto ge\\xe4ndert."
    }
  ],
  "checkout.header.button": [{ "type": 0, "value": "Zur\\xfcck zum Shop" }],
  "checkout.page.thanks.headline": [
    { "type": 0, "value": "Ihre Bestellung war erfolgreich" }
  ],
  "checkout.survey.buttonText": [
    { "type": 0, "value": "Bewerten & Umfrage starten" }
  ],
  "checkout.survey.hintText": [
    { "type": 0, "value": "Die Umfrage dauert nur 2 Minuten" }
  ],
  "checkout.survey.notRecommendedText": [
    { "type": 0, "value": "Auf gar keinen Fall weiterempfehlen" }
  ],
  "checkout.survey.radioGroupLabel": [
    { "type": 0, "value": "Bewerten Sie Ihre Erfahrung" }
  ],
  "checkout.survey.recommendedText": [
    { "type": 0, "value": "Auf jeden Fall weiterempfehlen" }
  ],
  "checkout.thanks.email": [
    {
      "type": 0,
      "value": "Wir senden Ihnen umgehend eine Best\\xe4tigungs-E-Mail an Ihre Adresse"
    }
  ],
  "checkout.thanks.headline": [
    { "type": 0, "value": "Ihre Bestellung war erfolgreich" }
  ],
  "checkout.thanks.orderId": [{ "type": 0, "value": "Ihre Auftragsnummer:" }],
  "checkoutPage.headline": [
    { "type": 0, "value": "Sicher und einfach bei Witt bestellen" }
  ],
  "colorSelect.modalHeadline": [{ "type": 0, "value": "Farbe w\\xe4hlen" }],
  "colorSwatchPicker": [{ "type": 0, "value": "Select colors" }],
  "colors.colorPreview.group.ariaLabel": [
    { "type": 0, "value": "Verf\\xfcgbare Farben" }
  ],
  "contactform.headline": [{ "type": 0, "value": "Kontaktformular" }],
  "contactform.helptextHeadline": [
    { "type": 0, "value": "Hilft Ihnen das weiter?" }
  ],
  "contactform.label.accessibility": [
    { "type": 0, "value": "Barriere melden" }
  ],
  "contactform.label.accessibilityHelptext": [
    {
      "type": 0,
      "value": "Sollte Ihnen eine Barriere auf unserer Webseite auffallen, k\\xf6nnen Sie uns diese hier mitteilen. Bitte geben Sie uns die genaue Seite an und beschreiben uns mit kurzen Worten worum es sich handelt."
    }
  ],
  "contactform.label.accountDeletion": [
    { "type": 0, "value": "Konto l\\xf6schen" }
  ],
  "contactform.label.accountDeletionHelptext": [
    {
      "type": 0,
      "value": "Sie m\\xf6chten Ihr Konto l\\xf6schen? Bitte geben Sie Ihren Namen, vollst\\xe4ndige Adresse und das Geburtsdatum mit an."
    }
  ],
  "contactform.label.cancellation": [{ "type": 0, "value": "Stornierung" }],
  "contactform.label.cancellationHelptext": [
    {
      "type": 0,
      "value": "Sie m\\xf6chten etwas stornieren? Bitte nennen Sie uns Ihre Kundennummer, Geburtsdatum und den Artikel, den Sie stornieren m\\xf6chten."
    }
  ],
  "contactform.label.contactDataChange": [
    { "type": 0, "value": "Adress-, Namens- und E-Mail-Adress-\\xc4nderung" }
  ],
  "contactform.label.contactDataChangeHelptext": [
    {
      "type": 0,
      "value": "Sie w\\xfcnschen eine \\xc4nderung Ihrer Kontaktdaten? Selbstverst\\xe4ndlich k\\xfcmmern wir uns um Ihr Anliegen. Bitte teilen Sie uns die gew\\xfcnschte \\xc4nderung und Ihren Namen, vollst\\xe4ndige Adresse und das Geburtsdatum mit an."
    }
  ],
  "contactform.label.contactReason": [{ "type": 0, "value": "Kontaktgrund" }],
  "contactform.label.contradiction": [{ "type": 0, "value": "Widerspruch" }],
  "contactform.label.contradictionHelptext": [
    {
      "type": 0,
      "value": "Selbstverst\\xe4ndlich k\\xfcmmern wir uns um Ihr Anliegen. Bitte geben Sie Ihren Namen, vollst\\xe4ndige Adresse und das Geburtsdatum mit an. "
    }
  ],
  "contactform.label.customerComplaint": [
    { "type": 0, "value": "Reklamation" }
  ],
  "contactform.label.customerComplaintHelptext": [
    {
      "type": 0,
      "value": "Waren Sie mit uns nicht zufrieden? Gerne kl\\xe4ren wir Ihr Anliegen. Bitte geben Sie Ihren Namen, vollst\\xe4ndige Adresse und das Geburtsdatum mit an."
    }
  ],
  "contactform.label.customerNumber": [{ "type": 0, "value": "Kundennummer" }],
  "contactform.label.defaultContactReason": [
    { "type": 0, "value": "Bitte w\\xe4hlen" }
  ],
  "contactform.label.deliveryInquiries": [
    { "type": 0, "value": "Anfragen zur Lieferung" }
  ],
  "contactform.label.deliveryInquiriesHelptext": [
    {
      "type": 0,
      "value": "Bitte pr\\xfcfen Sie Ihre Bestellbest\\xe4tigung. Wenn Ihre Bestellung bereits unser Haus verlassen hat, haben Sie eine Versandbest\\xe4tigung mit genaueren Informationen per E-Mail erhalten.  "
    }
  ],
  "contactform.label.fitGuarantee": [
    { "type": 0, "value": "Passt-mir-Garantie" }
  ],
  "contactform.label.fitGuaranteeHelptext": [
    {
      "type": 0,
      "value": "Um die Passt-mir-Garantie einzul\\xf6sen, nennen Sie uns bitte Ihren Namen, Ihre Adresse, die Artikelnummer des umzutauschenden Artikels sowie Ihre alte und gew\\xfcnschte neue Konfektionsgr\\xf6\\xdfe."
    }
  ],
  "contactform.label.invoiceAccountBalance": [
    { "type": 0, "value": "Anfragen zur Rechnung / Kontostand" }
  ],
  "contactform.label.invoiceAccountBalanceHelptext": [
    {
      "type": 0,
      "value": "Ihren aktuellen Kontostand k\\xf6nnen Sie jederzeit in Ihrem Kundenkonto auf dieser Seite einsehen. Falls Sie genauere Informationen ben\\xf6tigen, geben Sie bitte die Kundennummer und das Geburtsdatum mit an. "
    }
  ],
  "contactform.label.message": [{ "type": 0, "value": "Ihre Nachricht" }],
  "contactform.label.order": [{ "type": 0, "value": "Bestellung" }],
  "contactform.label.orderHelptext": [
    {
      "type": 0,
      "value": "Kommen Sie bei einem Bestellschritt nicht weiter? Nennen Sie uns hierf\\xfcr bitte die Kundennummer und das Geburtsdatum."
    }
  ],
  "contactform.label.others": [{ "type": 0, "value": "Sonstiges" }],
  "contactform.label.othersHelptext": [
    {
      "type": 0,
      "value": "Sie haben eine Frage an uns? Gerne helfen wir Ihnen weiter. Nennen Sie uns hierf\\xfcr bitte die Kundennummer oder die vollst\\xe4ndige Anschrift und das Geburtsdatum."
    }
  ],
  "contactform.label.privacy": [{ "type": 0, "value": "Datenschutz" }],
  "contactform.label.privacyHelptext": [
    {
      "type": 0,
      "value": "Selbstverst\\xe4ndlich k\\xfcmmern wir uns um Ihr Anliegen. Bitte geben Sie Ihren Namen, vollst\\xe4ndige Adresse und das Geburtsdatum mit an. "
    }
  ],
  "contactform.label.productSecurity": [
    { "type": 0, "value": "Produktsicherheit" }
  ],
  "contactform.label.productSecurityHelptext": [
    {
      "type": 0,
      "value": "Haben Sie ein Produkt in unserem Onlineshop entdeckt, das potenziell gef\\xe4hrlich ist oder nicht den Sicherheitsstandards entspricht, k\\xf6nnen Sie das hier melden. Bitte geben Sie dabei die Artikelnummer und das von Ihnen erkannte Risiko an."
    }
  ],
  "contactform.label.purchaseOrderChange": [
    { "type": 0, "value": "Bestell\\xe4nderung" }
  ],
  "contactform.label.purchaseOrderChangeHelptext": [
    {
      "type": 0,
      "value": "Sie m\\xf6chten etwas an Ihrer Bestellung \\xe4ndern?  Nennen Sie uns hierf\\xfcr bitte die Kundennummer und das Geburtsdatum."
    }
  ],
  "contactform.label.requestCatalog": [
    { "type": 0, "value": "Katalog anfordern" }
  ],
  "contactform.label.requestCatalogHelptext": [
    {
      "type": 0,
      "value": "Sie m\\xf6chten unseren aktuellen Katalog erhalten? Gerne schicken wir Ihnen diesen zu. Bitte geben Sie  Ihren Namen, vollst\\xe4ndige Adresse und das Geburtsdatum mit an."
    }
  ],
  "contactform.label.requestNewsletter": [
    { "type": 0, "value": "Newsletter anfordern" }
  ],
  "contactform.label.requestNewsletterHelptext": [
    {
      "type": 0,
      "value": "Sie m\\xf6chten unseren Newsletter erhalten? Mit dem Link k\\xf6nnen Sie sich zum kostenlosen Newsletter anmelden. Geben Sie ihre E-Mail-Adresse an und sichern Sie sich einen Gratis-Versand! https://www.witt-weiden.de/content/newsletter"
    }
  ],
  "contactform.label.return": [{ "type": 0, "value": "Retoure" }],
  "contactform.label.returnHelptext": [
    {
      "type": 0,
      "value": "Eine R\\xfccksendung kann bis zu 15 Werktage in Anspruch nehmen. Sie bekommen nach Erhalt der Retoure eine Eingangsbest\\xe4tigung per E-Mail. Haben Sie noch Fragen? Nennen Sie uns hierf\\xfcr bitte die Kundennummer und das Geburtsdatum."
    }
  ],
  "contactform.label.unsubscribeCatalog": [
    { "type": 0, "value": "Katalog abbestellen" }
  ],
  "contactform.label.unsubscribeCatalogHelptext": [
    {
      "type": 0,
      "value": "Sie m\\xf6chten keine Kataloge mehr erhalten? Wir k\\xfcmmern uns darum. Bitte geben Sie Ihren Namen, die vollst\\xe4ndige Adresse und das Geburtsdatum mit an. "
    }
  ],
  "contactform.label.unsubscribeNewsletter": [
    { "type": 0, "value": "Newsletter abbestellen" }
  ],
  "contactform.label.unsubscribeNewsletterHelptext": [
    {
      "type": 0,
      "value": "\\xd6ffnen Sie bitte den erhaltenen Newsletter. Am Ende des Newsletters befindet sich das Feld \"Newsletter abmelden\". Folgen Sie den beschriebenen Schritten zur Abmeldung. Es kann sein, dass die ein oder andere Ausgabe noch bei Ihnen ankommt."
    }
  ],
  "contactform.label.voucher": [{ "type": 0, "value": "Kaufgutschein" }],
  "contactform.label.voucherHelptext": [
    {
      "type": 0,
      "value": "Sie m\\xf6chten einen Kaufgutschein?  Teilen Sie uns den gew\\xfcnschten Betrag, die Kundennummer und das Geburtsdatum mit. "
    }
  ],
  "contactform.required.customerNumber": [
    { "type": 0, "value": "Die Kundennummer besteht aus 6 - 8 Zahlen." }
  ],
  "contactform.required.defaultMessage": [
    { "type": 0, "value": "Dies ist ein Pflichtfeld" }
  ],
  "contactform.requiredFieldsMissing": [
    { "type": 0, "value": "Bitte f\\xfcllen Sie alle Pflichtfelder aus" }
  ],
  "contactform.submit": [{ "type": 0, "value": "Nachricht senden" }],
  "contactform.submitFailed": [
    {
      "type": 0,
      "value": "Beim Abschicken ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut."
    }
  ],
  "contactform.submitSuccessful": [
    { "type": 0, "value": "Nachricht verschickt" }
  ],
  "continueShopping": [{ "type": 0, "value": "Weiter einkaufen" }],
  "controlButtons.remove.ariaLabel": [
    { "type": 0, "value": "Artikel entfernen" }
  ],
  "copyright.text": [{ "type": 0, "value": "\\xa9 2025 Witt" }],
  "coupon.code.title": [{ "type": 0, "value": "Ihr Aktionscode" }],
  "coupon.codes.title": [{ "type": 0, "value": "Ihre Aktionscodes" }],
  "coupon.copyAriaLabel": [
    { "type": 0, "value": "Gutscheincode " },
    { "type": 1, "value": "code" },
    { "type": 0, "value": " in die Zwischenablage kopieren" }
  ],
  "coupon.hint.headline": [{ "type": 0, "value": "Aktionscode kopiert" }],
  "coupon.hint.subheadline": [
    {
      "type": 0,
      "value": "Codes k\\xf6nnen bei der Bezahlung eingel\\xf6st werden."
    }
  ],
  "customer.backToOrders": [{ "type": 0, "value": "Zu Ihren Bestellungen" }],
  "customer.backToOverview": [{ "type": 0, "value": "Zur \\xdcbersicht" }],
  "customer.customerNumber": [{ "type": 0, "value": "Kundennummer" }],
  "customer.greeting": [
    { "type": 0, "value": "Hallo " },
    { "type": 1, "value": "salutation" },
    { "type": 0, "value": " " },
    { "type": 1, "value": "firstName" },
    { "type": 0, "value": " " },
    { "type": 1, "value": "lastName" }
  ],
  "customer.historicOrders.headline": [
    { "type": 0, "value": "Wichtige Information zur Systemumstellung" }
  ],
  "customer.historicOrdersHint.action": [
    { "type": 0, "value": "Kontakt aufnehmen" }
  ],
  "customer.historicOrdersHint.subheadline": [
    {
      "type": 0,
      "value": "Aufgrund der Umstellung unserer Systeme werden zur\\xfcckliegende Bestellungen und Rechnungen, die vor dem 01.07.2025 get\\xe4tigt wurden, nicht mehr angezeigt. M\\xf6chtest du eine Auskunft zu deinen Daten, kannst du dich gerne an unseren Kundenservice wenden."
    }
  ],
  "customer.invoiceOverview.accountBalance": [
    { "type": 0, "value": "Kontostand" }
  ],
  "customer.invoiceOverview.additionalImages": [
    { "type": 0, "value": "+ " },
    { "type": 1, "value": "amount" },
    { "type": 0, "value": " weitere" }
  ],
  "customer.invoiceOverview.backToOverview.label": [
    { "type": 0, "value": "Zur Kundenkonto-\\xdcbersicht" }
  ],
  "customer.invoiceOverview.headline": [{ "type": 0, "value": "Rechnungen" }],
  "customer.invoiceOverview.invoice.label": [
    { "type": 0, "value": "Rechnung vom " },
    { "type": 1, "value": "date" }
  ],
  "customer.invoiceOverview.invoiceAmount": [{ "type": 0, "value": "Betrag" }],
  "customer.invoiceOverview.invoiceNumber": [
    { "type": 0, "value": "Rechnungsnummer" }
  ],
  "customer.invoiceOverview.invoiceStatus": [{ "type": 0, "value": "Status" }],
  "customer.invoiceOverview.invoiceTitle": [
    { "type": 0, "value": "Rechnung vom " },
    { "type": 1, "value": "date" }
  ],
  "customer.invoiceOverview.invoicesFilterSelect.ariaLabel": [
    { "type": 0, "value": "Rechnungen filtern" }
  ],
  "customer.invoiceOverview.invoicesOfLast": [
    { "type": 0, "value": "Rechnungen der letzten" }
  ],
  "customer.invoiceOverview.modalClose": [
    { "type": 0, "value": "Schlie\\xdfen" }
  ],
  "customer.invoiceOverview.modalTitle": [
    { "type": 0, "value": "Sie suchen nach \\xe4lteren Rechnungen?" }
  ],
  "customer.invoiceOverview.open": [{ "type": 0, "value": "offen" }],
  "customer.invoiceOverview.openInvoice": [
    { "type": 0, "value": "Rechnung (PDF) anzeigen" }
  ],
  "customer.invoiceOverview.openModalText": [
    {
      "type": 0,
      "value": "Sie suchen nach Rechnungen \\xe4lter als 12 Monate?"
    }
  ],
  "customer.invoiceOverview.overdue": [
    { "type": 0, "value": "\\xfcberf\\xe4llig" }
  ],
  "customer.invoiceOverview.paid": [{ "type": 0, "value": "erledigt" }],
  "customer.invoiceOverview.sixMonths": [{ "type": 0, "value": "6 Monate" }],
  "customer.invoiceOverview.threeMonths": [{ "type": 0, "value": "3 Monate" }],
  "customer.invoiceOverview.twelveMonths": [
    { "type": 0, "value": "12 Monate" }
  ],
  "customer.maturities.account.headline": [
    { "type": 0, "value": "Kontostand" }
  ],
  "customer.maturities.account.transferHint": [
    {
      "type": 0,
      "value": "Bitte \\xfcberweisen Sie die offenen Betr\\xe4ge bis zum angegebenen Datum auf das folgende Konto (die Buchung Ihrer Zahlung kann 3-5 Werktage in Anspruch nehmen):"
    }
  ],
  "customer.maturities.amount": [{ "type": 0, "value": "Offener Betrag" }],
  "customer.maturities.backToOverview": [
    { "type": 0, "value": "Zur\\xfcck zur \\xdcbersicht" }
  ],
  "customer.maturities.backToOverview.label": [
    { "type": 0, "value": "Zur Kundenkonto-\\xdcbersicht" }
  ],
  "customer.maturities.due.date": [
    { "type": 0, "value": "Zahlung f\\xe4llig am" }
  ],
  "customer.maturities.headline": [{ "type": 0, "value": "Kontostand" }],
  "customer.maturities.outstanding.payments": [
    { "type": 0, "value": "Es sind keine Zahlungen offen." }
  ],
  "customer.maturities.table.headline": [
    { "type": 0, "value": "Offene Zahlungen" }
  ],
  "customer.order.contained.items": [
    { "type": 0, "value": "Enthaltene Artikel" }
  ],
  "customer.order.date": [{ "type": 0, "value": "Bestelldatum:" }],
  "customer.order.delivery.paybreak": [{ "type": 0, "value": "Zahlpause" }],
  "customer.order.delivery.payment": [
    { "type": 0, "value": "Lieferadresse & Zahlung" }
  ],
  "customer.order.number": [{ "type": 0, "value": "Auftrags-Nr.:" }],
  "customer.order.total.amount": [{ "type": 0, "value": "Gesamtbetrag:" }],
  "customer.orderDetails.headline": [{ "type": 0, "value": "Bestelldetails" }],
  "customer.orderOverview.headline": [
    { "type": 0, "value": "Ihre Bestellungen" }
  ],
  "customer.orders.backToOverview.label": [
    { "type": 0, "value": "Zur Kundenkonto-\\xdcbersicht" }
  ],
  "customer.orders.date.from": [{ "type": 0, "value": "vom" }],
  "customer.orders.invoice": [{ "type": 0, "value": "Rechnung (PDF)" }],
  "customer.orders.orderDetails": [
    { "type": 0, "value": "Zu den Bestelldetails" }
  ],
  "customer.orders.productRating": [{ "type": 0, "value": "Artikel bewerten" }],
  "customer.orders.returnItem": [
    { "type": 0, "value": "Artikel zur\\xfccksenden" }
  ],
  "customer.orders.shipmentTracking": [
    { "type": 0, "value": "Sendungsverfolgung" }
  ],
  "customer.orders.shipmentTracking.label": [
    { "type": 0, "value": "Sendungsverfolgung vom " },
    { "type": 1, "value": "orderDate" }
  ],
  "customer.overview.delivery.tile.headline": [
    { "type": 0, "value": "Aktuelle Lieferungen" }
  ],
  "customer.overview.delivery.tile.numberOfDeliveriesText": [
    { "type": 0, "value": "Paket " },
    { "type": 1, "value": "parcelNumber" },
    { "type": 0, "value": " von " },
    { "type": 1, "value": "amountOfParcels" }
  ],
  "customer.overview.delivery.tile.shipmentTrackingLink": [
    { "type": 0, "value": "Sendungsverfolgung" }
  ],
  "customer.overview.delivery.tile.slider.next": [
    { "type": 0, "value": "N\\xe4chste Bestellung" }
  ],
  "customer.overview.delivery.tile.slider.previous": [
    { "type": 0, "value": "Volgende bestelling" }
  ],
  "customer.overview.general.tile.new": [{ "type": 0, "value": "Neu" }],
  "customer.overview.headline": [{ "type": 0, "value": "Kundenkonto" }],
  "customer.overview.invoices.tile.button": [
    { "type": 0, "value": "Alle Rechnungen anzeigen" }
  ],
  "customer.overview.invoices.tile.headline": [
    { "type": 0, "value": "Rechnungen" }
  ],
  "customer.overview.invoices.tile.text": [
    {
      "type": 0,
      "value": "Hier finden Sie Ihre Rechnungen sowie Informationen zu Ihren offenen Zahlungen."
    }
  ],
  "customer.overview.logout.tile.button": [{ "type": 0, "value": "Abmelden" }],
  "customer.overview.logout.tile.headline": [
    { "type": 0, "value": "Abmelden" }
  ],
  "customer.overview.logout.tile.modal.headline": [
    { "type": 0, "value": "Abmelden..." }
  ],
  "customer.overview.logout.tile.text": [
    {
      "type": 0,
      "value": "Die Abmeldung l\\xf6scht die aktuelle Sitzung in Ihrem Browser."
    }
  ],
  "customer.overview.orders.shipmentTracking": [
    { "type": 0, "value": "Sendungsverfolgung" }
  ],
  "customer.overview.orders.tile.button": [
    { "type": 0, "value": "Alle Bestellungen anzeigen" }
  ],
  "customer.overview.orders.tile.headline": [
    { "type": 0, "value": "Bestellungen" }
  ],
  "customer.overview.orders.tile.text": [
    {
      "type": 0,
      "value": "Hier finden Sie Details zu Ihren Bestellungen der letzten 12 Monate. Z.B. Lieferstatus, Retouren, u.v.m."
    }
  ],
  "customer.overview.ordersAndReturns.tile.headline": [
    { "type": 0, "value": "Bestellungen / Retouren" }
  ],
  "customer.overview.ordersAndReturns.tile.ordersButton": [
    { "type": 0, "value": "Alle Bestellungen anzeigen" }
  ],
  "customer.overview.ordersAndReturns.tile.text": [
    {
      "type": 0,
      "value": "Hier finden Sie Details zu Ihren Bestellungen der letzten 12 Monate und k\\xf6nnen Artikel zur\\xfccksenden."
    }
  ],
  "customer.overview.tile.accountBalance.button": [
    { "type": 0, "value": "Offene Zahlungen anzeigen" }
  ],
  "customer.overview.tile.accountBalance.credit": [
    {
      "type": 0,
      "value": "Sie haben derzeit ein Guthaben auf Ihrem Kundenkonto."
    }
  ],
  "customer.overview.tile.accountBalance.headline": [
    { "type": 0, "value": "Kontostand" }
  ],
  "customer.overview.tile.accountBalance.label": [
    { "type": 0, "value": "Aktueller Kontostand" }
  ],
  "customer.overview.tile.accountBalance.noPayments": [
    { "type": 0, "value": "Es sind keine Zahlungen offen." }
  ],
  "customer.overview.tile.accountBalance.outstandingPayments": [
    {
      "type": 0,
      "value": "Hier finden Sie Informationen zu Ihren offenen Zahlungen und die Bankverbindung."
    }
  ],
  "customer.overview.tile.help.button": [
    { "type": 0, "value": "Kontaktm\\xf6glichkeiten" }
  ],
  "customer.overview.tile.help.deliveryAndReturn": [
    { "type": 0, "value": "Lieferung und R\\xfcckgabe" }
  ],
  "customer.overview.tile.help.deliveryAndReturnLink": [
    { "type": 0, "value": "/content/lieferung-und-rueckgabe" }
  ],
  "customer.overview.tile.help.frequentlyAskedQuestions": [
    { "type": 0, "value": "H\\xe4ufige Fragen" }
  ],
  "customer.overview.tile.help.frequentlyAskedQuestionsLink": [
    { "type": 0, "value": "/content/haeufige-fragen" }
  ],
  "customer.overview.tile.help.headline": [
    { "type": 0, "value": "Brauchen Sie Hilfe?" }
  ],
  "customer.overview.tile.help.text": [
    {
      "type": 0,
      "value": "Sollten Sie noch Fragen haben, kontaktieren Sie uns:"
    }
  ],
  "customer.returnItemOverview.backToOverview": [
    { "type": 0, "value": "Zur Bestell\\xfcbersicht" }
  ],
  "customer.returnItemOverview.headline": [
    { "type": 0, "value": "Artikel zur\\xfccksenden" }
  ],
  "customer.unauthorized.bulletpoint1": [
    { "type": 0, "value": "Sehen Sie Ihre Rechnungen ein" }
  ],
  "customer.unauthorized.bulletpoint2": [
    { "type": 0, "value": "\\xdcbersicht \\xfcber Ihre letzten Bestellungen" }
  ],
  "customer.unauthorized.bulletpoint3": [
    { "type": 0, "value": "Ihr aktueller Kontostand" }
  ],
  "customer.unauthorized.buttonText": [{ "type": 0, "value": "Anmelden" }],
  "customer.unauthorized.footerText": [
    {
      "type": 0,
      "value": "Hier k\\xf6nnen Sie sich anmelden. Wenn Sie Ihr Passwort \\xe4ndern m\\xf6chten, nutzen Sie bitte die Funktion \"Passwort vergessen\"."
    }
  ],
  "customer.unauthorized.subText": [
    {
      "type": 0,
      "value": "Bitte melden Sie sich an, um die Vorteile Ihres Kundenkontos zu nutzen."
    }
  ],
  "customer.unauthorized.uspHeadline": [
    { "type": 0, "value": "Ihre Vorteile" }
  ],
  "customer.verified": [{ "type": 0, "value": "verifiziert" }],
  "customer.verify.notVerified.displayText": [
    { "type": 0, "value": "Best\\xe4tigungs-E-Mail anfordern" }
  ],
  "customer.verify.notVerified.headline": [
    { "type": 0, "value": "Bitte best\\xe4tigen Sie Ihre E-Mail-Adresse" }
  ],
  "customer.verify.notVerified.subheadline": [
    {
      "type": 0,
      "value": "Wir ben\\xf6tigen Ihr Einverst\\xe4ndnis, um sicherzustellen, dass unsere Nachrichten Sie zuverl\\xe4ssig erreichen."
    }
  ],
  "customer.verify.pending.displayText": [
    { "type": 0, "value": "Best\\xe4tigungs-E-Mail erneut anfordern" }
  ],
  "customer.verify.pending.headline": [
    { "type": 0, "value": "Best\\xe4tigungs-E-Mail wurde gesendet" }
  ],
  "customer.verify.pending.subheadline": [
    {
      "type": 0,
      "value": "\\xd6ffnen Sie jetzt Ihr Postfach und best\\xe4tigen Sie Ihre E-Mail-Adresse "
    },
    { "type": 1, "value": "email" },
    {
      "type": 0,
      "value": ". Falls Sie die E-Mail nicht finden, \\xfcberpr\\xfcfen Sie Ihren Spam-Ordner oder fordern Sie diese erneut an."
    }
  ],
  "customer.verify.sendingFailed.headline": [
    {
      "type": 0,
      "value": "Ein Fehler ist aufgetreten. Bitte versuchen Sie es sp\\xe4ter erneut."
    }
  ],
  "customer.verify.sendingFailed.subheadline": [
    { "type": 0, "value": "Bitte versuchen Sie es sp\\xe4ter erneut" }
  ],
  "customer.verify.sendingSuccess.headline": [
    { "type": 0, "value": "Erfolgreich gesendet" }
  ],
  "customer.verify.withdrawn.displayText": [
    { "type": 0, "value": "Jetzt Kontakt aufnehmen" }
  ],
  "customer.verify.withdrawn.headline": [
    { "type": 0, "value": "E-Mails k\\xf6nnen nicht zugestellt werden" }
  ],
  "customer.verify.withdrawn.subheadline": [
    { "type": 0, "value": "Ihre E-Mail-Adresse " },
    { "type": 1, "value": "email" },
    {
      "type": 0,
      "value": " scheint nicht richtig zu sein. Bitte setzen Sie sich mit unserem Kundenservice in Verbindung, um diese \\xe4ndern zu lassen."
    }
  ],
  "customerArea.deleteAccount.confirmation.abort": [
    { "type": 0, "value": "Nein, Kundenkonto nicht l\\xf6schen" }
  ],
  "customerArea.deleteAccount.confirmation.confirm": [
    { "type": 0, "value": "Ja, Kundenkonto l\\xf6schen" }
  ],
  "customerArea.deleteAccount.confirmation.description": [
    {
      "type": 0,
      "value": "Sind Sie sicher, dass Sie Ihr Kundenkonto unwiderruflich l\\xf6schen lassen m\\xf6chten?"
    }
  ],
  "customerArea.deleteAccount.default.action": [
    { "type": 0, "value": "Mein Kundenkonto l\\xf6schen" }
  ],
  "customerArea.deleteAccount.default.description": [
    {
      "type": 0,
      "value": "Durch diese Aktion senden Sie eine unwiderrufliche Anforderung zur L\\xf6schung Ihres Kundenkontos."
    }
  ],
  "customerArea.deleteAccount.email.message": [
    {
      "type": 0,
      "value": "Sehr geehrter Kundenservice, bitte l\\xf6schen Sie mein Kundenkonto."
    }
  ],
  "customerArea.deleteAccount.email.subject": [
    { "type": 0, "value": "Bitte l\\xf6schen Sie mein Kundenkonto" }
  ],
  "customerArea.deleteAccount.error.description": [
    {
      "type": 0,
      "value": "Es ist ein Fehler aufgetreten und Ihre Anfrage, Ihr Kundenkonto zu l\\xf6schen, war nicht erfolgreich."
    }
  ],
  "customerArea.deleteAccount.error.help": [
    {
      "type": 0,
      "value": "Bitte kontaktieren Sie direkt unseren Kundenservice oder wiederholen Sie diesen Vorgang zu einem sp\\xe4teren Zeitpunkt erneut."
    }
  ],
  "customerArea.deleteAccount.failure": [
    {
      "type": 0,
      "value": "Es ist ein Fehler aufgetreten und Ihre Anfrage, Ihr Kundenkonto zu l\\xf6schen, war nicht erfolgreich. Bitte kontaktieren Sie uns direkt oder wiederholen Sie diesen Vorgang zu einem sp\\xe4teren Zeitpunkt erneut."
    }
  ],
  "customerArea.deleteAccount.heading": [
    { "type": 0, "value": "Kundenkonto l\\xf6schen" }
  ],
  "customerArea.deleteAccount.success.description": [
    {
      "type": 0,
      "value": "Die L\\xf6schung Ihres Kundenkontos wurde erfolgreich beantragt."
    }
  ],
  "dataprotection.page": [
    { "type": 0, "value": "/content/datenschutzhinweise" }
  ],
  "deliverability.available": [
    { "type": 0, "value": "Lieferung in 2-4 Werktagen" }
  ],
  "deliverability.christmas": [{ "type": 0, "value": "Bis Weihnachten" }],
  "deliverability.directShipping": [{ "type": 0, "value": "in 1-2 Wochen" }],
  "deliverability.directShipping2Weeks": [
    { "type": 0, "value": "in 2-3 Wochen" }
  ],
  "deliverability.directShipping5Days": [
    { "type": 0, "value": "in 5-6 Werktagen" }
  ],
  "deliverability.directShipping8Days": [
    { "type": 0, "value": "in 8-10 Werktagen" }
  ],
  "deliverability.easter": [{ "type": 0, "value": "Bis Ostern" }],
  "deliverability.endOfApril": [{ "type": 0, "value": "Ende April" }],
  "deliverability.endOfAugust": [{ "type": 0, "value": "Ende August" }],
  "deliverability.endOfDecember": [{ "type": 0, "value": "Ende Dezember" }],
  "deliverability.endOfFebruary": [{ "type": 0, "value": "Ende Februar" }],
  "deliverability.endOfJanuary": [{ "type": 0, "value": "Ende Januar" }],
  "deliverability.endOfJuly": [{ "type": 0, "value": "Ende Juli" }],
  "deliverability.endOfJune": [{ "type": 0, "value": "Ende Juni" }],
  "deliverability.endOfMarch": [{ "type": 0, "value": "Ende M\\xe4rz" }],
  "deliverability.endOfMay": [{ "type": 0, "value": "Ende Mai" }],
  "deliverability.endOfNovember": [{ "type": 0, "value": "Ende November" }],
  "deliverability.endOfOctober": [{ "type": 0, "value": "Ende Oktober" }],
  "deliverability.endOfSeptember": [{ "type": 0, "value": "Ende September" }],
  "deliverability.midOfApril": [{ "type": 0, "value": "Mitte April" }],
  "deliverability.midOfAugust": [{ "type": 0, "value": "Mitte August" }],
  "deliverability.midOfDecember": [{ "type": 0, "value": "Mitte Dezember" }],
  "deliverability.midOfFebruary": [{ "type": 0, "value": "Mitte Februar" }],
  "deliverability.midOfJanuary": [{ "type": 0, "value": "Mitte Januar" }],
  "deliverability.midOfJuly": [{ "type": 0, "value": "Mitte Juli" }],
  "deliverability.midOfJune": [{ "type": 0, "value": "Mitte Juni" }],
  "deliverability.midOfMarch": [{ "type": 0, "value": "Mitte M\\xe4rz" }],
  "deliverability.midOfMay": [{ "type": 0, "value": "Mitte Mai" }],
  "deliverability.midOfNovember": [{ "type": 0, "value": "Mitte November" }],
  "deliverability.midOfOctober": [{ "type": 0, "value": "Mitte Oktober" }],
  "deliverability.midOfSeptember": [{ "type": 0, "value": "Mitte September" }],
  "deliverability.outOfStock": [{ "type": 0, "value": "Bereits vergriffen" }],
  "deliverability.outsold": [{ "type": 0, "value": "Ausverkauft" }],
  "dof.button.addToBasket.label": [
    { "type": 1, "value": "quantity" },
    { "type": 0, "value": " Artikel hinzuf\\xfcgen und zum Warenkorb gehen" }
  ],
  "dof.headline": [{ "type": 0, "value": "Bestellschein" }],
  "dof.item.eeaResponsible": [{ "type": 0, "value": "Produktsicherheit" }],
  "dof.item.label": [{ "type": 0, "value": "Artikelnummer" }],
  "dof.item.removeButton.label": [{ "type": 0, "value": "Artikel entfernen" }],
  "dof.item.searchButton.ariaLabel": [
    { "type": 0, "value": "Suche nach Artikelnummer" }
  ],
  "dof.item.title.long": [
    { "type": 0, "value": "Bitte Artikelnummer eingeben" }
  ],
  "dof.item.title.short": [{ "type": 0, "value": "Artikelnummer" }],
  "dof.subheadline": [
    { "type": 0, "value": "Geben Sie hier Ihre Artikelnummern ein." }
  ],
  "dof.validation.error": [
    { "type": 0, "value": "Ung\\xfcltige Artikelnummer" }
  ],
  "dof.validation.quantityMoreThanStockError": [
    { "type": 0, "value": "Es sind nur noch " },
    { "type": 1, "value": "stock" },
    { "type": 0, "value": " Artikel auf Lager" }
  ],
  "email.verification.error": [
    { "type": 0, "value": "Es ist ein Fehler aufgetreten" }
  ],
  "email.verification.error.contactUs": [
    { "type": 0, "value": "Jetzt Kontakt aufnehmen" }
  ],
  "email.verification.error.subheadline": [
    {
      "type": 0,
      "value": "Ihre E-Mail-Adresse konnte nicht verifiziert werden, da ein technischer Fehler aufgetreten ist. Bitte versuchen Sie es zu einem sp\\xe4teren Zeitpunkt erneut. Falls die Probleme weiterhin auftreten, wenden Sie sich gerne an unseren Kundenservice."
    }
  ],
  "email.verification.invalid": [
    { "type": 0, "value": "Der Best\\xe4tigungslink ist ung\\xfcltig" }
  ],
  "email.verification.invalid.subheadline": [
    {
      "type": 0,
      "value": "Wir haben Ihnen einen neuen Link gesendet. Bitte \\xf6ffnen Sie Ihr Postfach und folgen Sie den Anweisungen in der E-Mail. Falls Probleme auftreten, wenden Sie sich gerne an unseren Kundenservice."
    }
  ],
  "email.verification.pending": [
    { "type": 0, "value": "Bitte best\\xe4tigen Sie Ihre E-Mail-Adresse" }
  ],
  "email.verification.pendingOrNoConsent": [
    {
      "type": 0,
      "value": "Wir ben\\xf6tigen Ihr Einverst\\xe4ndnis, um sicherzustellen, dass unsere Nachrichten Sie zuverl\\xe4ssig erreichen. \\xd6ffnen Sie jetzt Ihr Postfach und best\\xe4tigen Sie Ihre E-Mail-Adresse "
    },
    { "type": 1, "value": "email" },
    {
      "type": 0,
      "value": ". Falls Sie die E-Mail nicht finden, \\xfcberpr\\xfcfen Sie Ihren Spam-Ordner oder fordern Sie diese erneut \\xfcber das Kundenkonto an."
    }
  ],
  "email.verification.success": [
    { "type": 0, "value": "Ihre E-Mail-Adresse wurde erfolgreich verifiziert" }
  ],
  "email.verification.success.subheadline": [
    {
      "type": 0,
      "value": "Vielen Dank f\\xfcr die erfolgreiche Best\\xe4tigung Ihrer E-Mail-Adresse. Sie k\\xf6nnen diese Seite nun schlie\\xdfen."
    }
  ],
  "email.verification.title": [{ "type": 0, "value": "E-Mail Verifizierung" }],
  "error.belowMinOrderValueFreeGift": [
    {
      "type": 0,
      "value": "Bitte beachten Sie bei Gratisgeschenken den Mindestbestellwert in H\\xf6he von "
    },
    { "type": 1, "value": "formattedMinOrderValueFreeGift" },
    { "type": 0, "value": "." }
  ],
  "error.default": [
    {
      "type": 0,
      "value": "Leider ist ein technischer Fehler aufgetreten. Bitte versuchen Sie es zu einem sp\\xe4teren Zeitpunkt erneut. Sollte das Problem weiterhin bestehen, wenden Sie sich bitte an unseren Kundenservice unter 0 18 05 - 21 21 00."
    }
  ],
  "error.dofFailedToAddToBasket": [
    {
      "type": 0,
      "value": "Die Artikel auf Ihrem Direktbestellschein konnten nicht in den Warenkorb gelegt werden. Bitte versuchen Sie es sp\\xe4ter erneut."
    }
  ],
  "error.dofVariantNotSelected": [
    {
      "type": 0,
      "value": "Der Direktbestellschein enth\\xe4lt Artikel ohne Gr\\xf6\\xdfe. Bitte \\xfcberpr\\xfcfen Sie die Gr\\xf6\\xdfenauswahl. "
    }
  ],
  "error.maxItems": [
    { "type": 0, "value": "Ihr Warenkorb darf maximal " },
    { "type": 1, "value": "basketMaxItems" },
    {
      "type": 0,
      "value": " Artikel enthalten. Bitte reduzieren Sie die Menge."
    }
  ],
  "error.minOrderValue": [
    { "type": 0, "value": "Bitte beachten Sie den Mindestbestellwert." }
  ],
  "error.moreThanOneFreeGift": [
    {
      "type": 0,
      "value": "Gratiszugaben k\\xf6nnen nur einmal zum Warenkorb hinzugef\\xfcgt werden. "
    }
  ],
  "error.newsletterSubscribeFailed": [
    {
      "type": 0,
      "value": "Bei Ihrer Anmeldung zum Newsletter ist etwas schief gelaufen."
    }
  ],
  "error.newsletterUnsubscribeFailed": [
    {
      "type": 0,
      "value": "Bei Ihrer Abmeldung ist etwas schief gelaufen. Gerne hilft Ihnen unser Kundenservice weiter. Tel: 0180 5 21 21 00"
    }
  ],
  "error.quantityLimitExceeded": [
    {
      "type": 0,
      "value": "Sie haben bereits die maximale Menge von 20 St\\xfcck des Artikels "
    },
    { "type": 1, "value": "displayNumber" },
    { "type": 0, "value": " erreicht. " }
  ],
  "error.quantityMoreThanStock": [
    {
      "type": 0,
      "value": "Die gew\\xe4hlte Menge eines Ihrer Artikel \\xfcbersteigt unseren Lagerbestand. Bitte reduzieren Sie die Menge."
    }
  ],
  "error.soldOut": [
    {
      "type": 0,
      "value": "Ihr Bestellschein enth\\xe4lt ausverkaufte Artikel. Bitte entfernen Sie diese."
    }
  ],
  "error.wishlistFailedToAddItem": [
    {
      "type": 0,
      "value": "Der Artikel konnte nicht zum Merkzettel hinzugef\\xfcgt werden. Bitte versuchen Sie es sp\\xe4ter erneut."
    }
  ],
  "error.wishlistFailedToRemoveItem": [
    {
      "type": 0,
      "value": "Der Artikel konnte nicht von der Wunschliste entfernt werden. Bitte versuchen Sie es sp\\xe4ter erneut."
    }
  ],
  "filter.alloy": [{ "type": 0, "value": "Legierung" }],
  "filter.ankle": [{ "type": 0, "value": "Fu\\xdfweite" }],
  "filter.armLength": [{ "type": 0, "value": "\\xc4rmell\\xe4nge" }],
  "filter.backNeckline": [{ "type": 0, "value": "R\\xfcckenausschnitt" }],
  "filter.braceletMaterial": [{ "type": 0, "value": "Armbandmaterial" }],
  "filter.brandCut": [{ "type": 0, "value": "Modell" }],
  "filter.care": [{ "type": 0, "value": "B\\xfcgelart" }],
  "filter.classification": [{ "type": 0, "value": "Kategorie" }],
  "filter.cleaning": [{ "type": 0, "value": "Reinigung" }],
  "filter.clockFace": [{ "type": 0, "value": "Ziffernblatt" }],
  "filter.collar": [{ "type": 0, "value": "Kragenform" }],
  "filter.comfortLevel": [{ "type": 0, "value": "H\\xe4rtegrad" }],
  "filter.covertype": [{ "type": 0, "value": "Hussen-/Schonertyp" }],
  "filter.cup": [{ "type": 0, "value": "Cup" }],
  "filter.detail": [{ "type": 0, "value": "Besonderheit" }],
  "filter.fabric": [{ "type": 0, "value": "Stoffart" }],
  "filter.fastener": [{ "type": 0, "value": "Verschluss" }],
  "filter.filling": [{ "type": 0, "value": "F\\xfcllung" }],
  "filter.filterSize": [{ "type": 0, "value": "Gr\\xf6\\xdfe" }],
  "filter.fly": [{ "type": 0, "value": "Eingriff" }],
  "filter.form": [{ "type": 0, "value": "Form" }],
  "filter.heatDegree": [{ "type": 0, "value": "W\\xe4rmegrad" }],
  "filter.heelHeight": [{ "type": 0, "value": "Absatzh\\xf6he" }],
  "filter.hood": [{ "type": 0, "value": "Kapuze" }],
  "filter.hosiery": [{ "type": 0, "value": "Strumpfwaren" }],
  "filter.jewelleryColor": [{ "type": 0, "value": "Modeschmuck-Farbe" }],
  "filter.jewelleryLength": [{ "type": 0, "value": "L\\xe4nge in cm" }],
  "filter.knit": [{ "type": 0, "value": "Strickart" }],
  "filter.length": [{ "type": 0, "value": "L\\xe4nge" }],
  "filter.lining": [{ "type": 0, "value": "Innenfutter" }],
  "filter.mainMaterial": [{ "type": 0, "value": "Material" }],
  "filter.materialFunction": [{ "type": 0, "value": "Materialfunktion" }],
  "filter.mattressThickness": [{ "type": 0, "value": "Matratzenh\\xf6he" }],
  "filter.mattressType": [{ "type": 0, "value": "Matratzentyp" }],
  "filter.measures": [{ "type": 0, "value": "Ma\\xdfe" }],
  "filter.neckline": [{ "type": 0, "value": "Ausschnitt" }],
  "filter.occasion": [{ "type": 0, "value": "Anlass" }],
  "filter.packageSize": [{ "type": 0, "value": "Packungsgr\\xf6\\xdfe" }],
  "filter.panelStyle": [{ "type": 0, "value": "Befestigung" }],
  "filter.pattern": [{ "type": 0, "value": "Muster" }],
  "filter.pockets": [{ "type": 0, "value": "Taschen" }],
  "filter.price": [{ "type": 0, "value": "Preis" }],
  "filter.reset.all": [{ "type": 0, "value": "Auswahl aufheben" }],
  "filter.sale": [{ "type": 0, "value": "Nur Sale % Artikel" }],
  "filter.searchColor": [{ "type": 0, "value": "Farbe" }],
  "filter.seasonal": [{ "type": 0, "value": "Saisongrad" }],
  "filter.shape": [{ "type": 0, "value": "Schnittform" }],
  "filter.shaping": [{ "type": 0, "value": "Shape-Funktion" }],
  "filter.sheetHeight": [{ "type": 0, "value": "f\\xfcr Matratzenh\\xf6he" }],
  "filter.shoeWidth": [{ "type": 0, "value": "Schuhweite" }],
  "filter.sizeGuide": [{ "type": 0, "value": "Gr\\xf6\\xdfenlauf" }],
  "filter.sleeveStyle": [{ "type": 0, "value": "\\xc4rmelstil" }],
  "filter.stone": [{ "type": 0, "value": "Steinart" }],
  "filter.straps": [{ "type": 0, "value": "Tr\\xe4ger" }],
  "filter.sustainability": [{ "type": 0, "value": "Produktstandards" }],
  "filter.thermalRating": [{ "type": 0, "value": "W\\xe4rmeverm\\xf6gen" }],
  "filter.thigh": [{ "type": 0, "value": "Oberschenkelweite" }],
  "filter.trademark": [{ "type": 0, "value": "Marke" }],
  "filter.transparency": [{ "type": 0, "value": "Transparenz" }],
  "filter.type": [{ "type": 0, "value": "Typ" }],
  "filter.typeBra": [{ "type": 0, "value": "BH-Typ" }],
  "filter.typeCarpet": [{ "type": 0, "value": "Teppichart" }],
  "filter.underwire": [{ "type": 0, "value": "B\\xfcgel" }],
  "filter.upperMaterial": [{ "type": 0, "value": "Obermaterial" }],
  "filter.use": [{ "type": 0, "value": "Einsatz" }],
  "filter.waistband": [{ "type": 0, "value": "Bund" }],
  "filter.waistline": [{ "type": 0, "value": "Leibh\\xf6he" }],
  "footer.checkout.advantagesHeadline": [
    { "type": 0, "value": "Ihre Vorteile" }
  ],
  "footer.payback.label": [{ "type": 0, "value": "Payback-Information" }],
  "formControls.birthdayInput.invalidDate.error": [
    { "type": 0, "value": "Ihre Angabe ist kein valides Datum." }
  ],
  "formControls.birthdayInput.legalAge.error": [
    {
      "type": 0,
      "value": "Wenn Sie j\\xfcnger als 18 Jahre sind, k\\xf6nnen Sie keinen Newsletter abonnieren."
    }
  ],
  "formControls.numberInput.ariaLabel": [
    { "type": 0, "value": "Nummerneingabe" }
  ],
  "formControls.numberInputButtons.decrease.ariaLabel": [
    { "type": 0, "value": "Menge verringern" }
  ],
  "formControls.numberInputButtons.increase.ariaLabel": [
    { "type": 0, "value": "Menge erh\\xf6hen" }
  ],
  "formControls.searchForm.input.ariaLabel": [
    { "type": 0, "value": "Sucheingabe" }
  ],
  "formControls.searchForm.input.name": [{ "type": 0, "value": "Suchen" }],
  "formControls.searchForm.noResults": [
    { "type": 0, "value": "Keine Ergebnisse gefunden." }
  ],
  "formControls.searchForm.results": [
    { "type": 1, "value": "count" },
    { "type": 0, "value": " Ergebnisse verf\\xfcgbar." }
  ],
  "formControls.searchForm.searchButton.ariaLabel": [
    { "type": 0, "value": "Suchen-Taste" }
  ],
  "formControls.searchForm.suggestions.ariaLabel": [
    { "type": 0, "value": "Vorschl\\xe4ge" }
  ],
  "goto.basket": [{ "type": 0, "value": "Zum Warenkorb" }],
  "header.account": [{ "type": 0, "value": "Kundenkonto" }],
  "header.badge.item": [{ "type": 0, "value": "Artikel " }],
  "header.badge.items": [{ "type": 0, "value": "Artikel" }],
  "header.basket": [{ "type": 0, "value": "Warenkorb" }],
  "header.home.label": [{ "type": 0, "value": "Gehe zur Startseite" }],
  "header.image.alt": [{ "type": 0, "value": "Witt" }],
  "header.orderform": [{ "type": 0, "value": "Bestellschein" }],
  "header.payback.label": [{ "type": 0, "value": "PAYBACK" }],
  "header.search": [{ "type": 0, "value": "Stichwort / Artikelnummer" }],
  "header.wishlist": [{ "type": 0, "value": "Merkzettel" }],
  "hint.closeButtonAriaLabel": [
    { "type": 0, "value": "Benachrichtigung schlie\\xdfen" }
  ],
  "hint.error": [{ "type": 0, "value": "Fehler:" }],
  "hint.info": [{ "type": 0, "value": "Info:" }],
  "hint.success": [{ "type": 0, "value": "Erfolg:" }],
  "hint.warning": [{ "type": 0, "value": "Warnung:" }],
  "info.basket.delivery.state.changed": [
    {
      "type": 0,
      "value": "Die Lieferbarkeit Ihrer Artikel hat sich ge\\xe4ndert. Bitte \\xfcberpr\\xfcfen Sie Ihren Warenkorb."
    }
  ],
  "info.basket.service.disposal.removed": [
    {
      "type": 0,
      "value": "Um unseren Entsorgungs-Service nutzen zu k\\xf6nnen, muss mindestens eine Matratze oder ein Lattenrost bestellt werden. Der Entsorgungs-Service wurde aus dem Warenkorb entfernt.\\n"
    }
  ],
  "input.additional.error.allowedChars": [
    {
      "type": 0,
      "value": "Bitte geben Sie Buchstaben, Ziffern oder folgende Sonderzeichen ein: -./\\':()`^\\n"
    }
  ],
  "input.additional.label": [{ "type": 0, "value": "Adresszusatz" }],
  "input.city.error.allowedChars": [
    {
      "type": 0,
      "value": "Bitte geben Sie Buchstaben, Ziffern oder folgende Sonderzeichen ein: -./\\':()`^\\n"
    }
  ],
  "input.city.error.minLength": [
    { "type": 0, "value": "Bitte geben Sie min " },
    { "type": 1, "value": "min" },
    { "type": 0, "value": " Zeichen ein" }
  ],
  "input.city.error.required": [
    { "type": 0, "value": "Ort ist ein Pflichtfeld" }
  ],
  "input.city.label": [{ "type": 0, "value": "Ort" }],
  "input.email.error.invalid": [
    { "type": 0, "value": "Bitte eine g\\xfcltige E-Mail-Adresse eingeben" }
  ],
  "input.email.label": [{ "type": 0, "value": "Ihre E-Mail-Adresse" }],
  "input.firstName.error.allowedChars": [
    {
      "type": 0,
      "value": "Bitte geben Sie nur Buchstaben oder die folgenden Sonderzeichen ein: -./\\'& \\n"
    }
  ],
  "input.firstName.error.minLength": [
    { "type": 0, "value": "Bitte geben Sie min " },
    { "type": 1, "value": "min" },
    { "type": 0, "value": " Zeichen ein" }
  ],
  "input.firstName.error.required": [
    { "type": 0, "value": "Vorname ist ein Pflichtfeld" }
  ],
  "input.gender.error.required": [
    { "type": 0, "value": "Bitte w\\xe4hlen Sie eine Anrede aus" }
  ],
  "input.gender.genderFemale": [{ "type": 0, "value": "Frau" }],
  "input.gender.genderMale": [{ "type": 0, "value": "Herr" }],
  "input.gender.genderSelect": [{ "type": 0, "value": "Bitte w\\xe4hlen" }],
  "input.houseNumber.error.allowedChars": [
    {
      "type": 0,
      "value": "Bitte geben Sie Buchstaben, Ziffern oder folgende Sonderzeichen ein: -./\\':()`^\\n"
    }
  ],
  "input.houseNumber.error.minLength": [
    { "type": 0, "value": "Bitte geben Sie min " },
    { "type": 1, "value": "min" },
    { "type": 0, "value": " Zeichen ein" }
  ],
  "input.houseNumber.error.required": [
    { "type": 0, "value": "Hausnummer ist ein Pflichtfeld" }
  ],
  "input.houseNumber.label": [{ "type": 0, "value": "Hausnummer" }],
  "input.label.firstName": [{ "type": 0, "value": "Vorname" }],
  "input.label.gender": [{ "type": 0, "value": "Anrede" }],
  "input.lastName.error.allowedChars": [
    {
      "type": 0,
      "value": "Bitte geben Sie Buchstaben, Ziffern oder folgende Sonderzeichen ein: -./\\'& \\n"
    }
  ],
  "input.lastName.error.minLength": [
    { "type": 0, "value": "Bitte geben Sie min " },
    { "type": 1, "value": "min" },
    { "type": 0, "value": " Zeichen ein" }
  ],
  "input.lastName.error.required": [
    { "type": 0, "value": "Nachname ist ein Pflichtfeld" }
  ],
  "input.lastName.label": [{ "type": 0, "value": "Nachname" }],
  "input.street.error.allowedChars": [
    {
      "type": 0,
      "value": "Bitte geben Sie Buchstaben, Ziffern oder folgende Sonderzeichen ein: -./\\'&, \\n"
    }
  ],
  "input.street.error.minLength": [
    { "type": 0, "value": "Bitte geben Sie min " },
    { "type": 1, "value": "min" },
    { "type": 0, "value": " Zeichen ein" }
  ],
  "input.street.error.required": [
    { "type": 0, "value": "Stra\\xdfe ist ein Pflichtfeld" }
  ],
  "input.street.label": [{ "type": 0, "value": "Stra\\xdfe" }],
  "input.text.ariaLabel": [{ "type": 0, "value": "L\\xf6schen-Taste" }],
  "input.text.optionalLabel": [{ "type": 0, "value": "Optional" }],
  "input.zipCode.error.allowedChars": [
    { "type": 0, "value": "Bitte geben Sie mindestens 3 Ziffern ein.\\n" }
  ],
  "input.zipCode.error.minLength": [
    { "type": 0, "value": "Bitte geben Sie min " },
    { "type": 1, "value": "min" },
    { "type": 0, "value": " Zeichen ein" }
  ],
  "input.zipCode.error.required": [
    { "type": 0, "value": "PLZ ist ein Pflichtfeld" }
  ],
  "input.zipCode.label": [{ "type": 0, "value": "Postleitzahl" }],
  "languageSwitch.changeLanguageText": [{ "type": 0, "value": " " }],
  "languageSwitch.targetLanguage": [{ "type": 0, "value": " " }],
  "languageSwitchModal.description": [{ "type": 0, "value": " " }],
  "languageSwitchModal.french": [{ "type": 0, "value": " " }],
  "languageSwitchModal.german": [{ "type": 0, "value": " " }],
  "languageSwitchModal.headline": [{ "type": 0, "value": " " }],
  "link.external.label": [
    { "type": 0, "value": "\\xd6ffnet in neuem Fenster" }
  ],
  "link.phone.label": [{ "type": 0, "value": "\\xd6ffnet Telefon-Client" }],
  "link.phone.number": [{ "type": 0, "value": "Telefonnummer:" }],
  "link.show.less": [{ "type": 0, "value": "Weniger anzeigen" }],
  "link.show.less.newsletter.label": [
    { "type": 0, "value": "Weniger Newsletter-Informationen anzeigen" }
  ],
  "link.show.more": [{ "type": 0, "value": "Mehr anzeigen" }],
  "link.show.more.newsletter.label": [
    { "type": 0, "value": "Mehr Infos zum Newsletter anzeigen" }
  ],
  "loader.default.subheadline": [
    { "type": 0, "value": "Ladevorgang l\\xe4uft..." }
  ],
  "modal.authentication.headline": [{ "type": 0, "value": "Anmeldung" }],
  "modal.close": [{ "type": 0, "value": "Zur\\xfccksetzen" }],
  "modal.dataProtection.link": [
    { "type": 0, "value": "Hinweis zum Datenschutz" }
  ],
  "modal.default.cancel": [{ "type": 0, "value": "Schlie\\xdfen" }],
  "modal.disposalNote.cancel": [{ "type": 0, "value": "Schlie\\xdfen" }],
  "modal.disposalNote.link": [{ "type": 0, "value": "Entsorgungshinweis" }],
  "modal.disposalService.cancel": [{ "type": 0, "value": "Schlie\\xdfen" }],
  "modal.disposalService.headline": [
    { "type": 0, "value": "Matratzen- und Lattenrost-Mitnahme " }
  ],
  "modal.disposalService.link": [{ "type": 0, "value": "Mehr dazu" }],
  "modal.orderInformation.cancel": [{ "type": 0, "value": "Schlie\\xdfen" }],
  "modal.ratingInformation.cancel": [{ "type": 0, "value": "Schlie\\xdfen" }],
  "modal.ratingInformation.linkText": [
    { "type": 0, "value": "Wie funktionieren Bewertungen?" }
  ],
  "modal.shippingCost.cancel": [{ "type": 0, "value": "Schlie\\xdfen" }],
  "modal.shippingCost.linkLabelWithShippingCost": [
    { "type": 0, "value": "inkl. MwSt. zzgl." }
  ],
  "modal.shippingCost.linkLabelWithoutShippingCost": [
    { "type": 0, "value": "inkl. MwSt." }
  ],
  "modal.shippingCost.linkText": [{ "type": 0, "value": "Versand" }],
  "modal.sizeTable.cancel": [{ "type": 0, "value": "Schlie\\xdfen" }],
  "modal.sizeTable.link": [{ "type": 0, "value": "Gr\\xf6\\xdfentabelle" }],
  "modal.submit": [{ "type": 0, "value": "Anwenden" }],
  "modal.unauthorized.headline": [{ "type": 0, "value": "Anmeldung" }],
  "navbar.popover.headline": [{ "type": 0, "value": "Kategorien" }],
  "newsletter.activate.headline": [
    { "type": 0, "value": "Herzlichen Gl\\xfcckwunsch!" }
  ],
  "newsletter.activate.personalData.headline": [
    { "type": 0, "value": "Daten vervollst\\xe4ndigen" }
  ],
  "newsletter.activate.personalData.subheadline": [
    {
      "type": 0,
      "value": "Wenn sich Ihre pers\\xf6nlichen Daten ge\\xe4ndert haben oder Sie ein anderes Newsletter-Format erhalten m\\xf6chten, k\\xf6nnen Sie hier Ihr Newsletter-Abo aktualisieren."
    }
  ],
  "newsletter.activate.subheadline": [
    { "type": 0, "value": "Sie sind jetzt jede Woche bestens informiert." }
  ],
  "newsletter.activate.success.hint": [
    { "type": 0, "value": "Ihre Anmeldung zum Newsletter war erfolgreich.\\n" }
  ],
  "newsletter.activated.headline": [
    { "type": 0, "value": "Anmeldung bereits best\\xe4tigt!" }
  ],
  "newsletter.activated.subheadline": [
    {
      "type": 0,
      "value": "Sie haben den Anmelde-Link zum Newsletter bereits erfolgreich best\\xe4tigt."
    }
  ],
  "newsletter.banner.button.text": [{ "type": 0, "value": "Anmelden" }],
  "newsletter.banner.footer.text": [
    {
      "type": 0,
      "value": "Ihre Daten werden nicht an Dritte weitergegeben. Sie erhalten den Newsletter in regelm\\xe4\\xdfigen Abst\\xe4nden. Sie k\\xf6nnen sich jederzeit von unserem Service abmelden. Sie erhalten Ihren Gutscheincode, nachdem Sie auf den Link in der Best\\xe4tigungsmail geklickt haben."
    }
  ],
  "newsletter.banner.headline": [{ "type": 0, "value": "Unser Newsletter" }],
  "newsletter.banner.highlight": [{ "type": 0, "value": "GRATIS-Versand" }],
  "newsletter.banner.input.label": [
    { "type": 0, "value": "Ihre E-Mail-Adresse" }
  ],
  "newsletter.banner.input.placeholder": [
    { "type": 0, "value": "... hier eingeben" }
  ],
  "newsletter.banner.subHeadline": [
    { "type": 0, "value": "Jetzt anmelden und " },
    { "type": 1, "value": "highlight" },
    { "type": 0, "value": " sichern!" }
  ],
  "newsletter.flyout.ariaLabel": [
    { "type": 0, "value": "Newsletter Anmeldung" }
  ],
  "newsletter.flyout.bulletpoint1": [
    { "type": 0, "value": "Exklusive Vorteile" }
  ],
  "newsletter.flyout.bulletpoint2": [
    { "type": 0, "value": "Trends & Schn\\xe4ppchen" }
  ],
  "newsletter.flyout.bulletpoint3": [{ "type": 0, "value": "GRATIS Versand" }],
  "newsletter.flyout.closeButton.ariaLabel": [
    { "type": 0, "value": "Schlie\\xdfen" }
  ],
  "newsletter.flyout.dataProtection": [
    { "type": 0, "value": "Hinweis zum Datenschutz" }
  ],
  "newsletter.flyout.headline": [{ "type": 0, "value": "Newsletter" }],
  "newsletter.flyout.switchButton": [
    { "type": 0, "value": "GRATIS-Versand sichern!" }
  ],
  "newsletter.form.emptyErrorMessage": [
    { "type": 0, "value": "Bitte geben Sie eine E-Mail-Adresse ein" }
  ],
  "newsletter.subscribe.headline": [
    { "type": 0, "value": "Nur noch ein Schritt" }
  ],
  "newsletter.subscribe.instructions.headline": [
    { "type": 0, "value": "So funktioniert es:" }
  ],
  "newsletter.subscribe.subHeadline": [
    { "type": 0, "value": "...bis zur Newsletter-Anmeldung" }
  ],
  "newsletter.success.headline": [{ "type": 0, "value": "Unser Newsletter" }],
  "newsletter.success.instructions.step1": [
    { "type": 0, "value": "1. \\xd6ffnen Sie Ihr E-Mail Postfach" }
  ],
  "newsletter.success.instructions.step2": [
    { "type": 0, "value": "2. Best\\xe4tigen Sie den Anmelde-Link" }
  ],
  "newsletter.success.instructions.step3": [
    { "type": 0, "value": "3. Erhalten Sie Ihren " },
    { "type": 1, "value": "highlight" }
  ],
  "newsletter.success.instructions.step3Hightlight": [
    { "type": 0, "value": "Gutschein-Code" }
  ],
  "newsletter.success.subHeadline": [
    { "type": 0, "value": "Nur " },
    { "type": 1, "value": "highlight" },
    { "type": 0, "value": " bis zur Anmeldung!" }
  ],
  "newsletter.success.subheadlineHighlight": [
    { "type": 0, "value": "noch ein Klick" }
  ],
  "newsletter.unsubscribe.button.label": [{ "type": 0, "value": "Abmelden" }],
  "newsletter.unsubscribe.button.label.fromNewsletter": [
    { "type": 0, "value": "Abmelden" }
  ],
  "newsletter.unsubscribe.headline": [
    { "type": 0, "value": "Newsletter abmelden" }
  ],
  "newsletter.unsubscribe.headlineUwg": [
    { "type": 0, "value": "Produktempfehlungen abmelden" }
  ],
  "newsletter.unsubscribe.input.label": [
    { "type": 0, "value": "E-Mail-Adresse" }
  ],
  "newsletter.updateData.dateOfBirth.label": [
    { "type": 0, "value": "Geburtsdatum" }
  ],
  "newsletter.updateData.email.label": [{ "type": 0, "value": "E-Mail" }],
  "newsletter.updateData.failed": [
    {
      "type": 0,
      "value": "Die \\xc4nderungen konnten nicht gespeichert werden. Bitte versuchen Sie es sp\\xe4ter erneut."
    }
  ],
  "newsletter.updateData.headline": [
    { "type": 0, "value": "Ihr Witt-Newsletter" }
  ],
  "newsletter.updateData.loginButton": [
    { "type": 0, "value": "Zu den Kontaktdaten" }
  ],
  "newsletter.updateData.loginHint": [
    { "type": 0, "value": "Bitte melden Sie sich bei unserem Kundenservice." }
  ],
  "newsletter.updateData.loginLink": [{ "type": 0, "value": "/contact" }],
  "newsletter.updateData.salutation.female": [{ "type": 0, "value": "Frau" }],
  "newsletter.updateData.salutation.label": [{ "type": 0, "value": "Anrede" }],
  "newsletter.updateData.salutation.male": [{ "type": 0, "value": "Herr" }],
  "newsletter.updateData.salutation.select": [
    { "type": 0, "value": "Bitte w\\xe4hlen" }
  ],
  "newsletter.updateData.subHeadline": [
    {
      "type": 0,
      "value": "Wenn sich Ihre pers\\xf6nlichen Daten ge\\xe4ndert haben oder Sie ein anderes Newsletter-Format erhalten m\\xf6chten, k\\xf6nnen Sie hier Ihr Newsletter-Abo aktualisieren."
    }
  ],
  "newsletter.updateData.submit.label": [{ "type": 0, "value": "Absenden" }],
  "newsletter.updateData.succeeded": [
    {
      "type": 0,
      "value": "Ihre \\xc4nderungen wurden erfolgreich \\xfcbernommen."
    }
  ],
  "offCanvas.burger": [{ "type": 0, "value": "Men\\xfc" }],
  "offCanvas.button.back.ariaLabel": [{ "type": 0, "value": "Zur\\xfcck" }],
  "offCanvas.button.close.ariaLabel": [{ "type": 0, "value": "Schlie\\xdfen" }],
  "offCanvas.level1.headline": [{ "type": 0, "value": "Sortiment" }],
  "offCanvas.levels.showAll": [{ "type": 0, "value": "Alles anzeigen" }],
  "optOut.exists": [
    {
      "type": 0,
      "value": "Sie haben bereits einen Widerspruch gegen die Aufzeichnung erkl\\xe4rt. Ihre Interaktionen auf dieser Website werden nicht aufgezeichnet."
    }
  ],
  "optOut.headline": [{ "type": 0, "value": "Widerruf" }],
  "optOut.nonExistentProvider": [
    {
      "type": 0,
      "value": "Es konnte kein Widerspruch gesetzt werden. Die angegebene URL existiert nicht."
    }
  ],
  "optOut.settings": [{ "type": 0, "value": "Cookie-Einstellungen" }],
  "optOut.success": [{ "type": 0, "value": "Der Widerruf war erfolgreich" }],
  "orderInformation.tooltip.ariaLabel": [
    { "type": 0, "value": "Bestellinformationen" }
  ],
  "pagination.activePageLabel": [
    { "type": 0, "value": "Seite " },
    { "type": 1, "value": "activePage" },
    { "type": 0, "value": " von " },
    { "type": 1, "value": "pages" }
  ],
  "pagination.activePageOfPages": [
    { "type": 1, "value": "activePage" },
    { "type": 0, "value": " von " },
    { "type": 1, "value": "pages" }
  ],
  "pagination.next": [{ "type": 0, "value": "Weiter" }],
  "pagination.previous": [{ "type": 0, "value": "Zur\\xfcck" }],
  "password.reset.errorHeadline": [
    { "type": 0, "value": "Oops! Something went wrong." }
  ],
  "password.reset.errorText": [
    {
      "type": 0,
      "value": "Bitte versuchen Sie erneut den Link in der E-Mail zu klicken. Sie k\\xf6nnen zudem eine neue E-Mail \\xfcber Ihr Kundenkonto anfordern."
    }
  ],
  "password.reset.headline": [
    { "type": 0, "value": "Passwort zur\\xfccksetzen" }
  ],
  "password.reset.hint": [
    {
      "type": 0,
      "value": "Hinweis: Ein sicheres Passwort sollte mindestens 5 Zeichen besitzen und aus Buchstaben, Zahlen und Sonderzeichen bestehen."
    }
  ],
  "payback.learnmore": [{ "type": 0, "value": "mehr erfahren" }],
  "payback.learnmore.label": [
    { "type": 0, "value": "Mehr zu Payback-Punkten erfahren" }
  ],
  "payback.notregistered.cta": [
    { "type": 0, "value": "Jetzt anmelden und \\xb0Punkte sammeln!" }
  ],
  "payback.notregistered.headline": [
    {
      "type": 0,
      "value": "Noch nicht bei PAYBACK? Jetzt anmelden und \\xb0Punkte sammeln!"
    }
  ],
  "payback.notregistered.text": [
    { "type": 0, "value": "Mit diesem Einkauf h\\xe4tten Sie " },
    { "type": 1, "value": "points" },
    { "type": 0, "value": " PAYBACK \\xb0Punkte sammeln k\\xf6nnen." }
  ],
  "payback.registered.text": [
    { "type": 0, "value": "Mit diesem Einkauf haben Sie " },
    { "type": 1, "value": "points" },
    {
      "type": 0,
      "value": " PAYBACK \\xb0Punkte gesammelt. Wenn Sie einen Coupon eingel\\xf6st haben, finden Sie die gesammelten Extra-\\xb0Punkte in K\\xfcrze in Ihrem PAYBACK \\xb0Punktekonto."
    }
  ],
  "payback.title": [{ "type": 0, "value": "Payback-Information" }],
  "pdp.payback.text": [
    { "type": 0, "value": "Sie erhalten " },
    { "type": 1, "value": "points" },
    { "type": 0, "value": " PAYBACK \\xb0Punkte" }
  ],
  "pdp.recommendationLink": [{ "type": 0, "value": "hier" }],
  "pdp.sizeInformation.companyOfOriginExceptSheego": [
    { "type": 0, "value": "Artikel f\\xe4llt kleiner aus" }
  ],
  "pdp.sizeInformation.companyOfOriginSheego": [
    { "type": 0, "value": "Artikel f\\xe4llt gr\\xf6\\xdfer aus" }
  ],
  "pdp.soldoutSizeHint": [
    {
      "type": 0,
      "value": "Die gew\\xe4hlte Gr\\xf6\\xdfe ist leider ausverkauft. Klicken Sie "
    },
    { "type": 1, "value": "here" },
    { "type": 0, "value": ", um \\xe4hnliche Produkte anzusehen." }
  ],
  "pdp.zoomableImage.pressToZoomImage": [
    { "type": 0, "value": "Zum Vergr\\xf6\\xdfern Bild antippen" }
  ],
  "plp.configuration.filterBy": [{ "type": 0, "value": "Filtern nach" }],
  "plp.configuration.open": [{ "type": 0, "value": "Filtern & Sortieren" }],
  "plp.configuration.reset": [{ "type": 0, "value": "Zur\\xfccksetzen" }],
  "plp.configuration.sort": [{ "type": 0, "value": "Sortierung" }],
  "plp.configuration.sortBy": [{ "type": 0, "value": "Sortieren nach" }],
  "plp.configuration.submit": [{ "type": 0, "value": "Anwenden" }],
  "plp.configuration.title": [{ "type": 0, "value": "Filter" }],
  "popup.product.pdp": [{ "type": 0, "value": "Mehr zum Artikel" }],
  "popup.product.similar": [
    { "type": 0, "value": "\\xc4hnliche Artikel anzeigen" }
  ],
  "price.from": [{ "type": 0, "value": "ab" }],
  "price.only": [{ "type": 0, "value": "nur" }],
  "product.addToBasket": [{ "type": 0, "value": "In den Warenkorb" }],
  "product.attribute.additionalDimensionInformation": [
    { "type": 0, "value": "Zusatzinformation" }
  ],
  "product.attribute.alloy": [{ "type": 0, "value": "Legierung" }],
  "product.attribute.armLength": [{ "type": 0, "value": "\\xc4rmell\\xe4nge" }],
  "product.attribute.backNeckline": [
    { "type": 0, "value": "R\\xfcckenausschnitt" }
  ],
  "product.attribute.botanicalName": [
    { "type": 0, "value": "Botanische Bezeichnung der Holzart" }
  ],
  "product.attribute.braceletMaterial": [
    { "type": 0, "value": "Armbandmaterial" }
  ],
  "product.attribute.brandCut": [{ "type": 0, "value": "Modell" }],
  "product.attribute.clockFace": [{ "type": 0, "value": "Ziffernblatt" }],
  "product.attribute.clothingLength": [{ "type": 0, "value": "L\\xe4nge" }],
  "product.attribute.coating": [{ "type": 0, "value": "Beschichtung" }],
  "product.attribute.collar": [{ "type": 0, "value": "Kragenform" }],
  "product.attribute.color": [{ "type": 0, "value": "Farbe" }],
  "product.attribute.comfortLevel": [{ "type": 0, "value": "H\\xe4rtegrad" }],
  "product.attribute.curtainRod": [{ "type": 0, "value": "Gardinenstange" }],
  "product.attribute.detail": [{ "type": 0, "value": "Besonderheit" }],
  "product.attribute.detailColor": [{ "type": 0, "value": "Farbe" }],
  "product.attribute.drawerTechnology": [
    { "type": 0, "value": "Schubladentechnik" }
  ],
  "product.attribute.ekDenier": [{ "type": 0, "value": "Den-Angabe" }],
  "product.attribute.ekDetailBath": [{ "type": 0, "value": "Ausstattung" }],
  "product.attribute.ekFastener": [{ "type": 0, "value": "Verschluss" }],
  "product.attribute.ekForm": [{ "type": 0, "value": "Art/Form" }],
  "product.attribute.ekGuarantee": [{ "type": 0, "value": "Garantie" }],
  "product.attribute.ekHem": [{ "type": 0, "value": "Saumabschluss" }],
  "product.attribute.ekInsole": [{ "type": 0, "value": "Innensohle" }],
  "product.attribute.ekLining": [{ "type": 0, "value": "Innenfutter" }],
  "product.attribute.ekMaterialFunction": [
    { "type": 0, "value": "Materialfunktion" }
  ],
  "product.attribute.ekOutsole": [{ "type": 0, "value": "Au\\xdfensohle" }],
  "product.attribute.ekPockets": [{ "type": 0, "value": "Taschen" }],
  "product.attribute.ekSocket": [{ "type": 0, "value": "Fassung" }],
  "product.attribute.ekStretch": [{ "type": 0, "value": "Stretch" }],
  "product.attribute.electronic": [{ "type": 0, "value": "Elektronik" }],
  "product.attribute.energyEfficiencyClass": [
    { "type": 0, "value": "Energieeffizienzlabel" }
  ],
  "product.attribute.fabric": [{ "type": 0, "value": "Stoffart" }],
  "product.attribute.fastener": [{ "type": 0, "value": "Verschluss" }],
  "product.attribute.hairCare": [{ "type": 0, "value": "Haarpflegetyp" }],
  "product.attribute.hairLength": [{ "type": 0, "value": "Haarl\\xe4nge" }],
  "product.attribute.hairStyle": [{ "type": 0, "value": "Haarstil" }],
  "product.attribute.heatDegree": [
    { "type": 0, "value": "W\\xe4rmeverm\\xf6gen" }
  ],
  "product.attribute.hood": [{ "type": 0, "value": "Kapuze" }],
  "product.attribute.insidePocket": [{ "type": 0, "value": "Innenfach" }],
  "product.attribute.installation": [{ "type": 0, "value": "Montage" }],
  "product.attribute.knit": [{ "type": 0, "value": "Strickart" }],
  "product.attribute.lightColor": [{ "type": 0, "value": "Lichtfarbe" }],
  "product.attribute.lining": [{ "type": 0, "value": "Innenfutter" }],
  "product.attribute.liningMaterial": [{ "type": 0, "value": "Futter" }],
  "product.attribute.maintenanceNote": [
    { "type": 0, "value": "Pflegehinweis" }
  ],
  "product.attribute.material": [{ "type": 0, "value": "Material" }],
  "product.attribute.mattressThickness": [
    { "type": 0, "value": "Matratzenh\\xf6he" }
  ],
  "product.attribute.mattressType": [{ "type": 0, "value": "Matratzentyp" }],
  "product.attribute.measures": [{ "type": 0, "value": "Ma\\xdfe" }],
  "product.attribute.measuresBathmat": [
    { "type": 0, "value": "Ma\\xdfe Badematte" }
  ],
  "product.attribute.measuresBedSize": [
    { "type": 0, "value": "Geeignet f\\xfcr Bettgr\\xf6\\xdfe" }
  ],
  "product.attribute.measuresCableLength": [
    { "type": 0, "value": "Kabell\\xe4nge" }
  ],
  "product.attribute.measuresCountPillowcases": [
    { "type": 0, "value": "Anzahl Kissenbez\\xfcge" }
  ],
  "product.attribute.measuresDuvet": [
    { "type": 0, "value": "Ma\\xdfe Bettdecke" }
  ],
  "product.attribute.measuresDuvetcase": [
    { "type": 0, "value": "Ma\\xdfe Bettbezug" }
  ],
  "product.attribute.measuresInner": [{ "type": 0, "value": "Innenma\\xdfe" }],
  "product.attribute.measuresInnerCompartment": [
    { "type": 0, "value": "Fachinnenma\\xdfe" }
  ],
  "product.attribute.measuresInnerDrawer": [
    { "type": 0, "value": "Schubladeninnenma\\xdfe" }
  ],
  "product.attribute.measuresMattressHeight": [
    { "type": 0, "value": "Geeignet f\\xfcr Matratzenh\\xf6he" }
  ],
  "product.attribute.measuresMattressSize": [
    { "type": 0, "value": "Geeignet f\\xfcr Matratzengr\\xf6\\xdfe" }
  ],
  "product.attribute.measuresMaxDepth": [
    { "type": 0, "value": "Maximale Tiefe" }
  ],
  "product.attribute.measuresOuterDimension": [
    { "type": 0, "value": "Au\\xdfenma\\xdfe" }
  ],
  "product.attribute.measuresPillowcase": [
    { "type": 0, "value": "Ma\\xdfe Kissenbez\\xfcge" }
  ],
  "product.attribute.measuresShelf": [
    { "type": 0, "value": "Ma\\xdfe Einlegeb\\xf6den" }
  ],
  "product.attribute.measuresToiletCover": [
    { "type": 0, "value": "Ma\\xdfe WC-Deckelbezug" }
  ],
  "product.attribute.measuresToiletRug": [
    { "type": 0, "value": "Ma\\xdfe WC-Vorleger" }
  ],
  "product.attribute.neckline": [{ "type": 0, "value": "Ausschnitt" }],
  "product.attribute.packageSize": [
    { "type": 0, "value": "Packungsgr\\xf6\\xdfe" }
  ],
  "product.attribute.panelStyle": [{ "type": 0, "value": "Befestigung" }],
  "product.attribute.pileHeight": [
    { "type": 0, "value": "Florh\\xf6he in mm" }
  ],
  "product.attribute.pillowBeddingtype": [
    { "type": 0, "value": "Kissen-/Bettenart" }
  ],
  "product.attribute.plateauHeight": [
    { "type": 0, "value": "Plateauh\\xf6he in mm" }
  ],
  "product.attribute.scope": [{ "type": 0, "value": "Anwendungsbereich" }],
  "product.attribute.shape": [{ "type": 0, "value": "Schnittform" }],
  "product.attribute.sheetHeight": [
    { "type": 0, "value": "Geeignet f\\xfcr Matratzenh\\xf6he" }
  ],
  "product.attribute.shelf": [{ "type": 0, "value": "Einlegeboden" }],
  "product.attribute.shoeWidth": [{ "type": 0, "value": "Schuhweite" }],
  "product.attribute.sleeveStyle": [{ "type": 0, "value": "\\xc4rmelstil" }],
  "product.attribute.stone": [{ "type": 0, "value": "Steinart" }],
  "product.attribute.straps": [{ "type": 0, "value": "Tr\\xe4ger" }],
  "product.attribute.sustainability": [
    { "type": 0, "value": "Produktstandards" }
  ],
  "product.attribute.trademark": [{ "type": 0, "value": "Marke" }],
  "product.attribute.transparency": [{ "type": 0, "value": "Transparenz" }],
  "product.attribute.typeBra": [{ "type": 0, "value": "BH-Typ" }],
  "product.attribute.typeCarpet": [{ "type": 0, "value": "Teppichart" }],
  "product.attribute.underwire": [{ "type": 0, "value": "B\\xfcgel" }],
  "product.attribute.upperMaterial": [{ "type": 0, "value": "Obermaterial" }],
  "product.attribute.waistband": [{ "type": 0, "value": "Bund" }],
  "product.attribute.weeeNumber": [{ "type": 0, "value": "WEEE-Reg. Nr. DE" }],
  "product.attribute.woodCountryOfOrigin": [
    { "type": 0, "value": "Holz-Herkunftsland" }
  ],
  "product.backToOverview.label": [
    { "type": 0, "value": "Zur\\xfcck zur Produktkategorie" }
  ],
  "product.badge.exclusiveOnline": [{ "type": 0, "value": "Exklusiv online" }],
  "product.badge.heatDegree": [{ "type": 0, "value": "Sommerjacke" }],
  "product.badge.heatDegree.extraWarm": [
    { "type": 0, "value": "stark w\\xe4rmend" }
  ],
  "product.badge.heatDegree.medium": [
    { "type": 0, "value": "\\xdcbergangsjacke" }
  ],
  "product.badge.heatDegree.warm": [{ "type": 0, "value": "w\\xe4rmend" }],
  "product.badge.justSale": [{ "type": 0, "value": "Sale" }],
  "product.badge.new": [{ "type": 0, "value": "NEU" }],
  "product.badge.priceHighlighting": [{ "type": 0, "value": "Preistipp" }],
  "product.badge.sale": [
    { "type": 0, "value": "- " },
    { "style": "percent", "type": 2, "value": "saving" }
  ],
  "product.badge.upToSale": [
    { "type": 0, "value": "bis zu - " },
    { "style": "percent", "type": 2, "value": "saving" }
  ],
  "product.description": [{ "type": 0, "value": "Produktbeschreibung" }],
  "product.detail.effect": [{ "type": 0, "value": "Wirkungsweise" }],
  "product.details": [{ "type": 0, "value": "Details" }],
  "product.displayNumber": [{ "type": 0, "value": "Artikelnummer" }],
  "product.displayNumberChanged": [
    {
      "type": 0,
      "value": "Dieser Artikel wird ab sofort unter der Artikelnummer "
    },
    { "type": 1, "value": "displayNumber" },
    { "type": 0, "value": " gef\\xfchrt (urspr\\xfcnglich " },
    { "type": 1, "value": "oldDisplayNumber" },
    { "type": 0, "value": ")." }
  ],
  "product.errors.giftAlreadyInBasket": [
    {
      "type": 0,
      "value": "Der Artikel liegt bereits im Warenkorb und kann kein weiteres Mal hinzugef\\xfcgt werden."
    }
  ],
  "product.errors.noVariantSelected": [
    { "type": 0, "value": "Bitte w\\xe4hlen Sie eine Gr\\xf6\\xdfe" }
  ],
  "product.errors.quantityLimitExceeded": [
    { "type": 0, "value": "Sie haben diesen Artikel bereits " },
    { "type": 1, "value": "quantityLimit" },
    {
      "type": 0,
      "value": " mal in ihrem Warenkorb. Damit ist die Maximalmenge erreicht.\\n"
    }
  ],
  "product.errors.server": [
    {
      "type": 0,
      "value": "Der Artikel konnte nicht zum Warenkorb hinzugef\\xfcgt werden."
    }
  ],
  "product.errors.variantSoldout": [
    { "type": 0, "value": "Diese Gr\\xf6\\xdfe ist ausverkauft" }
  ],
  "product.forwardingAgent": [{ "type": 0, "value": "(Speditionslieferung)" }],
  "product.productSafety.actorInfo": [
    { "type": 0, "value": "Angaben zum Wirtschaftsakteur in der EU" }
  ],
  "product.productSafety.actorResponsibility": [
    {
      "type": 0,
      "value": "Der in der EU ans\\xe4ssige Wirtschaftsakteur, der f\\xfcr die Einhaltung der erforderlichen Vorschriften dieses Produkts verantwortlich ist:"
    }
  ],
  "product.productSafety.eeaResponsible": [
    { "type": 0, "value": "Produktsicherheit" }
  ],
  "product.productSafety.error": [
    { "type": 0, "value": "Es ist ein Fehler aufgetreten" }
  ],
  "product.productSafety.error.action": [
    { "type": 0, "value": "Jetzt Kontakt aufnehmen" }
  ],
  "product.productSafety.error.content": [
    {
      "type": 0,
      "value": "Beim Laden der Daten zu diesem Artikel ist ein technischer Fehler aufgetreten. Bitte versuchen Sie es erneut oder kontaktieren Sie den Kundenservice."
    }
  ],
  "product.productSafety.headline": [
    { "type": 0, "value": "Produktsicherheit" }
  ],
  "product.productSafety.modalButton": [
    { "type": 0, "value": "schlie\\xdfen" }
  ],
  "product.productSafety.modalHeadline": [
    { "type": 0, "value": "Produktsicherheit" }
  ],
  "product.productSafety.text": [
    { "type": 0, "value": "Verantwortlicher Wirtschaftsakteur in der EU" }
  ],
  "product.rating.modal.already.submitted": [
    {
      "type": 0,
      "value": "Sie haben diesen Artikel bereits bewertet, eine erneute Bewertung ist leider nicht m\\xf6glich."
    }
  ],
  "product.rating.modal.contact.consent": [
    {
      "type": 0,
      "value": "Ich stimme zu, dass bei R\\xfcckfragen Kontakt zu meiner E-Mail Adresse "
    },
    { "type": 1, "value": "email" },
    { "type": 0, "value": " aufgenommen werden darf." }
  ],
  "product.rating.modal.detail.headline": [
    { "type": 0, "value": "Bewertung im Detail" }
  ],
  "product.rating.modal.field.isrecommended": [
    { "type": 0, "value": "W\\xfcrden Sie den Artikel empfehlen?\\n" }
  ],
  "product.rating.modal.field.rating": [
    { "type": 0, "value": "Gesamtbewertung" }
  ],
  "product.rating.modal.field.reviewtext": [
    { "type": 0, "value": "Kommentar \\n" }
  ],
  "product.rating.modal.field.title": [
    { "type": 0, "value": "Bewertungstitel\\n" }
  ],
  "product.rating.modal.field.usernickname": [
    { "type": 0, "value": "Spitzname\\n" }
  ],
  "product.rating.modal.headline": [
    { "type": 0, "value": "\"" },
    { "type": 1, "value": "productName" },
    { "type": 0, "value": "\" bewerten" }
  ],
  "product.rating.modal.mandatoryFields.note": [
    { "type": 0, "value": "Pflichtfelder sind mit * gekennzeichnet." }
  ],
  "product.rating.modal.not.available": [
    {
      "type": 0,
      "value": "Aus technischen Gr\\xfcnden ist es momentan leider nicht m\\xf6glich diesen Artikel zu bewerten."
    }
  ],
  "product.rating.modal.notRecommended": [{ "type": 0, "value": "Nein" }],
  "product.rating.modal.recommended": [{ "type": 0, "value": "Ja" }],
  "product.rating.modal.submit": [{ "type": 0, "value": "Bewerten" }],
  "product.rating.modal.success": [
    {
      "type": 0,
      "value": "Vielen Dank f\\xfcr Ihre Bewertung. Sie k\\xf6nnen dieses Fenster jetzt schlie\\xdfen."
    }
  ],
  "product.rating.noDetailRating": [
    {
      "type": 0,
      "value": "Die Eigenschaften dieses Artikels wurden noch nicht bewertet.\\n"
    }
  ],
  "product.ratings.headline": [{ "type": 0, "value": "Kundenbewertungen" }],
  "product.returnPolicy.headline": [{ "type": 0, "value": "R\\xfcckgabe" }],
  "product.reviewStars.ariaLabel": [
    { "type": 0, "value": "Produktbewertungen, " },
    { "type": 1, "value": "rating" },
    { "type": 0, "value": " von 5 Sternen" }
  ],
  "product.sale": [{ "type": 0, "value": "Sale" }],
  "product.saving": [
    { "type": 0, "value": "Sie sparen " },
    { "style": "percent", "type": 2, "value": "saving" }
  ],
  "product.services.disposal.labelAndPrice": [
    { "type": 0, "value": "Matratzen- und Lattenrost-Mitnahme (29,99€ / Stk.)" }
  ],
  "product.services.disposal.quantityLabel": [
    { "type": 0, "value": "Gesamtmenge der Matratzen und Lattenroste" }
  ],
  "product.services.headline": [
    { "type": 0, "value": "Dazu passende Services:" }
  ],
  "product.share.link": [{ "type": 0, "value": "Teilen" }],
  "product.success.addToBasket": [
    { "type": 0, "value": "Artikel hinzugef\\xfcgt" }
  ],
  "product.sustainabilityLogo.altText": [
    { "type": 0, "value": "Nachhaltigkeitslogo" }
  ],
  "product.sustainabilityLogo.headline": [
    { "type": 0, "value": "Nachhaltigkeit" }
  ],
  "product.sustainabilityLogo.linkText": [
    { "type": 0, "value": "Mehr erfahren" }
  ],
  "product.sustainabilityLogo.linkUrl": [
    { "type": 0, "value": "/content/nachhaltigkeit#gots-und-ocs" }
  ],
  "product.sustainabilityLogo.text": [
    { "type": 0, "value": "Zertifizierungen f\\xfcr dieses Produkt" }
  ],
  "product.upToSaving": [
    { "type": 0, "value": "Sie sparen bis zu " },
    { "style": "percent", "type": 2, "value": "saving" }
  ],
  "productListing.numberOfProducts.label": [
    { "type": 1, "value": "count" },
    { "type": 0, "value": " Produkte" }
  ],
  "promotionBar.label": [
    { "type": 0, "value": "Produktkategorie " },
    { "type": 1, "value": "categoryName" },
    { "type": 0, "value": " \\xf6ffnen" }
  ],
  "rating.modal.reviewItem.yesterday": [{ "type": 0, "value": "Gestern" }],
  "ratings.averageRatingHeadline": [
    { "type": 0, "value": "Durchschnittliche Bewertung" }
  ],
  "ratings.filter.label": [{ "type": 0, "value": "Filter" }],
  "ratings.filter.selectAll": [{ "type": 0, "value": "Alle anzeigen" }],
  "ratings.modal.cancel": [{ "type": 0, "value": "Schlie\\xdfen" }],
  "ratings.modal.headline": [
    { "type": 0, "value": "Bewertungen von \"" },
    { "type": 1, "value": "productName" },
    { "type": 0, "value": "\"" }
  ],
  "ratings.modal.noRatings": [
    { "type": 0, "value": "Es sind keine Bewertungen mit " },
    { "type": 1, "value": "rating" },
    { "type": 0, "value": " Sternen vorhanden." }
  ],
  "ratings.modal.noRatingsYet": [
    {
      "type": 0,
      "value": "Es gibt noch keine Bewertungen f\\xfcr dieses Produkt."
    }
  ],
  "ratings.modal.reviewInDetailHeadline": [
    { "type": 0, "value": "Bewertung im Detail" }
  ],
  "ratings.modal.reviewItem.verifiedPurchase": [
    { "type": 0, "value": "Verifizierter Kauf" }
  ],
  "ratings.readAllReviewsButton": [
    { "type": 0, "value": "Alle Rezensionen lesen" }
  ],
  "ratings.review.feedbackQuestion": [
    { "type": 0, "value": "War diese Rezension hilfreich?" }
  ],
  "ratings.review.negativeFeedbackButton": [{ "type": 0, "value": "Nein" }],
  "ratings.review.positiveFeedbackButton": [{ "type": 0, "value": "Ja" }],
  "ratings.review.productIsNotRecommended": [
    { "type": 0, "value": "Nein, ich w\\xfcrde diesen Artikel nicht empfehlen" }
  ],
  "ratings.review.productIsRecommended": [
    { "type": 0, "value": "Ja, ich w\\xfcrde diesen Artikel empfehlen" }
  ],
  "ratings.review.reportReviewButton": [{ "type": 0, "value": "Melden" }],
  "ratings.review.reportReviewHintText": [
    {
      "type": 0,
      "value": "Vielen Dank, dass Sie diese Rezension gemeldet haben."
    }
  ],
  "ratings.sort.ascending": [{ "type": 0, "value": "Bewertung aufsteigend" }],
  "ratings.sort.descending": [{ "type": 0, "value": "Bewertung absteigend" }],
  "ratings.sort.helpful": [{ "type": 0, "value": "Hilfreichste" }],
  "ratings.sort.label": [{ "type": 0, "value": "Sortierung" }],
  "ratings.sort.modal.header": [
    { "type": 0, "value": "Bewertungen sortieren" }
  ],
  "ratings.sort.newest": [{ "type": 0, "value": "Neueste" }],
  "ratings.stars": [{ "type": 0, "value": "Stern(e)" }],
  "ratings.summary.averageOverallRating": [
    { "type": 0, "value": "Gesamtbewertung " },
    { "type": 1, "value": "overallRatingAverage" },
    { "type": 0, "value": " von " },
    { "type": 1, "value": "maxAmountOfStars" }
  ],
  "ratings.summary.noRatings": [
    { "type": 0, "value": "Dieser Artikel wurde noch nicht bewertet." }
  ],
  "ratings.summary.totalReviewCount": [
    { "type": 0, "value": "(" },
    { "type": 1, "value": "totalReviewCount" },
    { "type": 0, "value": " Kundenbewertungen)" }
  ],
  "recommendation.outfit.combination.label": [
    { "type": 0, "value": "In Kombination mit" }
  ],
  "recommendation.outfit.subtitle": [{ "type": 0, "value": "Dieser Artikel" }],
  "recommendation.wishlist.title": [{ "type": 0, "value": "Mein Merkzettel" }],
  "scarcity.hint": [
    { "type": 0, "value": "Nicht verpassen! Nur noch " },
    { "type": 1, "value": "quantity" },
    { "type": 0, "value": " Artikel verf\\xfcgbar." }
  ],
  "scrid.hint.headline": [
    {
      "type": 0,
      "value": "Willkommen! Ihre PAYBACK-Kundennummer ist bereits aktiv"
    }
  ],
  "scrid.hint.subheadline": [
    {
      "type": 0,
      "value": "Sammeln Sie jetzt PAYBACK\\xb0Punkte bei Ihrem Einkauf"
    }
  ],
  "scrollup.button.text": [{ "type": 0, "value": "nach oben" }],
  "search.field.placeholder": [
    { "type": 0, "value": "Stichwort / Artikelnummer" }
  ],
  "search.noResult.hint": [
    { "type": 0, "value": "Sie haben nach \"" },
    { "type": 1, "value": "term" },
    {
      "type": 0,
      "value": "\" gesucht. Wir haben leider kein Ergebnis gefunden!"
    }
  ],
  "search.title": [
    { "type": 1, "value": "query" },
    { "type": 0, "value": " - Suchergebnis" }
  ],
  "seo.markup.organization": [{ "type": 0, "value": "Witt" }],
  "seo.pdp.description": [
    { "type": 1, "value": "product" },
    { "type": 0, "value": " in " },
    { "type": 1, "value": "color" },
    { "type": 0, "value": " ab " },
    { "type": 1, "value": "price" },
    {
      "type": 0,
      "value": " bequem online shoppen ✔Top Kundenservice ✔Ratenkauf ✔Kauf auf Rechnung ➽ Jetzt bestellen!"
    }
  ],
  "seo.pdp.title": [
    { "type": 1, "value": "product" },
    { "type": 0, "value": " in " },
    { "type": 1, "value": "color" }
  ],
  "seo.plp.description": [
    { "type": 1, "value": "category" },
    {
      "type": 0,
      "value": " online shoppen bei Witt ✔Gro\\xdfe Auswahl & Kombi-Tipps ✔Top Kundenservice ✔Kauf auf Rechnung ✔Ratenkauf ➽ Jetzt Schn\\xe4ppchen entdecken!"
    }
  ],
  "seo.search.description": [
    { "type": 1, "value": "query" },
    {
      "type": 0,
      "value": " wurde gesucht - und gefunden bei Witt ✔Top Preis-Leistungs-Verh\\xe4ltnis ✔Vielf\\xe4ltige Auswahl ✔Kauf auf Rechnung ➽ Jetzt st\\xf6bern und Angebote entdecken!"
    }
  ],
  "seo.title.online": [{ "type": 0, "value": "online kaufen" }],
  "seoContent.variable.currentCampaign": [
    {
      "type": 0,
      "value": "Jetzt mit Ihrem Einkauf 33fach Payback-Punkte sammeln! "
    }
  ],
  "seoContent.variable.usps": [
    {
      "type": 0,
      "value": "✓ Moderne Designs ✓ Bequeme Passformen ✓ Wohlf\\xfchl-Qualit\\xe4t"
    }
  ],
  "seoContent.variable.year": [{ "type": 0, "value": " " }],
  "sharing.siteName": [{ "type": 0, "value": "Witt" }],
  "shippingCostsFlat.headline": [
    { "type": 0, "value": "Versandkosten-Flatrate" }
  ],
  "shippingCostsFlat.prefix.expired": [{ "type": 0, "value": "Abgelaufen am" }],
  "shippingCostsFlat.prefix.valid": [{ "type": 0, "value": "G\\xfcltig bis" }],
  "shopname": [{ "type": 0, "value": "Witt" }],
  "sideNavigation.ariaLabel": [{ "type": 0, "value": "Seitennavigation" }],
  "sizeSelect.ariaLabel": [
    { "type": 0, "value": "Gr\\xf6\\xdfenauswahl Dropdown" }
  ],
  "sizeSelect.modalHeadline": [
    { "type": 0, "value": "Gr\\xf6\\xdfe w\\xe4hlen" }
  ],
  "sizeSelectModal.cancelButton": [{ "type": 0, "value": "Schlie\\xdfen" }],
  "sizeSelectModal.headline": [
    { "type": 0, "value": "Gr\\xf6\\xdfe von \"" },
    { "type": 1, "value": "productName" },
    { "type": 0, "value": "\" w\\xe4hlen" }
  ],
  "slider.priceRange.handle.label": [
    { "type": 0, "value": "Bewegen, um die Preisspanne festzulegen" }
  ],
  "slider.priceRange.label": [
    { "type": 0, "value": "Preisspanne von " },
    { "type": 1, "value": "min" },
    { "type": 0, "value": " " },
    { "type": 1, "value": "currency" },
    { "type": 0, "value": " bis " },
    { "type": 1, "value": "max" },
    { "type": 0, "value": " " },
    { "type": 1, "value": "currency" }
  ],
  "smartbanner.description": [{ "type": 0, "value": "Kostenloser Download" }],
  "smartbanner.showButtonText": [{ "type": 0, "value": "zur App" }],
  "sorting.new": [{ "type": 0, "value": "Neuheiten" }],
  "sorting.price.ascending": [{ "type": 0, "value": "Niedrigster Preis" }],
  "sorting.price.descending": [{ "type": 0, "value": "H\\xf6chster Preis" }],
  "sorting.topseller": [{ "type": 0, "value": "Topseller" }],
  "spinner.loading": [{ "type": 0, "value": "Laden..." }],
  "store.display.description": [
    {
      "type": 0,
      "value": "Einfach Preis-Etikett Ihres Lieblingsartikels unter den Scanner halten und mehr zu Details, Farben\\nund Kombinationsm\\xf6glichkeiten erfahren."
    }
  ],
  "store.display.error": [
    {
      "type": 0,
      "value": "Das gescannte Produkt ist leider nicht im Online Sortiment verf\\xfcgbar. Bei Fragen wenden Sie sich gerne an unser Personal."
    }
  ],
  "store.display.headline": [
    { "type": 0, "value": "Hier mehr erfahren zu Ihrem Lieblings-Artikel" }
  ],
  "substitution.action.replace": [{ "type": 0, "value": "Artikel ersetzen" }],
  "substitution.action.show": [{ "type": 0, "value": "Zum Produkt" }],
  "substitution.intro": [
    {
      "type": 0,
      "value": "Alternativ k\\xf6nnen wir Ihnen einen anderen kostenlosen Artikel empfehlen:"
    }
  ],
  "suggestion.brand": [{ "type": 0, "value": "Marke" }],
  "suggestion.category": [{ "type": 0, "value": "Kategorie" }],
  "suggestion.showAll": [
    { "type": 0, "value": "Alle Ergebnisse zu \"" },
    { "type": 1, "value": "term" },
    { "type": 0, "value": "\" anzeigen" }
  ],
  "support.browser": [{ "type": 0, "value": "Ihr Browser" }],
  "support.cookieId": [{ "type": 0, "value": "Ihre Cookie-ID" }],
  "support.email": [{ "type": 0, "value": "<EMAIL>" }],
  "support.headline": [{ "type": 0, "value": "Support" }],
  "support.information": [
    {
      "type": 0,
      "value": "Bitte teilen Sie uns kurz mit, wie wir Ihnen helfen k\\xf6nnen und senden Sie uns Ihre Service-ID und Browser im Chat oder per E-Mail an "
    },
    { "type": 1, "value": "email" },
    { "type": 0, "value": "." }
  ],
  "support.serviceId": [{ "type": 0, "value": "Ihre Service-ID" }],
  "swiper.navigationButtons.nextSlide.label": [
    { "type": 0, "value": "N\\xe4chstes Element" }
  ],
  "swiper.navigationButtons.prevSlide.label": [
    { "type": 0, "value": "Vorheriges Element" }
  ],
  "swiper.paginationBulletMessage": [
    { "type": 0, "value": "Gehe zu Bild " },
    { "type": 1, "value": "index" }
  ],
  "swiper.slideLabelMessageImage": [
    { "type": 0, "value": "Bild " },
    { "type": 1, "value": "index" },
    { "type": 0, "value": " von " },
    { "type": 1, "value": "slidesLength" }
  ],
  "swiper.slideLabelMessageReco": [
    { "type": 0, "value": "Artikel " },
    { "type": 1, "value": "index" },
    { "type": 0, "value": " von " },
    { "type": 1, "value": "slidesLength" }
  ],
  "totalCalculation.carrierLabel": [{ "type": 0, "value": "Speditionskosten" }],
  "totalCalculation.cashOnDeliveryLabel": [
    { "type": 0, "value": "Nachnahmegeb\\xfchr" }
  ],
  "totalCalculation.expressDeliveryLabel": [
    { "type": 0, "value": "Eilservice-Lieferung" }
  ],
  "totalCalculation.installmentsLabel": [
    { "type": 0, "value": "Ratenaufschlag" }
  ],
  "totalCalculation.paybreakLabel": [
    { "type": 0, "value": "Aufschlag f\\xfcr Zahlpause" }
  ],
  "totalCalculation.shippingLabel": [{ "type": 0, "value": "Versandkosten" }],
  "totalCalculation.sumDiscountLabel": [{ "type": 0, "value": "Gutschein" }],
  "totalCalculation.sumProductLabel": [{ "type": 0, "value": "Summe Artikel" }],
  "trackandtrace.pageTitle": [{ "type": 0, "value": "Retourenstatus" }],
  "trackandtrace.title": [{ "type": 0, "value": "Sendungsverfolgung" }],
  "unauthorized.page.redirect.btn": [
    { "type": 0, "value": "Seite \\xf6ffnen" }
  ],
  "unauthorized.page.redirect.headline": [
    { "type": 0, "value": "Sie werden weitergeleitet..." }
  ],
  "unauthorized.page.redirect.info": [
    {
      "type": 0,
      "value": "Wenn sich die gew\\xfcnschte Seite nicht \\xf6ffnen l\\xe4sst, klicken Sie auf diesen Button:"
    }
  ],
  "userlike.chat.online": [{ "type": 0, "value": "Chat" }],
  "uspbanner.title": [{ "type": 0, "value": "Your shopping benefits" }],
  "uspbanner.uspOne": [
    { "type": 0, "value": "Herausragende Gr\\xf6\\xdfenvielfalt" }
  ],
  "uspbanner.uspThree": [{ "type": 0, "value": "Kostenloser R\\xfcckversand" }],
  "uspbanner.uspTwo": [{ "type": 0, "value": "Kauf auf Rechnung" }],
  "wishlist.addItem": [
    { "type": 0, "value": "Artikel zur Wunschliste hinzuf\\xfcgen\\n" }
  ],
  "wishlist.description": [
    { "type": 0, "value": "Hier finden Sie Ihre gemerkten Artikel.\\n" }
  ],
  "wishlist.explanation.headline": [
    { "type": 0, "value": "Wie funktioniert es?" }
  ],
  "wishlist.explanation.step1": [
    {
      "type": 0,
      "value": "1. W\\xe4hlen Sie Artikel, f\\xfcr die Sie sich interessieren"
    }
  ],
  "wishlist.explanation.step2": [
    {
      "type": 0,
      "value": "2. Speichern Sie die Artikel auf Ihrem pers\\xf6nlichen Merkzettel"
    }
  ],
  "wishlist.explanation.step3": [
    {
      "type": 0,
      "value": "3. Bestellen Sie die Artikel zu einem sp\\xe4teren Zeitpunkt\\n"
    }
  ],
  "wishlist.headline": [{ "type": 0, "value": "Merkzettel\\n" }],
  "wishlist.hint.empty": [
    {
      "type": 0,
      "value": "Es befinden sich keine Artikel auf Ihrem Merkzettel."
    }
  ],
  "wishlist.removeItem": [
    { "type": 0, "value": "Artikel zu Wunschliste hinzugef\\xfcgt" }
  ],
  "wishlist.saveItem": [{ "type": 0, "value": "Merken" }],
  "wishlist.savedItem": [{ "type": 0, "value": "Gemerkt" }]
}
"""
