package com.ottogroup.appkit.nativeui.creator

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.awaitResult
import com.ottogroup.appkit.nativeui.api.ProductReviewsRepository
import com.ottogroup.appkit.nativeui.model.domain.ProductReviews
import com.ottogroup.appkit.nativeui.model.domain.Review
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.ProductReviewsComponent
import com.ottogroup.appkit.nativeui.model.ui.ProductReviewsComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.ProductReviewsScreen
import com.ottogroup.appkit.nativeui.model.ui.ReviewsInformation
import com.ottogroup.appkit.nativeui.model.ui.ReviewsList
import com.ottogroup.appkit.nativeui.model.ui.ReviewsSortingOptions
import com.ottogroup.appkit.nativeui.model.ui.WriteReviewButton
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map

@OptIn(ExperimentalCoroutinesApi::class)
internal class ProductReviewsScreenCreator {
    fun createScreen(
        reviewsFlow: Flow<Operation<ProductReviews>>,
        componentConfigs: ComponentConfigs<ProductReviewsComponentConfig>,
        productReviewsRepository: ProductReviewsRepository,
        screenId: String
    ): Flow<Result<ProductReviewsScreen>> {
        // wait until we have a reviews result
        val reviewsResultFlow = reviewsFlow.awaitResult()

        return reviewsResultFlow.flatMapLatest { reviewsResult ->
            val result = when (reviewsResult) {
                is Result.Failure -> flowOf(Result.Failure<ProductReviewsScreen>(reviewsResult.failure))
                is Result.Success -> {
                    val reviews = reviewsResult.value
                    createReviewsComponents(
                        reviews = reviews,
                        componentConfigs = componentConfigs,
                        productReviewsRepository = productReviewsRepository,
                        screenId = screenId
                    ).map { Result.Success(it) }
                }
            }
            result
        }
    }

    private fun createReviewsComponents(
        reviews: ProductReviews,
        componentConfigs: ComponentConfigs<ProductReviewsComponentConfig>,
        productReviewsRepository: ProductReviewsRepository,
        screenId: String
    ): Flow<ProductReviewsScreen> {
        val filledInComponents = componentConfigs.components.map { config ->
            when (config) {
                is ReviewsInformation.Config -> createReviewsInformation(
                    reviews = reviews,
                    screenId = screenId,
                    config = config
                ).asFlow()

                ReviewsSortingOptions.Config -> createReviewsSortingOptions(screenId = screenId).asFlow()
                is ReviewsList.Config -> {
                    productReviewsRepository.displayedProductReviews.map { reviewList ->
                        createReviewsList(
                            reviews = reviewList,
                            totalReviewsCount = productReviewsRepository.totalReviewsCount,
                            screenId = screenId,
                            config = config
                        )
                    }
                }

                is WriteReviewButton.Config -> createWriteReviewButton(
                    reviews = reviews,
                    config = config,
                ).asFlow()
            }
        }
        return combine(filledInComponents) { values ->
            ProductReviewsScreen(values.filterNotNull())
        }
    }

    private fun ProductReviewsComponent?.asFlow(): Flow<ProductReviewsComponent?> = flowOf(this)

    private fun createReviewsInformation(
        reviews: ProductReviews,
        screenId: String,
        config: ReviewsInformation.Config
    ): ReviewsInformation {
        return ReviewsInformation(
            brandName = reviews.brandName,
            title = reviews.title,
            rating = reviews.reviews?.rating,
            screenId = screenId,
            config = config
        )
    }

    private fun createReviewsSortingOptions(screenId: String): ReviewsSortingOptions {
        return ReviewsSortingOptions(options = ReviewsSortingOptions.SortingOption.entries, screenId = screenId)
    }

    private fun createReviewsList(
        reviews: List<Review>,
        totalReviewsCount: Int,
        screenId: String,
        config: ReviewsList.Config
    ): ReviewsList {
        return ReviewsList(
            reviews = reviews,
            totalReviewsCount = totalReviewsCount,
            screenId = screenId,
            config = config
        )
    }

    private fun createWriteReviewButton(
        reviews: ProductReviews,
        config: WriteReviewButton.Config,
    ): WriteReviewButton {
        return WriteReviewButton(
            writeReviewUrl = config.writeReviewUrl?.replace(
                com.ottogroup.appkit.nativeui.model.ui.ProductReviews.Config.URL_PRODUCT_ID_PLACEHOLDER,
                reviews.id,
            ) ?: reviews.reviews.writeReviewUrl,
            config = config,
        )
    }
}
