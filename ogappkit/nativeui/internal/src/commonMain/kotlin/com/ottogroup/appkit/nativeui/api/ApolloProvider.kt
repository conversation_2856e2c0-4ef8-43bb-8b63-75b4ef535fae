package com.ottogroup.appkit.nativeui.api

import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.api.http.HttpRequest
import com.apollographql.apollo.api.http.HttpResponse
import com.apollographql.apollo.cache.normalized.api.MemoryCacheFactory
import com.apollographql.apollo.cache.normalized.normalizedCache
import com.apollographql.apollo.network.http.HttpInterceptor
import com.apollographql.apollo.network.http.HttpInterceptorChain
import com.apollographql.apollo.network.http.HttpNetworkTransport
import com.ottogroup.appkit.base.di.InternalKoinComponent
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.util.NativeLogger
import kotlin.time.Duration.Companion.minutes
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid
import okio.Buffer
import okio.use
import org.koin.core.Koin
import org.koin.core.scope.Scope

/**
 * Provides an instance of [ApolloClient]. The instance returned must
 * conform to the currently applicable [OGNativeConfig] in regard to base
 * URL.
 *
 * Using this type directly to perform GraphQL operations is usually not
 * optimal. Prefer going through [ApolloRepository].
 */
internal interface ApolloProvider {
    val apolloClient: ApolloClient
}

/**
 * Provides an Apollo client specifically configured for Contentful GraphQL API.
 * Used for size table queries that need to hit the Contentful endpoint.
 */
internal interface ContentfulApolloProvider {
    val apolloClient: ApolloClient
}

internal class ApolloProviderImpl(
    private val configProvider: OGNativeConfigProvider,
) : ApolloProvider, InternalKoinComponent {

    private val config get() = configProvider.configState.value

    override val apolloClient: ApolloClient get() = getKoin().configScope(config).get()
}

private fun Koin.configScope(config: OGNativeConfig): Scope {
    val scopeId = config.toString()
    return getOrCreateScope<OGNativeConfig>(scopeId)
}

internal class ApolloClientFactory(
    private val configProvider: OGNativeConfigProvider,
) {
    private val config get() = configProvider.configState.value

    fun createApolloClient(): ApolloClient {
        return ApolloClient.Builder()
            .networkTransport(
                HttpNetworkTransport.Builder()
                    .serverUrl(config.graphQLBackend.url)
                    .addInterceptor(DebugLogInterceptor())
                    .build()
            )
            .normalizedCache(
                MemoryCacheFactory(
                    maxSizeBytes = CACHE_SIZE_BYTES,
                    expireAfterMillis = CACHE_EXPIRATION_MILLISECONDS,
                )
            ).apply {
                config.graphQLBackend.headers.forEach { addHttpHeader(it.key, it.value) }
            }
            .build()
    }

    companion object {
        internal const val CACHE_SIZE_BYTES = 10 * 1024 * 1024
        internal val CACHE_EXPIRATION_MILLISECONDS = 30.minutes.inWholeMilliseconds
    }
}

private class DebugLogInterceptor : HttpInterceptor {
    override suspend fun intercept(
        request: HttpRequest,
        chain: HttpInterceptorChain
    ): HttpResponse {
        // generate a random ID so we can later match up responses and requests in the logs
        @OptIn(ExperimentalUuidApi::class)
        val id = Uuid.random().toString().takeLast(6)
        NativeLogger.d {
            val body = Buffer().use {
                request.body?.writeTo(it)
                it.readUtf8()
            }
            "Request($id): ${request.method} - ${request.url} - $body"
        }
        val response = chain.proceed(request)
        NativeLogger.d {
            "Response($id): ${response.statusCode} - ${response.body?.peek()?.readUtf8()}"
        }
        return response
    }
}

internal class ContentfulApolloProviderImpl : ContentfulApolloProvider {
    override val apolloClient: ApolloClient by lazy {
        ApolloClient.Builder()
            .networkTransport(
                HttpNetworkTransport.Builder()
                    .serverUrl("https://graphql.contentful.com/content/v1/spaces/joape0egh3h4")
                    .addInterceptor(DebugLogInterceptor())
                    .build()
            )
            .normalizedCache(
                MemoryCacheFactory(
                    maxSizeBytes = ApolloClientFactory.CACHE_SIZE_BYTES,
                    expireAfterMillis = ApolloClientFactory.CACHE_EXPIRATION_MILLISECONDS,
                )
            )
            .build()
    }
}
