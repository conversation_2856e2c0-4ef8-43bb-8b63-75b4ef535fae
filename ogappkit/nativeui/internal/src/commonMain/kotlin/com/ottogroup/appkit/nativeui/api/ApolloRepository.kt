package com.ottogroup.appkit.nativeui.api

import com.apollographql.apollo.ApolloCall
import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.api.ApolloResponse
import com.apollographql.apollo.api.Mutation
import com.apollographql.apollo.api.Operation
import com.apollographql.apollo.api.Query
import com.apollographql.apollo.cache.normalized.FetchPolicy
import com.apollographql.apollo.cache.normalized.api.MemoryCache
import com.apollographql.apollo.cache.normalized.api.NormalizedCache
import com.apollographql.apollo.cache.normalized.apolloStore
import com.apollographql.apollo.cache.normalized.fetchPolicy
import com.apollographql.apollo.cache.normalized.storePartialResponses
import com.apollographql.apollo.cache.normalized.watch
import com.apollographql.apollo.exception.CacheMissException
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.combineWithPreviousValue
import com.ottogroup.appkit.nativeui.util.NativeLogger
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull

/**
 * Provides convenience wrappers around some Apollo functionality, such as
 * conversion to the [Result] type and more transparent error handling. All
 * usage of the Apollo client should happen through an instance of this
 * interface.
 */
internal interface ApolloRepository {
    /** Performs a one-shot mutation. */
    suspend fun <T : Mutation.Data> mutate(
        mutation: Mutation<T>,
        httpHeaders: Map<String, String> = emptyMap(),
    ): Result<T>

    /** Performs a query without hitting the cache and without rate limiting. */
    suspend fun <T : Query.Data> query(
        query: Query<T>,
        httpHeaders: Map<String, String> = emptyMap(),
    ): Result<T>

    /**
     * A query that does not return its result. Used for updating the data
     * contained in the cache. The query is rate-limited to prevent excessive
     * near-simultaneous requests.
     */
    suspend fun <T : Query.Data> updateQuery(
        query: Query<T>,
        httpHeaders: Map<String, String> = emptyMap(),
    )

    /**
     * Performs a query, immediately emitting cached data and watching the
     * in-memory cache for updates.
     *
     * The returned flow is a cold flow: repeated collection will re-trigger
     * the query. Cached results will be immediately available and will be
     * updated with the latest data from the server.
     *
     * If other requests modify the same cache entries as returned by this
     * query, the flow will emit the new data.
     *
     * This flow never naturally completes. It always remains active, watching
     * the cache until cancelled.
     */
    fun <T : Query.Data> queryWithCache(
        query: Query<T>,
        fetchPolicy: FetchPolicy = FetchPolicy.CacheAndNetwork,
        httpHeaders: Map<String, String> = emptyMap(),
    ): Flow<Result<T>>

    /**
     * Performs a query using a specific Apollo client with cache support.
     * Used for queries that need to hit different endpoints (e.g. Contentful).
     */
    fun <T : Query.Data> queryWithCacheUsingClient(
        query: Query<T>,
        apolloClient: ApolloClient,
        fetchPolicy: FetchPolicy = FetchPolicy.CacheAndNetwork,
        httpHeaders: Map<String, String> = emptyMap(),
    ): Flow<Result<T>>
}

internal class ApolloRepositoryImpl(
    private val apolloProvider: ApolloProvider,
    private val rateLimiter: RateLimiter<Query<*>>
) : ApolloRepository {

    override suspend fun <T : Mutation.Data> mutate(
        mutation: Mutation<T>,
        httpHeaders: Map<String, String>,
    ): Result<T> = handleApolloOperation {
        apolloProvider.apolloClient.mutation(mutation).apply {
            httpHeaders.forEach { addHttpHeader(it.key, it.value) }
        }.execute()
    }

    override suspend fun <T : Query.Data> query(
        query: Query<T>,
        httpHeaders: Map<String, String>,
    ): Result<T> = handleApolloOperation {
        apolloProvider.apolloClient.query(query).apply {
            httpHeaders.forEach { addHttpHeader(it.key, it.value) }
        }.fetchPolicy(FetchPolicy.NetworkOnly).execute()
    }

    override suspend fun <T : Query.Data> updateQuery(query: Query<T>, httpHeaders: Map<String, String>) {
        rateLimiter.limit(query) {
            query(query, httpHeaders)
        }
    }

    override fun <T : Query.Data> queryWithCache(
        query: Query<T>,
        fetchPolicy: FetchPolicy,
        httpHeaders: Map<String, String>,
    ): Flow<Result<T>> {
        rateLimiter.mark(query)

        NativeLogger.d { apolloProvider.apolloClient.createInfos().toString() }

        return apolloProvider.apolloClient
            .query(query)
            .apply {
                httpHeaders.forEach { addHttpHeader(it.key, it.value) }
            }
            .storePartialResponses(true)
            .fetchPolicy(fetchPolicy)
            .errorHandlingWatch()
    }

    /**
     * Performs a query using a specific Apollo client with cache support.
     * Used for queries that need to hit different endpoints (e.g. Contentful).
     */
    override fun <T : Query.Data> queryWithCacheUsingClient(
        query: Query<T>,
        apolloClient: ApolloClient,
        fetchPolicy: FetchPolicy,
        httpHeaders: Map<String, String>,
    ): Flow<Result<T>> {
        rateLimiter.mark(query)

        return apolloClient
            .query(query)
            .apply {
                httpHeaders.forEach { addHttpHeader(it.key, it.value) }
            }
            .storePartialResponses(true)
            .fetchPolicy(fetchPolicy)
            .errorHandlingWatch()
    }
}

@Suppress("TooGenericExceptionCaught")
private suspend fun <D : Operation.Data> handleApolloOperation(operation: suspend () -> ApolloResponse<D>): Result<D> {
    return try {
        operation().asResult()
    } catch (t: Throwable) {
        t.asResult()
    }
}

private fun <D : Operation.Data> ApolloResponse<D>.asResult(): Result<D> {
    exception?.let { return it.asResult() }

    val errorsStrings = errors?.map { it.toString() }

    val data = data
    if (data == null) {
        return RuntimeException("Something went wrong:\n$errorsStrings").asResult()
    } else {
        if (!errorsStrings.isNullOrEmpty()) {
            NativeLogger.e("Response has partial data and errors: " + errorsStrings.joinToString(", "))
        }
        return Result.Success(data)
    }
}

private fun <T : Any> Throwable.asResult(): Result.Failure<T> {
    return Result.Failure(this)
}

/**
 * Custom `watch` functionality that only emits failures if we do not
 * already have a previous good result (from cache).
 */
private fun <D : Query.Data> ApolloCall<D>.errorHandlingWatch(): Flow<Result<D>> {
    return watch()
        .filter {
            it.exception !is CacheMissException
        }
        .map { it.asResult() }
        .combineWithPreviousValue()
        .mapNotNull { (previousResult, currentResult) ->
            when {
                // if this is the first result we get, emit it
                previousResult == null -> currentResult
                // if this result is a success, emit it
                currentResult is Result.Success -> currentResult
                // if this result is not a success, but we have previously emitted a success (e.g. a cached one),
                // emit nothing. Previous result is still good.
                previousResult is Result.Success -> null
                // if the previous result is also not a success, return this one to report the latest failure
                else -> currentResult
            }
        }
}

private fun ApolloClient.createInfos(): ApolloCacheDebugInfos {
    val cacheDump = apolloStore.dump()
    val recordCount = cacheDump.values.sumOf { it.values.size }
    val sizeInBytes = cacheSizeInBytes()
    val contents =
        cacheDump.entries.filter { it.key.simpleName.orEmpty().contains("Memory") }.map { it.value.keys.toList() }
            .toString()

    return ApolloCacheDebugInfos(recordCount, sizeInBytes, contents)
}

private fun ApolloClient.cacheSizeInBytes(): Int {
    return apolloStore.accessCache {
        var currentCache: NormalizedCache? = it
        while (currentCache != null && currentCache !is MemoryCache) {
            currentCache = currentCache.nextCache
        }

        currentCache?.size ?: -1
    }
}

private data class ApolloCacheDebugInfos(
    val recordCount: Int,
    val sizeInBytes: Int,
    val contents: String,
)

internal fun NativeApi.CachePolicy.toFetchPolicy(): FetchPolicy {
    return when (this) {
        NativeApi.CachePolicy.CacheOnly -> FetchPolicy.CacheOnly
        NativeApi.CachePolicy.CacheFirst -> FetchPolicy.CacheFirst
        NativeApi.CachePolicy.CacheAndNetwork -> FetchPolicy.CacheAndNetwork
    }
}
