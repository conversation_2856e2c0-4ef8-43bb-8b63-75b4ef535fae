package com.ottogroup.appkit.nativeui.api.lascana

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.benchmark
import com.ottogroup.appkit.base.getOrNull
import com.ottogroup.appkit.base.log
import com.ottogroup.appkit.base.map
import com.ottogroup.appkit.nativeui.api.ApolloRepository
import com.ottogroup.appkit.nativeui.api.NativeApi
import com.ottogroup.appkit.nativeui.api.toFetchPolicy
import com.ottogroup.appkit.nativeui.lascana.MasterProductVariantsQuery
import com.ottogroup.appkit.nativeui.lascana.MinimalProductQuery
import com.ottogroup.appkit.nativeui.lascana.ProductDetailQuery
import com.ottogroup.appkit.nativeui.lascana.ProductReviewsQuery
import com.ottogroup.appkit.nativeui.lascana.fragment.AvailabilityFields
import com.ottogroup.appkit.nativeui.lascana.fragment.VariantInfo
import com.ottogroup.appkit.nativeui.model.domain.Basket
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.ProductReviews
import com.ottogroup.appkit.nativeui.model.domain.Wishlist
import com.ottogroup.appkit.nativeui.util.NativeLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onSubscription
import kotlinx.coroutines.launch

internal interface LascanaNativeApi : NativeApi

internal class LascanaNativeApiImpl(
    private val apolloRepository: ApolloRepository,
    private val wishlistApi: LascanaWishlistApi,
    private val basketApi: LascanaBasketApi,
    private val coroutineScope: CoroutineScope,
    private val modelMapping: ModelMapping = ModelMapping(
        fuseNonColorDimensionsInto = ModelMapping.FUSED_SIZE_DIMENSION_NAME
    ),
) : LascanaNativeApi {
    override fun getMinimalProduct(id: String): Flow<Result<Product>> {
        return apolloRepository.queryWithCache(MinimalProductQuery(id))
            .map { it.map { modelMapping.toProduct(it.product.minimalProduct) } }
    }

    override fun getReviews(id: String): Flow<Result<ProductReviews>> {
        return apolloRepository.queryWithCache(ProductReviewsQuery(id))
            .map { it.map { modelMapping.toProductReviews(it.product) } }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun getProduct(
        id: String,
        resolveVariant: Boolean,
        includeShopTheLook: Boolean,
        cachePolicy: NativeApi.CachePolicy,
        overrideProductId: String?,
    ): Flow<Result<Product>> {
        if (cachePolicy != NativeApi.CachePolicy.CacheOnly) {
            // Skip wishlist update when only returning a cached product. There will be a non-cached request as well.
            coroutineScope.launch {
                updateWishlist()
            }
        }

        val fetch = benchmark()
        val query = ProductDetailQuery(id, includeShopTheLook)
        val flow: Flow<Result<Product>> =
            apolloRepository.queryWithCache(
                query,
                cachePolicy.toFetchPolicy()
            ).flatMapLatest { result ->
                fetch.log("Requested product", NativeLogger)
                when (result) {
                    is Result.Failure -> {
                        flowOf(Result.Failure(result.failure))
                    }

                    is Result.Success -> {
                        val data = result.value
                        if (data.product.isMaster) {
                            if (resolveVariant) {
                                getPrimaryVariantOfMaster(data.product, includeShopTheLook, cachePolicy)
                            } else {
                                flowOf(Result.Success(data.product))
                            }
                        } else {
                            enrichVariantWithSiblingVariants(data.product, cachePolicy)
                        }
                    }
                }
            }.map { result ->
                result.map { productData ->
                    modelMapping.toProduct(
                        if (overrideProductId != null) {
                            productData.copy(id = overrideProductId)
                        } else {
                            productData
                        }
                    )
                }
            }
        return flow.onEach {
            fetch.log("Complete product", NativeLogger)
        }
    }

    private fun getPrimaryVariantOfMaster(
        masterProduct: ProductDetailQuery.Product,
        includeShopTheLook: Boolean,
        cachePolicy: NativeApi.CachePolicy,
    ): Flow<Result<ProductDetailQuery.Product>> {
        val primaryVariantId = masterProduct.findPrimaryVariantId
        if (primaryVariantId == null) {
            NativeLogger.w("Master product ${masterProduct.id} contains no variants. Returned data will be incomplete.")
            return flowOf(Result.Success(masterProduct))
        }

        val fetch = benchmark()
        return apolloRepository.queryWithCache(
            query = ProductDetailQuery(primaryVariantId, includeShopTheLook),
            fetchPolicy = cachePolicy.toFetchPolicy(),
        ).map { primaryVariantResult ->
            primaryVariantResult.map {
                val primaryVariantWithAllVariants = it.copy(
                    product = it.product.copy(variants = masterProduct.variants)
                )
                primaryVariantWithAllVariants.product
            }
        }.onEach {
            fetch.log("Primary variant", NativeLogger)
        }
    }

    private fun enrichVariantWithSiblingVariants(
        variantProduct: ProductDetailQuery.Product,
        cachePolicy: NativeApi.CachePolicy,
    ): Flow<Result<ProductDetailQuery.Product>> {
        val cachePolicy = if (cachePolicy == NativeApi.CachePolicy.CacheOnly) {
            cachePolicy
        } else {
            // skip additional network requests if we already know the master
            // this brings the danger of serving stale variant data, but in most cases it will
            // save on network requests and refreshes.
            NativeApi.CachePolicy.CacheFirst
        }

        val parentId = variantProduct.parentId
        if (parentId.isBlank()) {
            return flowOf(
                Result.Failure(IllegalStateException("Product is variant but has no parent ID."))
            )
        }

        val fetch = benchmark()
        return apolloRepository.queryWithCache(
            query = MasterProductVariantsQuery(parentId),
            fetchPolicy = cachePolicy.toFetchPolicy(),
        ).map { parentResult ->
            parentResult.map { parent ->
                // If the parent product reports no variants, at least include the variant we already know.
                val variants = if (parent.product.variants.isNotEmpty()) {
                    parent.product.variants
                } else {
                    listOf(variantProduct.asVariant())
                }

                val variantWithAllVariants = variantProduct.copy(
                    variants = variants.map { it.asDetailQueryVariant() }
                )
                variantWithAllVariants
            }
        }.onEach {
            fetch.log("Sibling variants", NativeLogger)
        }
    }

    private val cachedWishlist = MutableSharedFlow<Wishlist>(replay = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)

    private suspend fun updateWishlist() {
        wishlistApi.getWishlist().getOrNull()?.let {
            cachedWishlist.emit(it)
        }
    }

    override fun getWishlist(): Flow<Result<Wishlist>> {
        return cachedWishlist
            .onSubscription {
                // ensure we have fresh wishlist data when first subscribing
                updateWishlist()
            }
            .map {
                Result.Success(it)
            }
            .distinctUntilChanged()
    }

    override suspend fun addProductToWishlist(id: String): Result<Wishlist> {
        return wishlistApi.addToWishlist(id).also {
            if (it is Result.Success) cachedWishlist.emit(it.value)
        }
    }

    override suspend fun removeProductFromWishlist(id: String): Result<Wishlist> {
        return wishlistApi.removeFromWishlist(id).also {
            if (it is Result.Success) cachedWishlist.emit(it.value)
        }
    }

    override fun isProductOnWishlist(id: String): Flow<Result<Boolean>> {
        return cachedWishlist.map { wishlist ->
            Result.Success(wishlist.items.any { it.id == id })
        }
    }

    private val cachedBasket = MutableSharedFlow<Basket>(replay = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)

    private suspend fun updateBasket() {
        basketApi.getBasket().getOrNull()?.let {
            cachedBasket.emit(it)
        }
    }

    override fun getBasket(): Flow<Result<Basket>> {
        return cachedBasket
            .onSubscription {
                // ensure we have fresh basket data when first subscribing
                updateBasket()
            }
            .map {
                Result.Success(it)
            }
            .distinctUntilChanged()
    }

    override suspend fun addProductToBasket(id: String): Result<Basket> {
        return basketApi.addToBasket(id).also {
            if (it is Result.Success) cachedBasket.emit(it.value)
        }
    }

    override suspend fun addVoucherToBasket(id: String, customName: String?): Result<Basket> {
        return basketApi.addToBasket(id, customName).also {
            if (it is Result.Success) cachedBasket.emit(it.value)
        }
    }
}

private fun ProductDetailQuery.Product.asVariant(): MasterProductVariantsQuery.Variant {
    return MasterProductVariantsQuery.Variant(
        __typename = __typename,
        id = id,
        variantInfo = VariantInfo(
            __typename = __typename,
            id = id,
            sku = sku,
            variantValues = variantValues,
            availabilityFields = AvailabilityFields(
                __typename = availabilityFields.__typename,
                deliveryInformation = availabilityFields.deliveryInformation,
                stock = availabilityFields.stock,
                flyouts = availabilityFields.flyouts,
                id = availabilityFields.id,
            ),
            imageGallery = VariantInfo.ImageGallery(
                __typename = imageGallery.__typename,
                thumb = imageGallery.thumb
            ),
            completePrice = completePrice,
            positionedPicture = positionedPicture,
        ),
    )
}

private fun MasterProductVariantsQuery.Variant.asDetailQueryVariant(): ProductDetailQuery.Variant {
    return ProductDetailQuery.Variant(
        __typename = __typename,
        id = id,
        variantInfo = variantInfo,
    )
}

private val ProductDetailQuery.Product.isMaster: Boolean get() = parentId.isBlank()
private val ProductDetailQuery.Product.findPrimaryVariantId: String?
    get() = variants
        .sortedWith(
            compareBy(
                {
                    // sort unavailable variants to the end
                    if (it.variantInfo.availabilityFields.stock.stockStatus == ModelMapping.STOCK_STATUS_NOT_IN_STOCK) {
                        1
                    } else {
                        -1
                    }
                },
                {
                    // sort expensive variants to the end
                    it.variantInfo.completePrice.price.priceFields.price
                }
            )
        )
        .firstOrNull()?.id
