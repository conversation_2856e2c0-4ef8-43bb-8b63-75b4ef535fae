package com.ottogroup.appkit.nativeui.config

import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow

internal class OGNativeConfigProvider {
    private val _configState = MutableStateFlow<OGNativeConfig>(OGNativeConfig.None())
    val configState = _configState.asStateFlow()

    fun update(config: OGNativeConfig) {
        _configState.value = config
    }
}

internal fun <T : OGNativeConfig> OGNativeConfigProvider.config(): T = configState.value as T
