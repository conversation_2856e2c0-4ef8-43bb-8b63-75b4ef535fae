package com.ottogroup.appkit.nativeui.api.witt

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.map
import com.ottogroup.appkit.nativeui.api.ApolloRepository
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.config.config
import com.ottogroup.appkit.nativeui.config.safeLocale
import com.ottogroup.appkit.nativeui.model.domain.Basket
import com.ottogroup.appkit.nativeui.witt.AddItemToBasketMutation
import com.ottogroup.appkit.nativeui.witt.GetBasketQuery

internal interface WittBasketApi {
    suspend fun getBasket(): Result<Basket>
    suspend fun addToBasket(id: String): Result<Basket>
}

internal class WittBasketApiImpl(
    private val apolloRepository: ApolloRepository,
    private val configProvider: OGNativeConfigProvider,
) : WittBasketApi {

    private val config get() = configProvider.config<OGNativeConfig.Witt>()

    override suspend fun getBasket(): Result<Basket> {
        val locale = configProvider.config<OGNativeConfig.Witt>().safeLocale
        return when (val headers = config.bearerTokenHeaders()) {
            is Result.Failure -> Result.Failure(headers.failure)
            is Result.Success -> {
                apolloRepository.query(GetBasketQuery(locale), httpHeaders = headers.value).map { data ->
                    Basket(
                        data.basket.items.map { apiItem ->
                            Basket.Item(
                                id = apiItem.id,
                                amount = apiItem.quantity
                            )
                        }
                    )
                }
            }
        }
    }

    override suspend fun addToBasket(id: String): Result<Basket> {
        val parsedId = WittProductId.from(id)

        if (!parsedId.isComplete()) {
            return Result.Failure(
                IllegalArgumentException("Invalid product ID format: $id")
            )
        }

        return when (val headers = config.bearerTokenHeaders()) {
            is Result.Failure -> Result.Failure(headers.failure)
            is Result.Success -> {
                apolloRepository.mutate(
                    AddItemToBasketMutation(
                        displayNumber = parsedId.displayNumber.orEmpty(),
                        variantId = parsedId.variantId.orEmpty(),
                        productId = parsedId.productId,
                        promotion = parsedId.promotion.orEmpty(),
                        locale = config.safeLocale
                    ),
                    httpHeaders = headers.value
                ).map { data ->
                    Basket(
                        data.addItemsToBasket.items.map { apiItem ->
                            Basket.Item(
                                id = apiItem.id,
                                amount = apiItem.quantity
                            )
                        }
                    )
                }
            }
        }
    }
}
