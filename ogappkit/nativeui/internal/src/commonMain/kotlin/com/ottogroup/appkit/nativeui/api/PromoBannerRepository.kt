package com.ottogroup.appkit.nativeui.api

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.nativeui.api.contentful.ContentfulRepository
import com.ottogroup.appkit.nativeui.api.dynamicyield.DynamicYieldRepository
import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldPromoBanner
import com.ottogroup.appkit.nativeui.model.ui.PromoBanner
import com.ottogroup.appkit.tracking.event.ECommerceItem
import kotlinx.coroutines.flow.Flow

internal interface PromoBannerRepository {
    fun getDynamicYieldBanner(
        productSku: String,
        eCommerceItem: ECommerceItem,
        config: DynamicYieldPromoBanner.Config,
    ): Flow<Operation<PromoBanner.Content>>

    fun getContentfulBanner(): Flow<Operation<PromoBanner.Content>>
}

internal class PromoBannerRepositoryImpl(
    private val dynamicYieldRepository: DynamicYieldRepository,
    private val contentfulRepository: ContentfulRepository,
) : PromoBannerRepository {

    override fun getDynamicYieldBanner(
        productSku: String,
        eCommerceItem: ECommerceItem,
        config: DynamicYieldPromoBanner.Config,
    ): Flow<Operation<PromoBanner.Content>> {
        return dynamicYieldRepository.getBanner(productSku, eCommerceItem, config)
    }

    override fun getContentfulBanner(): Flow<Operation<PromoBanner.Content>> {
        return contentfulRepository.getBanner()
    }
}
