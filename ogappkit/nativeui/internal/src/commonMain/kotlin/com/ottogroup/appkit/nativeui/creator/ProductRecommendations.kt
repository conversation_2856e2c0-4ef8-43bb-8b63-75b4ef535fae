package com.ottogroup.appkit.nativeui.creator

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.withInitialValue
import com.ottogroup.appkit.nativeui.api.RecommendationsRepository
import com.ottogroup.appkit.nativeui.model.domain.Dimension
import com.ottogroup.appkit.nativeui.model.domain.MoreFromTheSeries
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.ShopTheLook
import com.ottogroup.appkit.nativeui.model.ui.AddToBasketSuccessScreen
import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldRecommendations
import com.ottogroup.appkit.nativeui.model.ui.GkAirRecommendations
import com.ottogroup.appkit.nativeui.model.ui.LoadingComponent
import com.ottogroup.appkit.nativeui.model.ui.MoreFromTheSeriesRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendationsConfig
import com.ottogroup.appkit.nativeui.model.ui.RecentlyViewedRecommendations
import com.ottogroup.appkit.nativeui.model.ui.Screen
import com.ottogroup.appkit.nativeui.model.ui.ShopTheLookRecommendations
import com.ottogroup.appkit.nativeui.model.ui.StaticProductRecommendations
import com.ottogroup.appkit.nativeui.util.safeParentId
import kotlin.reflect.KClass
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map

@OptIn(ExperimentalCoroutinesApi::class)
internal fun Flow<Product>.createProductRecommendations(
    config: ProductRecommendationsConfig,
    recommendationsRepository: RecommendationsRepository,
    screenContext: KClass<out Screen<*>>,
): Flow<ProductRecommendations<*>?> {
    data class ProductLens(
        val id: String,
        val safeParentId: String,
        val sku: String,
        val shopTheLook: ShopTheLook?,
        val moreFromTheSeries: MoreFromTheSeries?,
        val categoryId: String? = null,
        val fusedDimensions: List<Dimension>,
    )
    return flatMapWithLens(
        mapping = { ProductLens(it.id, it.safeParentId, it.sku, it.shopTheLook, it.moreFromTheSeries, it.categoryId, it.fusedDimensions) },
    ) { lens ->
        val recoOperationFlow = when (config) {
            is RecentlyViewedRecommendations.Config -> recommendationsRepository.getRecentlyViewedRecommendations(
                lens.safeParentId,
                config
            )

            is StaticProductRecommendations.Config -> recommendationsRepository.getStaticRecommendations(config)
            is DynamicYieldRecommendations.Config -> recommendationsRepository.getDynamicYieldRecommendations(
                lens.sku,
                config,
            )

            is GkAirRecommendations.Config -> recommendationsRepository.getGkAirRecommendations(
                productId = lens.id,
                categoryId = lens.categoryId ?: "",
                fusedDimensions = lens.fusedDimensions,
                isAddToBasketSuccessScreen = screenContext == AddToBasketSuccessScreen::class,
                config = config,
            )

            is ShopTheLookRecommendations.Config -> recommendationsRepository.getShopTheLookRecommendations(
                lens.shopTheLook,
                config,
            )

            is MoreFromTheSeriesRecommendations.Config -> recommendationsRepository.getMoreFromTheSeriesRecommendations(
                lens.moreFromTheSeries,
                config,
            )
        }

        recoOperationFlow.map { recoOperation ->
            val state = when (recoOperation) {
                is Operation.Complete -> when (val result = recoOperation.result) {
                    is Result.Failure -> {
                        null
                    }

                    is Result.Success -> {
                        LoadingComponent.State.Done(result.value.copy(products = result.value.products.take(config.maxEntries)))
                    }
                }

                Operation.InProgress -> {
                    LoadingComponent.State.Loading(
                        if (config is ShopTheLookRecommendations.Config) {
                            Placeholders.shopTheLookRecommendations
                        } else {
                            Placeholders.productRecommendations
                        }
                    )
                }
            }

            if (state == null || state is LoadingComponent.State.Done && state.content.products.isEmpty()) return@map null

            when (config) {
                is RecentlyViewedRecommendations.Config -> RecentlyViewedRecommendations(
                    state = state,
                    config = config,
                )

                is StaticProductRecommendations.Config -> StaticProductRecommendations(
                    state = state,
                    config = config,
                )

                is DynamicYieldRecommendations.Config -> DynamicYieldRecommendations(
                    state = state,
                    config = config,
                )

                is GkAirRecommendations.Config -> GkAirRecommendations(
                    state = state,
                    config = config,
                )

                is ShopTheLookRecommendations.Config -> ShopTheLookRecommendations(
                    state = state,
                    config = config,
                )

                is MoreFromTheSeriesRecommendations.Config -> MoreFromTheSeriesRecommendations(
                    state = state,
                    config = config,
                )
            }
        }
    }.withInitialValue(
        when (config) {
            is RecentlyViewedRecommendations.Config -> RecentlyViewedRecommendations(
                state = LoadingComponent.State.Loading(Placeholders.productRecommendations),
                config = config,
            )

            is StaticProductRecommendations.Config -> StaticProductRecommendations(
                state = LoadingComponent.State.Loading(Placeholders.productRecommendations),
                config = config,
            )

            is DynamicYieldRecommendations.Config -> DynamicYieldRecommendations(
                state = LoadingComponent.State.Loading(Placeholders.productRecommendations),
                config = config,
            )

            is GkAirRecommendations.Config -> GkAirRecommendations(
                state = LoadingComponent.State.Loading(Placeholders.productRecommendations),
                config = config,
            )

            is ShopTheLookRecommendations.Config -> ShopTheLookRecommendations(
                state = LoadingComponent.State.Loading(Placeholders.shopTheLookRecommendations),
                config = config,
            )

            is MoreFromTheSeriesRecommendations.Config -> MoreFromTheSeriesRecommendations(
                state = LoadingComponent.State.Loading(Placeholders.productRecommendations),
                config = config,
            )
        }
    )
}
