package com.ottogroup.appkit.nativeui.api.witt

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.benchmark
import com.ottogroup.appkit.base.combineOrEmpty
import com.ottogroup.appkit.base.getOrNull
import com.ottogroup.appkit.base.log
import com.ottogroup.appkit.base.map
import com.ottogroup.appkit.nativeui.api.ApolloRepository
import com.ottogroup.appkit.nativeui.api.NativeApi
import com.ottogroup.appkit.nativeui.api.toFetchPolicy
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.config.config
import com.ottogroup.appkit.nativeui.config.safeLocale
import com.ottogroup.appkit.nativeui.model.domain.Basket
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.ProductReviews
import com.ottogroup.appkit.nativeui.model.domain.Wishlist
import com.ottogroup.appkit.nativeui.util.NativeLogger
import com.ottogroup.appkit.nativeui.witt.GetEuImportersQuery
import com.ottogroup.appkit.nativeui.witt.ProductDetailQuery
import com.ottogroup.appkit.nativeui.witt.ProductReviewsQuery
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.onSubscription
import kotlinx.coroutines.launch

internal interface WittNativeApi : NativeApi

internal class WittNativeApiImpl(
    private val apolloRepository: ApolloRepository,
    private val basketApi: WittBasketApi,
    private val wishlistApi: WittWishlistApi,
    private val configProvider: OGNativeConfigProvider,
    private val modelMapping: WittModelMapping,
    private val coroutineScope: CoroutineScope,
) : WittNativeApi {

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun getProduct(
        id: String,
        resolveVariant: Boolean,
        includeShopTheLook: Boolean,
        cachePolicy: NativeApi.CachePolicy,
        overrideProductId: String?
    ): Flow<Result<Product>> {
        if (cachePolicy != NativeApi.CachePolicy.CacheOnly) {
            // Skip wishlist update when only returning a cached product. There will be a non-cached request as well.
            coroutineScope.launch {
                updateWishlist()
            }
        }

        val fetch = benchmark()
        val locale = configProvider.config<OGNativeConfig.Witt>().safeLocale
        val parsedId = WittProductId.from(id)
        val productId = parsedId.productId
        val query = ProductDetailQuery(productId, locale)
        val flow = apolloRepository.queryWithCache(
            query,
            cachePolicy.toFetchPolicy()
        ).flatMapLatest { result ->
            when (result) {
                is Result.Failure -> {
                    flowOf(Result.Failure(result.failure))
                }

                is Result.Success -> {
                    val siblings = result.value.productBy.siblings.orEmpty()

                    siblings.filter<ProductDetailQuery.Sibling> { it.id != productId }
                        .map<ProductDetailQuery.Sibling, Flow<Result<ProductDetailQuery.Data>>> { sibling ->
                            apolloRepository.queryWithCache<ProductDetailQuery.Data>(
                                ProductDetailQuery(sibling.id, locale),
                                cachePolicy.toFetchPolicy()
                            )
                        }
                        .combineOrEmpty<Result<ProductDetailQuery.Data>>()
                        .map<List<Result<ProductDetailQuery.Data>>, Result.Success<List<ProductDetailQuery.Data>>> { siblingResults ->
                            Result.Success<List<ProductDetailQuery.Data>>(listOf<ProductDetailQuery.Data>(result.value) + siblingResults.mapNotNull<Result<ProductDetailQuery.Data>, ProductDetailQuery.Data> { it.getOrNull<ProductDetailQuery.Data>() })
                        }
                }
            }
        }.flatMapLatest { result ->
            when (result) {
                is Result.Failure -> {
                    flowOf(Result.Failure(result.failure))
                }

                is Result.Success -> {
                    val data = result.value
                    val primaryProduct = data.first()
                    val productData = primaryProduct.productBy

                    /* For Witt, we need to request the EU importer on a dimension-ID basis. Perhaps different sizes
                     * sometimes have different importers?
                     * The web shop solves this by only requesting the data when the user clicks the respective UI
                     * element. For our current UI concept, we need to pre-fetch it. So we request the data for all
                     * dimensions, filter out duplicates and then show them all at once, not per dimension.
                     * This is not ideal, because we add multiple queries at product load time for data that most
                     * people do not care about. Also, it might not be legal if it is unclear which importer is for
                     * which dimension?
                     */
                    val dimensionIds = productData.dimensions.map { it.id }
                    val euImportersFlow = dimensionIds.map {
                        apolloRepository.queryWithCache(
                            query = GetEuImportersQuery(dimensionId = it, locale = locale),
                            fetchPolicy = cachePolicy.toFetchPolicy(),
                        )
                    }.combineOrEmpty()
                        .map { results ->
                            results.mapNotNull { it.getOrNull() }.distinct()
                        }

                    euImportersFlow.map<List<GetEuImportersQuery.Data>, Result.Success<Product>> { euImporters ->
                        Result.Success<Product>(
                            modelMapping.toProduct(
                                queryProduct = productData,
                                siblings = data.drop<ProductDetailQuery.Data>(1)
                                    .map<ProductDetailQuery.Data, ProductDetailQuery.ProductBy> { it.productBy },
                                // all siblings share the same reviews and rating stats
                                reviews = primaryProduct.productRatings.orEmpty<ProductDetailQuery.ProductRating>(),
                                rating = primaryProduct.productRatingStatistics,
                                requestedId = overrideProductId ?: id,
                                euImporters = euImporters.flatMap { it.euImporterByProduct.orEmpty() },
                            )
                        )
                    }
                }
            }
        }
        return flow.onEach {
            fetch.log("Complete product", NativeLogger)
        }
    }

    override fun getMinimalProduct(id: String): Flow<Result<Product>> {
        // TODO
        return emptyFlow()
    }

    override fun getReviews(id: String): Flow<Result<ProductReviews>> {
        val parsedId = WittProductId.from(id)
        val locale = configProvider.config<OGNativeConfig.Witt>().safeLocale
        return apolloRepository.queryWithCache(ProductReviewsQuery(parsedId.productId, locale))
            .map { it.map { modelMapping.toProductReviews(it) } }
    }

    private val cachedWishlist = MutableSharedFlow<Wishlist>(replay = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)

    private suspend fun updateWishlist() {
        wishlistApi.getWishlist().getOrNull()?.let {
            cachedWishlist.emit(it)
        }
    }

    override fun getWishlist(): Flow<Result<Wishlist>> {
        return cachedWishlist
            .onSubscription {
                // ensure we have fresh wishlist data when first subscribing
                updateWishlist()
            }
            .map {
                Result.Success(it)
            }
            .distinctUntilChanged()
    }

    override suspend fun addProductToWishlist(id: String): Result<Wishlist> {
        return wishlistApi.addToWishlist(id).also {
            if (it is Result.Success) cachedWishlist.emit(it.value)
        }
    }

    override suspend fun removeProductFromWishlist(id: String): Result<Wishlist> {
        val parsedId = WittProductId.from(id)
        val item = cachedWishlist.firstOrNull()?.let { wishlist ->
            wishlist.items.firstOrNull { it.productId == parsedId.productId }
        }
        return wishlistApi.removeFromWishlist(item?.id ?: id).also {
            if (it is Result.Success) cachedWishlist.emit(it.value)
        }
    }

    override fun isProductOnWishlist(id: String): Flow<Result<Boolean>> {
        val parsedId = WittProductId.from(id)
        return cachedWishlist.map { wishlist ->
            Result.Success(wishlist.items.any { it.productId == parsedId.productId })
        }
    }

    private val cachedBasket = MutableSharedFlow<Basket>(replay = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)

    private suspend fun updateBasket() {
        basketApi.getBasket().getOrNull()?.let {
            cachedBasket.emit(it)
        }
    }

    override fun getBasket(): Flow<Result<Basket>> {
        return cachedBasket
            .onSubscription {
                // ensure we have fresh basket data when first subscribing
                updateBasket()
            }
            .map {
                Result.Success(it)
            }
            .distinctUntilChanged()
    }

    override suspend fun addProductToBasket(id: String): Result<Basket> {
        return basketApi.addToBasket(id).also {
            if (it is Result.Success) cachedBasket.emit(it.value)
        }
    }

    override suspend fun addVoucherToBasket(
        id: String,
        customName: String?
    ): Result<Basket> {
        // TODO
        return Result.Failure(NotImplementedError())
    }
}
