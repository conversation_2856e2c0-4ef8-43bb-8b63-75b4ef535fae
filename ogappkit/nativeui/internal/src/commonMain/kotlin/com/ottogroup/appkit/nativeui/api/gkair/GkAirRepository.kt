package com.ottogroup.appkit.nativeui.api.gkair

import co.touchlab.stately.collections.ConcurrentMutableMap
import com.apollographql.apollo.api.Query
import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.base.combineOrEmpty
import com.ottogroup.appkit.base.getOrElse
import com.ottogroup.appkit.base.resultFor
import com.ottogroup.appkit.nativeui.api.ApolloRepository
import com.ottogroup.appkit.nativeui.api.WishlistRepository
import com.ottogroup.appkit.nativeui.api.witt.WittModelMapping
import com.ottogroup.appkit.nativeui.api.witt.WittProductId
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.config.config
import com.ottogroup.appkit.nativeui.config.safeLocale
import com.ottogroup.appkit.nativeui.model.domain.Dimension
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.singleDefaultDimension
import com.ottogroup.appkit.nativeui.model.ui.GkAirRecommendations
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendations
import com.ottogroup.appkit.nativeui.witt.GkKAirRecosAfterAddToBasketQuery
import com.ottogroup.appkit.nativeui.witt.GkKAirRecosOnPdpQuery
import com.ottogroup.appkit.nativeui.witt.fragment.GkAirOutputFields
import com.ottogroup.appkit.tracking.consent.OGTrackingConsent
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.shareIn

internal interface GkAirRepository {
    fun getGkAirRecommendations(
        productId: String,
        categoryId: String,
        fusedDimensions: List<Dimension>,
        isAddToBasketSuccessScreen: Boolean,
        config: GkAirRecommendations.Config,
    ): Flow<Operation<ProductRecommendations.Content>>
}

internal class GkAirRepositoryImpl(
    private val apolloRepository: ApolloRepository,
    private val configProvider: OGNativeConfigProvider,
    private val wishlistRepository: WishlistRepository,
    private val modelMapping: WittModelMapping,
    private val trackingConsent: OGTrackingConsent,
    private val coroutineScope: CoroutineScope,
) : GkAirRepository {

    override fun getGkAirRecommendations(
        productId: String,
        categoryId: String,
        fusedDimensions: List<Dimension>,
        isAddToBasketSuccessScreen: Boolean,
        config: GkAirRecommendations.Config
    ): Flow<Operation<ProductRecommendations.Content>> {
        val parsedId = WittProductId.from(productId)
        val size = fusedDimensions.singleDefaultDimension()?.values?.find { it.productId == productId }?.text

        return if (isAddToBasketSuccessScreen) {
            getRecommendationsAfterAddToBasket(
                productId = parsedId.productId,
                categoryId = categoryId,
                size = size ?: "",
                config = config
            )
        } else {
            getRecommendationsOnPdp(
                productId = parsedId.productId,
                categoryId = categoryId,
                config = config
            )
        }
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private fun getRecommendationsOnPdp(
        productId: String,
        categoryId: String,
        config: GkAirRecommendations.Config,
    ): Flow<Operation<ProductRecommendations.Content>> {
        return getRecommendations(
            query = { sessionId, locale, trackingConsent ->
                GkKAirRecosOnPdpQuery(
                    productId = productId,
                    categoryId = "C$categoryId",
                    sessionId = sessionId,
                    locale = locale,
                    trackingConsent = trackingConsent,
                )
            },
            unpackResponse = { data ->
                data.gkAir.map { it.gkAirOutputFields }
            },
            config = config,
        )
    }

    private fun getRecommendationsAfterAddToBasket(
        productId: String,
        categoryId: String,
        size: String,
        config: GkAirRecommendations.Config
    ): Flow<Operation<ProductRecommendations.Content>> {
        return getRecommendations(
            query = { sessionId, locale, trackingConsent ->
                GkKAirRecosAfterAddToBasketQuery(
                    productId = productId,
                    categoryId = "C$categoryId",
                    size = size,
                    sessionId = sessionId,
                    locale = locale,
                    trackingConsent = trackingConsent,
                )
            },
            unpackResponse = { data ->
                data.gkAir.map { it.gkAirOutputFields }
            },
            config = config,
        )
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    private fun <D : Query.Data> getRecommendations(
        query: (sessionId: String, locale: String, trackingConsent: Boolean) -> Query<D>,
        unpackResponse: (D) -> List<GkAirOutputFields>,
        config: GkAirRecommendations.Config,
    ): Flow<Operation<ProductRecommendations.Content>> {
        return flow {
            val result = resultFor {
                val featureConfig = configProvider.config<OGNativeConfig.Witt>()
                val url = featureConfig.cookies.url
                val cookieName = featureConfig.cookies.recoSessionIdCookieName
                requireNotNull(
                    featureConfig.cookiesBridge.getCookies(url)[cookieName]
                ) { "Cookie $cookieName not found for URL $url" }
            }
            emit(result)
        }.flatMapLatest { sessionIdResult ->
            val sessionId = when (sessionIdResult) {
                is Result.Failure -> return@flatMapLatest flowOf(
                    Result.Failure(sessionIdResult.failure)
                )

                is Result.Success -> sessionIdResult.value
            }
            val locale = configProvider.config<OGNativeConfig.Witt>().safeLocale

            debounceQuery(
                query = query(
                    sessionId,
                    locale,
                    trackingConsent.consentForService(OGTrackingServiceId.GkAir).first()
                )
            )
        }.flatMapLatest { result ->
            when (result) {
                is Result.Failure -> flowOf(Result.Failure(result.failure))
                is Result.Success -> {
                    val gkAirs = unpackResponse(result.value)
                    val reco = gkAirs.find { it.outputId == config.outputId }
                        ?: return@flatMapLatest flowOf(
                            Result.Failure<ProductRecommendations.Content>(
                                IllegalStateException("GKAir output does not contain requested outputId: ${config.outputId}")
                            )
                        )
                    val recommendedProducts: Flow<Result<ProductRecommendations.Content>> =
                        reco.products.map { reco ->
                            val product = reco.product
                            wishlistRepository.isProductOnWishlist(product.id)
                                .map { wishlistResult ->
                                    ProductRecommendations.RecommendedProduct(
                                        productId = product.id,
                                        secondaryId = null,
                                        brandName = product.brand?.name,
                                        title = product.name,
                                        price = with(modelMapping) { product.price?.productPriceFields?.toPrice() },
                                        image = product.images?.firstOrNull()?.imageFields?.hash?.let { hash ->
                                            with(modelMapping) {
                                                Image(url = hash.recoImageUrl, thumbnailUrl = hash.thumbnailUrl)
                                            }
                                        } ?: Image("", ""),
                                        isWishlisted = wishlistResult.getOrElse(false),
                                        productIdForWishlisting = product.id,
                                    )
                                }
                        }.combineOrEmpty()
                            .map { recommendedProducts ->
                                Result.Success(
                                    ProductRecommendations.Content(
                                        products = recommendedProducts,
                                        image = null,
                                        trackingId = "GKAir_${reco.title?.replace(" ", "_") ?: reco.outputId}",
                                        title = reco.title,
                                    )
                                )
                            }
                    recommendedProducts
                }
            }
        }.asOperation()
    }

    private val inFlightQueries = ConcurrentMutableMap<Query<*>, Flow<Result<*>>>()

    /**
     * Debounces the execution of a query by storing it in a map of in-flight
     * queries. If the same query is requested again within the debounce
     * period, it will return the existing response flow instead of executing
     * the query again.
     *
     * This is useful because the GK Air response actually contains all the
     * possible recommendation outputs, and we simply have to filter out each
     * one we want from the same response.
     */
    private fun <D : Query.Data> debounceQuery(
        query: Query<D>
    ): Flow<Result<D>> {
        @Suppress("UNCHECKED_CAST")
        return inFlightQueries.getOrPut(query) {
            flow {
                emit(apolloRepository.query(query = query))
                delay(DEBOUNCE_MS)
                inFlightQueries.remove(query)
            }.shareIn(
                scope = coroutineScope,
                started = SharingStarted.WhileSubscribed(stopTimeoutMillis = DEBOUNCE_MS),
                replay = 1,
            )
        } as Flow<Result<D>>
    }

    private companion object {
        const val DEBOUNCE_MS = 1000L
    }
}
