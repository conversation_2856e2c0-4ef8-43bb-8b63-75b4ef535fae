package com.ottogroup.appkit.nativeui.creator.productdetail

import com.ottogroup.appkit.base.getOrElse
import com.ottogroup.appkit.base.getOrNull
import com.ottogroup.appkit.base.withInitialValue
import com.ottogroup.appkit.base.awaitResult
import com.ottogroup.appkit.nativeui.api.WishlistRepository
import com.ottogroup.appkit.nativeui.api.NativeApi
import com.ottogroup.appkit.nativeui.api.contentful.ContentfulRepository
import com.ottogroup.appkit.nativeui.creator.Placeholders
import com.ottogroup.appkit.nativeui.creator.flatMapWithLens
import com.ottogroup.appkit.nativeui.creator.mapOrNull
import com.ottogroup.appkit.nativeui.model.domain.CompanyOfOrigin
import com.ottogroup.appkit.nativeui.model.domain.Dimension
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.SizeDimensionMatrix
import com.ottogroup.appkit.nativeui.model.domain.SizeTableData
import com.ottogroup.appkit.nativeui.model.domain.VoucherSpec
import com.ottogroup.appkit.nativeui.model.domain.singleDefaultDimension
import com.ottogroup.appkit.nativeui.model.ui.LoadingComponent
import com.ottogroup.appkit.nativeui.model.ui.ProductDimensions
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.combine

internal fun Flow<Product>.createProductDimensions(
    config: ProductDimensions.Config,
    wishlistRepository: WishlistRepository,
    contentfulRepository: ContentfulRepository,
): Flow<ProductDimensions?> {
    return flatMapWithLens(
        mapping = {
            ProductLens(
                id = it.id,
                individualDimensions = it.individualDimensions,
                fusedDimensions = it.fusedDimensions,
                sizeMatrix = it.sizeMatrix,
                voucherSpec = it.voucherSpec,
                sizeAdvisorUrl = it.sizeAdvisorUrl,
                productMkz = it.mkz,
                companyOfOrigin = it.companyOfOrigin,
            )
        }
    ) { lens ->
        if (lens.sizeMatrix == null && lens.individualDimensions.singleDefaultDimension() == null) {
            return@flatMapWithLens flowOf(null)
        }

        val styleToUse = config.longTitleStyle?.takeIf { hasLongTitles(lens, it.characterLimit) }?.style ?: config.style

        val wishlistFlow = wishlistRepository.isProductOnWishlist(lens.id)
        val sizeTableFlow = if (!lens.productMkz.isNullOrEmpty() && lens.companyOfOrigin != null) {
            contentfulRepository.getSizeTable(
                productMkz = lens.productMkz,
                companyOfOrigin = lens.companyOfOrigin,
                cachePolicy = NativeApi.CachePolicy.CacheFirst
            ).awaitResult().map { result ->
                result.getOrNull()
            }
        } else {
            flowOf(null)
        }

        combine(wishlistFlow, sizeTableFlow) { isOnWishlistResult, sizeTableData ->
            val isWishlisted = isOnWishlistResult.getOrElse(false)

            val content = if (lens.voucherSpec != null) {
                lens.individualDimensions.singleDefaultDimension()?.let {
                    ProductDimensions.VoucherDimension(
                        dimension = it.toProductDimension(lens),
                        customName = lens.voucherSpec.takeIf { it.allowsCustomName }?.let {
                            ProductDimensions.VoucherDimension.CustomName(
                                maxLength = it.maxCustomNameLength,
                            )
                        },
                        isWishlisted = isWishlisted,
                        productIdForWishlisting = lens.id,
                        sizeAdvisorUrl = lens.sizeAdvisorUrl,
                    )
                }
            } else {
                when (styleToUse) {
                    ProductDimensions.Config.Style.FLAT,
                    ProductDimensions.Config.Style.FLATCHIPS -> {
                        createFlatDimensions(lens, isWishlisted, sizeTableData)
                    }

                    ProductDimensions.Config.Style.NESTED,
                    ProductDimensions.Config.Style.CATEGORIZED -> {
                        /* If sizeMatrix is null, this means we do not have exactly two dimensions. The usual case of this
                         * happening is that the product has zero or one size dimensions. In this case, fall back to returning
                         * a flat list. */
                        if (lens.sizeMatrix == null) {
                            createFlatDimensions(lens, isWishlisted, sizeTableData)
                        } else {
                            ProductDimensions.NestedDimensions(
                                name = lens.sizeMatrix.parentDimensionName,
                                entries = lens.sizeMatrix.entries.map {
                                    ProductDimensions.NestedDimensions.Entry(
                                        name = it.name,
                                        dimension = it.child.toProductDimension(lens),
                                    )
                                },
                                isWishlisted = isWishlisted,
                                productIdForWishlisting = lens.id,
                                sizeAdvisorUrl = lens.sizeAdvisorUrl,
                                sizeTableData = sizeTableData
                            )
                        }
                    }
                }
            }
            content to styleToUse
        }
    }.mapOrNull { (content, styleToUse) ->
        // Filter out ProductDimensions component when it would only display a single entry.
        // In these cases, ProductVariant applies.
        val filteredContent = when (content) {
            is ProductDimensions.VoucherDimension -> content
            is ProductDimensions.NestedDimensions -> {
                if (content.entries.flatMap { it.dimension.variants }.size <= 1) {
                    null
                } else {
                    content
                }
            }

            is ProductDimensions.FlatDimension -> if (content.dimension.variants.size <= 1) {
                null
            } else {
                content
            }

            null -> null
        }

        filteredContent?.let { LoadingComponent.State.Done(it) to styleToUse }
    }.withInitialValue(LoadingComponent.State.Loading(Placeholders.productDimensions) to config.style)
        .mapOrNull { (loadingState, styleToUse) ->
            val updatedConfig = config.copy(style = styleToUse)
            ProductDimensions(loadingState, updatedConfig)
        }
}

private data class ProductLens(
    val id: String,
    val individualDimensions: List<Dimension>,
    val fusedDimensions: List<Dimension>,
    val sizeMatrix: SizeDimensionMatrix?,
    val voucherSpec: VoucherSpec?,
    val sizeAdvisorUrl: String?,
    val productMkz: String? = null,
    val companyOfOrigin: CompanyOfOrigin? = null
)

private fun Dimension.toProductDimension(lens: ProductLens) = ProductDimensions.ProductDimension(
    name = name,
    variants = values.map { value ->
        ProductDimensions.ProductDimension.VariantLink(
            name = value.text,
            productId = value.productId,
            availability = value.availability,
            price = value.price,
            isSelected = value.productId == lens.id,
        )
    }
)

private fun createFlatDimensions(
    lens: ProductLens,
    isWishlisted: Boolean,
    sizeTableData: SizeTableData?,
): ProductDimensions.Content? {
    val dimension: Dimension = lens.fusedDimensions.singleDefaultDimension() ?: return null
    return ProductDimensions.FlatDimension(
        dimension.toProductDimension(lens),
        isWishlisted = isWishlisted,
        productIdForWishlisting = lens.id,
        sizeAdvisorUrl = lens.sizeAdvisorUrl,
        sizeTableData = sizeTableData
    )
}

private fun hasLongTitles(lens: ProductLens, characterLimit: Int): Boolean {
    // Check fused dimensions first
    if (hasLongTitlesInDimensions(lens.fusedDimensions, characterLimit)) {
        return true
    }

    // Only check individual dimensions if they differ from fused dimensions
    if (lens.individualDimensions != lens.fusedDimensions &&
        hasLongTitlesInDimensions(lens.individualDimensions, characterLimit)
    ) {
        return true
    }

    // Only check size matrix if we haven't found long titles in dimensions yet.
    return lens.sizeMatrix?.entries?.any { entry ->
        "${entry.name} ${entry.child.name}".length >= characterLimit
    } == true
}

private fun hasLongTitlesInDimensions(dimensions: List<Dimension>, characterLimit: Int): Boolean {
    return dimensions
        .filter { it.type == Dimension.DimensionType.DEFAULT }
        .any { dimension ->
            dimension.values.any { value ->
                value.text.length >= characterLimit
            }
        }
}
