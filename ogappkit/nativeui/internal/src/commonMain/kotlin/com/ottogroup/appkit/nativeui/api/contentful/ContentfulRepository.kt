package com.ottogroup.appkit.nativeui.api.contentful

import com.apollographql.apollo.api.Optional
import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.nativeui.api.ApolloRepository
import com.ottogroup.appkit.nativeui.api.ContentfulApolloProvider
import com.ottogroup.appkit.nativeui.api.NativeApi
import com.ottogroup.appkit.nativeui.api.toFetchPolicy
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.config.config
import com.ottogroup.appkit.nativeui.config.safeLocale
import com.ottogroup.appkit.nativeui.model.domain.CompanyOfOrigin
import com.ottogroup.appkit.nativeui.model.domain.SizeTable
import com.ottogroup.appkit.nativeui.model.domain.SizeTableData
import com.ottogroup.appkit.nativeui.model.domain.SizeTableDetailsItem
import com.ottogroup.appkit.nativeui.model.ui.PromoBanner
import com.ottogroup.appkit.nativeui.wittContentful.BannerQuery
import com.ottogroup.appkit.nativeui.wittContentful.SizeTableCollectionQuery
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf

internal interface ContentfulRepository {
    fun getSizeTable(
        productMkz: String,
        companyOfOrigin: CompanyOfOrigin,
        cachePolicy: NativeApi.CachePolicy = NativeApi.CachePolicy.CacheFirst
    ): Flow<Operation<SizeTableData>>

    fun getBanner(cachePolicy: NativeApi.CachePolicy = NativeApi.CachePolicy.CacheFirst): Flow<Operation<PromoBanner.Content>>
}

internal class ContentfulRepositoryImpl(
    private val contentfulApolloProvider: ContentfulApolloProvider,
    private val apolloRepository: ApolloRepository,
    private val configProvider: OGNativeConfigProvider
) : ContentfulRepository {

    // TODO: If we keep direct access to Contentful, add token into Witt config and let app provide it.
    private val headers = mapOf("Authorization" to "Bearer 8PDnED2ulc_Od7o6_LokwH9DEh9jqqaU0FtO02Z3RuQ")

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun getSizeTable(
        productMkz: String,
        companyOfOrigin: CompanyOfOrigin,
        cachePolicy: NativeApi.CachePolicy
    ): Flow<Operation<SizeTableData>> {
        val locale = configProvider.config<OGNativeConfig.Witt>().safeLocale
        val query = SizeTableCollectionQuery(
            productMkz = Optional.present(productMkz),
            locale = Optional.present(locale),
            company = Optional.present(companyOfOrigin.name.lowercase())
        )

        return apolloRepository.queryWithCacheUsingClient(
            query,
            contentfulApolloProvider.apolloClient,
            cachePolicy.toFetchPolicy(),
            headers
        ).flatMapLatest { result ->
            when (result) {
                is Result.Failure -> flowOf(Result.Failure(result.failure))
                is Result.Success -> {
                    val sizeTableItem = result.value.sizeTableCollection?.items?.firstOrNull()
                    if (sizeTableItem != null) {
                        flowOf(Result.Success(sizeTableItem.toSizeTable()))
                    } else {
                        flowOf(Result.Failure(IllegalStateException("No size table found for productMkz: $productMkz")))
                    }
                }
            }
        }.asOperation()
    }

    @OptIn(ExperimentalCoroutinesApi::class)
    override fun getBanner(cachePolicy: NativeApi.CachePolicy): Flow<Operation<PromoBanner.Content>> {
        val locale = configProvider.config<OGNativeConfig.Witt>().safeLocale
        val query = BannerQuery(locale = Optional.present(locale))

        return apolloRepository.queryWithCacheUsingClient(
            query,
            contentfulApolloProvider.apolloClient,
            cachePolicy.toFetchPolicy(),
            headers
        ).flatMapLatest { result ->
            when (result) {
                is Result.Failure -> flowOf(Result.Failure<PromoBanner.Content>(result.failure))
                is Result.Success -> {
                    val bannerItem = result.value.bannerCollection?.items?.firstOrNull()
                    if (bannerItem != null) {
                        flowOf(Result.Success<PromoBanner.Content>(bannerItem.toBanner()))
                    } else {
                        flowOf(Result.Failure<PromoBanner.Content>(IllegalStateException("No banner available")))
                    }
                }
            }.asOperation()
        }
    }

    private fun SizeTableCollectionQuery.Item.toSizeTable(): SizeTableData {
        val extractedTables = content?.extractTablesWithHeadlinesFromJson() ?: emptyList()

        return SizeTableData(
            tables = extractedTables,
            detailsHeadline = measuringHeadline,
            detailsDescription = measuringIntroductionText,
            detailsImageUrl = measuringAsset?.url,
            detailsItems = measuringItemsCollection?.items?.mapNotNull { it?.toSizeTableDetailsItem() } ?: emptyList(),
            tipText = measuringTip?.tipText
        )
    }

    private fun SizeTableCollectionQuery.Content.extractTablesWithHeadlinesFromJson(): List<SizeTable> {
        return try {
            val jsonMap = json as? Map<*, *> ?: return emptyList()
            val content = jsonMap["content"] as? List<*> ?: return emptyList()

            val result = mutableListOf<SizeTable>()
            var currentHeadline: String? = null
            var currentTableDetailsItems: MutableList<SizeTableDetailsItem>? = null

            content.forEach { contentNode ->
                val nodeMap = contentNode as? Map<*, *> ?: return@forEach
                val nodeType = nodeMap["nodeType"] as? String

                when {
                    nodeType?.startsWith("heading-") == true -> {
                        val headlineText = extractHeadlineText(nodeMap)
                        if (!headlineText.isNullOrBlank()) {
                            currentHeadline = headlineText
                            // Reset table details items for new section
                            currentTableDetailsItems = mutableListOf()
                        }
                    }

                    nodeType == "paragraph" -> {
                        // Only collect paragraphs if we have a current headline and haven't hit a table yet
                        if (currentHeadline != null && currentTableDetailsItems != null) {
                            val paragraphDetails = extractParagraphDetails(nodeMap)
                            if (paragraphDetails != null) {
                                currentTableDetailsItems!!.add(paragraphDetails)
                            }
                        }
                    }

                    nodeType == "table" -> {
                        val tableData = extractTableData(nodeMap)
                        if (tableData.isNotEmpty()) {
                            result.add(
                                SizeTable(
                                    headline = currentHeadline,
                                    tableDetailsItems = currentTableDetailsItems?.takeIf { it.isNotEmpty() },
                                    table = tableData
                                )
                            )
                        }
                        // Reset for next table
                        currentTableDetailsItems = null
                    }
                }
            }

            result
        } catch (e: Exception) {
            emptyList()
        }
    }

    private fun extractHeadlineText(nodeMap: Map<*, *>): String? {
        return try {
            val nodeContent = nodeMap["content"] as? List<*> ?: return null
            nodeContent.mapNotNull { textNode ->
                val textMap = textNode as? Map<*, *> ?: return@mapNotNull null
                val textNodeType = textMap["nodeType"] as? String
                if (textNodeType == "text") {
                    textMap["value"] as? String
                } else null
            }.joinToString("").takeIf { it.isNotBlank() }
        } catch (e: Exception) {
            null
        }
    }

    private fun extractTableData(tableNode: Map<*, *>): Map<String, List<String>> {
        return try {
            val tableContent = tableNode["content"] as? List<*> ?: return emptyMap()

            // Extract headers from the first row
            val firstRow = tableContent.firstOrNull() as? Map<*, *> ?: return emptyMap()
            if (firstRow["nodeType"] as? String != "table-row") return emptyMap()

            val headerCells = firstRow["content"] as? List<*> ?: return emptyMap()

            // Check if we have table-header-cell nodes (first format)
            val headerCellNodes = headerCells.mapNotNull { cell ->
                val cellMap = cell as? Map<*, *> ?: return@mapNotNull null
                if (cellMap["nodeType"] as? String == "table-header-cell") {
                    extractTextFromTableCell(cellMap)
                } else null
            }

            if (headerCellNodes.isNotEmpty()) {
                // First format: table with table-header-cell nodes
                val result = headerCellNodes.associateWith { mutableListOf<String>() }

                // Process data rows (skip the first row which contains headers)
                tableContent.drop(1).forEach { rowNode ->
                    val rowMap = rowNode as? Map<*, *> ?: return@forEach
                    if (rowMap["nodeType"] as? String == "table-row") {
                        val cells = rowMap["content"] as? List<*> ?: return@forEach
                        val cellValues = cells.mapNotNull { cell ->
                            val cellMap = cell as? Map<*, *> ?: return@mapNotNull null
                            if (cellMap["nodeType"] as? String == "table-cell") {
                                extractTextFromTableCell(cellMap)
                            } else null
                        }

                        // Add cell values to corresponding headers
                        cellValues.forEachIndexed { index, value ->
                            if (index < headerCellNodes.size) {
                                result[headerCellNodes[index]]?.add(value)
                            }
                        }
                    }
                }

                return result.mapValues { it.value.toList() }
            } else {
                // Second format: table where first cell of each row is the header
                val result = mutableMapOf<String, MutableList<String>>()

                tableContent.forEach { rowNode ->
                    val rowMap = rowNode as? Map<*, *> ?: return@forEach
                    if (rowMap["nodeType"] as? String == "table-row") {
                        val cells = rowMap["content"] as? List<*> ?: return@forEach
                        val cellValues = cells.mapNotNull { cell ->
                            val cellMap = cell as? Map<*, *> ?: return@mapNotNull null
                            if (cellMap["nodeType"] as? String == "table-cell") {
                                extractTextFromTableCell(cellMap)
                            } else null
                        }

                        if (cellValues.isNotEmpty()) {
                            val header = cellValues.first()
                            val values = cellValues.drop(1)
                            result[header] = values.toMutableList()
                        }
                    }
                }

                return result.mapValues { it.value.toList() }
            }
        } catch (e: Exception) {
            emptyMap()
        }
    }

    private fun extractTextFromTableCell(cellMap: Map<*, *>): String? {
        return try {
            val cellContent = cellMap["content"] as? List<*> ?: return null
            cellContent.mapNotNull { contentNode ->
                val nodeMap = contentNode as? Map<*, *> ?: return@mapNotNull null
                if (nodeMap["nodeType"] as? String == "paragraph") {
                    val paragraphContent = nodeMap["content"] as? List<*> ?: return@mapNotNull null
                    paragraphContent.mapNotNull { textNode ->
                        val textMap = textNode as? Map<*, *> ?: return@mapNotNull null
                        if (textMap["nodeType"] as? String == "text") {
                            textMap["value"] as? String
                        } else null
                    }.joinToString("")
                } else null
            }.joinToString("").trim()
        } catch (e: Exception) {
            null
        }
    }

    private fun SizeTableCollectionQuery.Item1.toSizeTableDetailsItem(): SizeTableDetailsItem {
        return SizeTableDetailsItem(
            title = title,
            description = richtext?.extractTextFromJson() ?: ""
        )
    }

    private fun SizeTableCollectionQuery.Richtext.extractTextFromJson(): String {
        return try {
            val jsonMap = json as? Map<*, *> ?: return ""
            val content = jsonMap["content"] as? List<*> ?: return ""

            content.mapNotNull { contentNode ->
                val nodeMap = contentNode as? Map<*, *> ?: return@mapNotNull null
                val nodeContent = nodeMap["content"] as? List<*> ?: return@mapNotNull null

                nodeContent.mapNotNull { textNode ->
                    val textMap = textNode as? Map<*, *> ?: return@mapNotNull null
                    val nodeType = textMap["nodeType"] as? String
                    if (nodeType == "text") {
                        textMap["value"] as? String
                    } else null
                }.joinToString("")
            }.joinToString(" ")
        } catch (e: Exception) {
            ""
        }
    }

    private fun extractParagraphDetails(nodeMap: Map<*, *>): SizeTableDetailsItem? {
        return try {
            val nodeContent = nodeMap["content"] as? List<*> ?: return null
            val textElements = nodeContent.mapNotNull { textNode ->
                val textMap = textNode as? Map<*, *> ?: return@mapNotNull null
                if (textMap["nodeType"] as? String == "text") {
                    val value = textMap["value"] as? String ?: ""
                    val marks = textMap["marks"] as? List<*> ?: emptyList<Any>()
                    val isBold = marks.any { mark ->
                        val markMap = mark as? Map<*, *>
                        markMap?.get("type") == "bold"
                    }
                    Pair(value, isBold)
                } else null
            }

            if (textElements.isEmpty()) return null

            // Find the first bold element as the key
            val keyElement = textElements.find { it.second }?.first

            // Join all text elements for the value, removing the key part
            val fullText = textElements.joinToString("") { it.first }
            val value = fullText.removePrefix(keyElement ?: "")
                .removePrefix(":")
                .trim()

            // Clean up the key (remove trailing colon if present)
            val cleanKey = keyElement?.trim()?.removeSuffix(":").takeIf { (it ?: "").isNotBlank() }

            if (cleanKey != null || value.isNotBlank()) {
                SizeTableDetailsItem(title = cleanKey, description = value)
            } else null
        } catch (e: Exception) {
            null
        }
    }

    private fun BannerQuery.Item.toBanner(): PromoBanner.Content {
        val textContent = text?.json?.let { json ->
            try {
                val jsonMap = json as? Map<*, *> ?: return@let ""
                val content = jsonMap["content"] as? List<*> ?: return@let ""

                content.firstNotNullOfOrNull { contentNode ->
                    val nodeMap = contentNode as? Map<*, *> ?: return@firstNotNullOfOrNull null
                    if (nodeMap["nodeType"] as? String == "paragraph") {
                        val nodeContent = nodeMap["content"] as? List<*> ?: return@firstNotNullOfOrNull null
                        nodeContent.firstNotNullOfOrNull { textNode ->
                            val textMap = textNode as? Map<*, *> ?: return@firstNotNullOfOrNull null
                            if (textMap["nodeType"] as? String == "text") {
                                textMap["value"] as? String
                            } else null
                        }
                    } else null
                } ?: ""
            } catch (e: Exception) {
                ""
            }
        } ?: ""

        return PromoBanner.Content(
            text = textContent,
            infoText = modal?.coupon?.hint?.ifBlank { null },
            promoCode = modal?.coupon?.code
        )
    }
}
