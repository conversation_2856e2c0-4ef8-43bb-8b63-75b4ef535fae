package com.ottogroup.appkit.nativeui.creator.productdetail

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.Result.Failure
import com.ottogroup.appkit.base.Result.Success
import com.ottogroup.appkit.base.getOrElse
import com.ottogroup.appkit.base.onEachDistinctBy
import com.ottogroup.appkit.base.withInitialValue
import com.ottogroup.appkit.nativeui.ProductDetailScreenRequestContext
import com.ottogroup.appkit.nativeui.api.Contextual
import com.ottogroup.appkit.nativeui.api.PromoBannerRepository
import com.ottogroup.appkit.nativeui.api.RecommendationsRepository
import com.ottogroup.appkit.nativeui.api.WishlistRepository
import com.ottogroup.appkit.nativeui.api.contentful.ContentfulRepository
import com.ottogroup.appkit.nativeui.creator.Placeholders
import com.ottogroup.appkit.nativeui.creator.createProductRecommendations
import com.ottogroup.appkit.nativeui.creator.flatMapWithLens
import com.ottogroup.appkit.nativeui.creator.mapOrNull
import com.ottogroup.appkit.nativeui.creator.mapWithLens
import com.ottogroup.appkit.nativeui.model.domain.ArticleStandards
import com.ottogroup.appkit.nativeui.model.domain.Availability
import com.ottogroup.appkit.nativeui.model.domain.Brand
import com.ottogroup.appkit.nativeui.model.domain.Dimension
import com.ottogroup.appkit.nativeui.model.domain.Dimension.DimensionType
import com.ottogroup.appkit.nativeui.model.domain.Flag
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.Information
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.Rating
import com.ottogroup.appkit.nativeui.model.domain.Reviews
import com.ottogroup.appkit.nativeui.model.domain.VoucherSpec
import com.ottogroup.appkit.nativeui.model.domain.singleColorDimension
import com.ottogroup.appkit.nativeui.model.ui.AddToBasketButton
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.ContentfulPromoBanner
import com.ottogroup.appkit.nativeui.model.ui.DynamicYieldPromoBanner
import com.ottogroup.appkit.nativeui.model.ui.LoadingComponent.State
import com.ottogroup.appkit.nativeui.model.ui.ProductAvailability
import com.ottogroup.appkit.nativeui.model.ui.ProductColor
import com.ottogroup.appkit.nativeui.model.ui.ProductColorDimension
import com.ottogroup.appkit.nativeui.model.ui.ProductDetailComponent
import com.ottogroup.appkit.nativeui.model.ui.ProductDetailComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.ProductDetailScreen
import com.ottogroup.appkit.nativeui.model.ui.ProductDimensions
import com.ottogroup.appkit.nativeui.model.ui.ProductGallery
import com.ottogroup.appkit.nativeui.model.ui.ProductHeader
import com.ottogroup.appkit.nativeui.model.ui.ProductInformation
import com.ottogroup.appkit.nativeui.model.ui.ProductPrice
import com.ottogroup.appkit.nativeui.model.ui.ProductRating
import com.ottogroup.appkit.nativeui.model.ui.ProductRecommendationsConfig
import com.ottogroup.appkit.nativeui.model.ui.ProductReviews
import com.ottogroup.appkit.nativeui.model.ui.ProductTitle
import com.ottogroup.appkit.nativeui.model.ui.ProductVariant
import com.ottogroup.appkit.nativeui.model.ui.PromoBanner
import com.ottogroup.appkit.nativeui.model.ui.PromoBannerConfig
import com.ottogroup.appkit.nativeui.model.ui.ShopUsps
import com.ottogroup.appkit.nativeui.tracking.NativeTrackingProvider
import com.ottogroup.appkit.nativeui.util.NativeLogger
import com.ottogroup.appkit.nativeui.util.safeParentId
import com.ottogroup.appkit.tracking.event.ECommerceItem
import kotlin.time.Duration.Companion.milliseconds
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.channelFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.shareIn

@OptIn(ExperimentalCoroutinesApi::class)
internal class ProductDetailScreenCreator(
    private val wishlistRepository: WishlistRepository,
    private val recommendationsRepository: RecommendationsRepository,
    private val promoBannerRepository: PromoBannerRepository,
    private val contentfulRepository: ContentfulRepository,
    private val nativeTrackingProvider: NativeTrackingProvider,
) {

    fun createScreen(
        productFlow: Flow<Contextual<Operation<Product>>>,
        componentConfigs: ComponentConfigs<ProductDetailComponentConfig>,
        screenId: String,
    ): Flow<Result<ProductDetailScreen>> {
        return channelFlow {
            productFlow.mapNotNull {
                // ignore in progress operation
                (it.value as? Operation.Complete)?.let { completeOperation ->
                    Contextual(it.context, completeOperation.result)
                }
            }.onEach { (_, result) ->
                // if product loading failed, fail the whole screen flow
                (result as? Failure)?.let { f -> send(Failure<ProductDetailScreen>(f.failure)) }
            }.mapNotNull { (context, result) ->
                (result as? Success<Product>)?.let { success ->
                    NativeLogger.v { "Creating screen for product ${success.value.debugLogRepresentation()}" }
                    Contextual(context, success.value)
                }
            }.onEachDistinctBy(selector = { (_, product) ->
                listOf(
                    product.sku,
                    product.isOptimisticFake
                )
            }) { (context, product) ->
                if (!product.isOptimisticFake) {
                    nativeTrackingProvider.nativeTracking.viewItem(product)
                }
            }
                // ensure everything above is executed only once, even though below we subscribe to the flow multiple times
                .shareIn(scope = this, SharingStarted.Lazily)
                .createProductComponents(componentConfigs, screenId)
                .collect { it ->
                    send(Success(it))
                }
        }
    }

    private fun Flow<Contextual<Product>>.createProductComponents(
        componentConfigs: ComponentConfigs<ProductDetailComponentConfig>,
        screenId: String
    ): Flow<ProductDetailScreen> {
        // many components don't need to know about the context
        val productFlow = this.map { it.value }

        val filledInComponents = componentConfigs.components.map { config ->

            when (config) {
                ProductHeader.Config -> productFlow.createProductHeader()
                ProductGallery.Config -> createProductGallery()
                ProductColorDimension.Config -> createProductColorDimension()
                ProductColor.Config -> productFlow.createProductColor()
                is ProductRating.Config -> productFlow.createProductRating(config)
                is ProductPrice.Config -> productFlow.createProductPrice(config)
                ProductAvailability.Config -> productFlow.createProductAvailability()
                is ProductDimensions.Config -> productFlow.createProductDimensions(
                    config,
                    wishlistRepository,
                    contentfulRepository
                )

                ProductVariant.Config -> productFlow.createProductVariant()
                is ProductInformation.Config -> productFlow.createProductInformation(config)
                is ProductReviews.Config -> productFlow.createProductReviews(config)
                is ProductRecommendationsConfig -> {
                    @Suppress("UNCHECKED_CAST")
                    productFlow.createProductRecommendations(
                        config,
                        recommendationsRepository,
                        screenContext = ProductDetailScreen::class,
                    ) as Flow<ProductDetailComponent<*>?>
                }

                is AddToBasketButton.Config -> productFlow.createAddToBasketButton()
                is ShopUsps.Config -> productFlow.createShopUsps(config)
                ProductTitle.Config -> productFlow.createProductTitle()
                is PromoBannerConfig -> {
                    @Suppress("UNCHECKED_CAST")
                    productFlow.createPromoBanner(
                        config,
                        promoBannerRepository = promoBannerRepository,
                        nativeTrackingProvider = nativeTrackingProvider
                    ) as Flow<ProductDetailComponent<*>?>
                }
            }
        }
        return combine(filledInComponents) {
            ProductDetailScreen(
                screenId = screenId,
                components = it.filterNotNull()
            )
        }.distinctUntilChanged()
    }

    private fun Flow<Product>.createProductHeader(): Flow<ProductHeader> {
        data class ProductLens(
            val shortTitle: String,
            val brandName: String?,
            val webShopUrl: String?,
            val id: String,
        )
        return flatMapWithLens(
            mapping = {
                ProductLens(
                    shortTitle = it.shortTitle,
                    brandName = it.brand?.name,
                    webShopUrl = it.webShopUrl,
                    id = it.id,
                )
            }
        ) { lens ->
            wishlistRepository.isProductOnWishlist(lens.id).map { isOnWishlistResult ->
                State.Done(
                    ProductHeader.Content(
                        title = lens.shortTitle,
                        brandName = lens.brandName,
                        sharingData = lens.webShopUrl?.let { ProductHeader.SharingData(url = it) },
                        isWishlisted = isOnWishlistResult.getOrElse(false),
                        productIdForWishlisting = lens.id,
                        productId = lens.id,
                    )
                )
            }
        }.withInitialValue(State.Loading(Placeholders.productHeader))
            .map { ProductHeader(it) }
    }

    private fun Flow<Contextual<Product>>.createProductGallery(): Flow<ProductGallery> {
        data class ProductLens(
            val images: List<Image>,
            val flags: List<Flag>,
            val id: String,
            val forceLoadingState: Boolean
        )
        return flatMapWithLens(
            mapping = { (context, product) ->
                ProductLens(
                    product.images,
                    product.flags,
                    product.id,
                    forceLoadingState = product.isOptimisticFake && context is ProductDetailScreenRequestContext.FromColorSelection,
                )
            },
        ) { lens ->
            if (lens.forceLoadingState) {
                return@flatMapWithLens flowOf(State.Loading(Placeholders.productGallery))
            }

            wishlistRepository.isProductOnWishlist(lens.id).map { isOnWishlistResult ->
                State.Done(
                    ProductGallery.Content(
                        images = lens.images,
                        flags = lens.flags,
                        isWishlisted = isOnWishlistResult.getOrElse(false),
                        productIdForWishlisting = lens.id,
                    )
                )
            }
        }.withInitialValue(State.Loading(Placeholders.productGallery))
            .map { ProductGallery(it) }
    }

    private fun Flow<Contextual<Product>>.createProductColorDimension(): Flow<ProductColorDimension?> {
        data class ProductLens(val individualDimensions: List<Dimension>, val id: String)
        return mapWithLens(
            mapping = { (context, product) ->
                ProductLens(product.individualDimensions, product.id)
            },
        ) { lens ->
            val colorDimension = lens.individualDimensions.singleColorDimension() ?: return@mapWithLens null
            val colorLinks = colorDimension.values.map { value ->
                ProductColorDimension.ColorLink(
                    colorName = value.text,
                    productId = value.productId,
                    preview = value.thumbnailUrl?.let(ProductColorDimension.ColorLink.Preview::Thumbnail),
                    availability = value.availability,
                    isSelected = value.productId == lens.id,
                )
            }.sortedWith { a, b ->
                when {
                    a.isSelected -> -1
                    b.isSelected -> 1
                    else -> 0
                }
            }

            // no sense offering the color component with only a single color or no variant information (i.e. no colors)
            if (colorLinks.size <= 1) return@mapWithLens null

            State.Done(
                ProductColorDimension.Content(
                    colorName = colorLinks.find { it.isSelected }?.colorName.orEmpty(),
                    colors = colorLinks,
                )
            )
        }.withInitialValue(State.Loading(Placeholders.productColorDimension))
            .mapOrNull { ProductColorDimension(it) }
    }

    private fun Flow<Product>.createProductColor(): Flow<ProductColor?> {
        data class ProductLens(val individualDimensions: List<Dimension>, val voucherSpec: VoucherSpec?)
        return mapWithLens(
            mapping = { ProductLens(it.individualDimensions, it.voucherSpec) },
        ) { lens ->
            if (lens.voucherSpec != null) {
                return@mapWithLens null
            }

            val colorDimension = lens.individualDimensions.singleColorDimension() ?: return@mapWithLens null
            val colorValue = colorDimension.values.singleOrNull() ?: return@mapWithLens null
            State.Done(ProductColor.Content(colorValue.text))
        }.withInitialValue(
            // this component does not show in initial loading, but must emit to unblock flow collection
            null
        ).mapOrNull { ProductColor(it) }
    }

    private fun Flow<Product>.createProductRating(config: ProductRating.Config): Flow<ProductRating?> {
        data class ProductLens(val id: String, val rating: Rating?)
        return mapWithLens(
            mapping = { ProductLens(it.id, it.reviews.rating) },
        ) { lens ->
            lens.rating?.let {
                State.Done(
                    ProductRating.Content(
                        rating = it,
                        allReviewsUrl = config.allReviewsUrl.replace(
                            ProductRating.Config.URL_PRODUCT_ID_PLACEHOLDER,
                            lens.id,
                        ),
                    )
                )
            }
        }.withInitialValue(State.Loading(Placeholders.productRating))
            .mapOrNull { ProductRating(it, config) }
    }

    private fun Flow<Product>.createProductPrice(config: ProductPrice.Config): Flow<ProductPrice> {
        data class ProductLens(val price: Price)
        return mapWithLens(
            mapping = { ProductLens(it.price) },
        ) { lens ->
            State.Done(ProductPrice.Content(price = lens.price))
        }.withInitialValue(
            State.Loading(Placeholders.productPrice)
        ).map { ProductPrice(it, config) }
    }

    private fun Flow<Product>.createProductAvailability(): Flow<ProductAvailability> {
        data class ProductLens(val availability: Availability)
        return mapWithLens(
            mapping = { ProductLens(it.availability) },
        ) { lens ->
            State.Done(ProductAvailability.Content(availability = lens.availability))
        }.withInitialValue(State.Loading(Placeholders.productAvailability))
            .map { ProductAvailability(it) }
    }

    private fun Flow<Product>.createProductVariant(): Flow<ProductVariant?> {
        data class ProductLens(val id: String, val fusedDimensions: List<Dimension>)
        return mapWithLens(
            mapping = { ProductLens(it.id, it.fusedDimensions) },
        ) { lens ->
            /* There is a potential issue here if we DON'T fuse multiple size dimensions into a single one. In that
             * case we can have single-variant products that still need to display multiple dimension name-value-pairs.
             * This is currently not covered by the concept and is not relevant for LAS, but might come up in the future.
             */
            val singleDimension =
                lens.fusedDimensions.singleOrNull { it.type != DimensionType.COLOR }
                    ?: return@mapWithLens null
            val singleDimensionValue = singleDimension.values.singleOrNull() ?: return@mapWithLens null
            val variantLink = ProductDimensions.ProductDimension.VariantLink(
                name = singleDimensionValue.text,
                productId = singleDimensionValue.productId,
                availability = singleDimensionValue.availability,
                price = singleDimensionValue.price,
                isSelected = singleDimensionValue.productId == lens.id,
            )
            State.Done(ProductVariant.Content(singleDimension.name, variantLink))
        }.withInitialValue(
            // this component does not show in initial loading, but must emit to unblock flow collection
            null
        ).mapOrNull { ProductVariant(it) }
    }

    private fun Flow<Product>.createProductInformation(config: ProductInformation.Config): Flow<ProductInformation?> {
        data class ProductLens(val information: Information, val brand: Brand?, val voucherSpec: VoucherSpec?)
        return mapWithLens(
            mapping = { ProductLens(it.information, it.brand, it.voucherSpec) },
        ) { lens ->
            if (lens.voucherSpec != null) {
                return@mapWithLens null
            }

            State.Done(
                ProductInformation.Content(
                    description = ProductInformation.Description(
                        articleNumber = lens.information.articleNumber,
                        bulletPoints = lens.information.bulletPoints,
                        text = lens.information.description,
                        documents = lens.information.documents,
                    ),
                    details = ProductInformation.Details(
                        attributesTable = lens.information.attributesTable,
                    ),
                    brand = lens.brand?.takeIf { it.description != null }?.let {
                        ProductInformation.Brand(
                            name = it.name,
                            description = it.description,
                        )
                    },
                    importantInformation = lens.information.distributingCompanies.takeIf { it.isNotEmpty() }
                        ?.let { companies ->
                            ProductInformation.ImportantInformation(
                                distributingCompanies = companies.map { company ->
                                    ProductInformation.ImportantInformation.DistributingCompany(
                                        name = company.name,
                                        data = listOfNotNull(
                                            company.address,
                                            company.phone,
                                            company.email,
                                            company.url,
                                        ).joinToString(separator = "\n")
                                    )
                                }
                            )
                        },
                    articleStandards = lens.information.articleStandards?.let { articleStandards ->
                        when (articleStandards) {
                            is ArticleStandards.StructuredSeals -> articleStandards
                            is ArticleStandards.SealUrls -> articleStandards.copy(
                                informationLink = config.sustainabilityUrl
                            )
                        }
                    },
                )
            )
        }.withInitialValue(
            State.Loading(Placeholders.productInformation)
        ).mapOrNull { ProductInformation(state = it, config = config) }
    }

    private fun Flow<Product>.createProductReviews(config: ProductReviews.Config): Flow<ProductReviews> {
        data class ProductLens(val parentId: String, val reviews: Reviews)
        return mapWithLens(
            mapping = { ProductLens(it.safeParentId, it.reviews) },
        ) { lens ->
            val reviews = lens.reviews
            val rating = lens.reviews.rating
            val content = if (reviews.reviews.isEmpty() || rating == null) {
                ProductReviews.Content.Empty(
                    writeReviewUrl = if (config.showWriteReviewButton) {
                        config.writeReviewUrl?.replace(
                            ProductReviews.Config.URL_PRODUCT_ID_PLACEHOLDER,
                            lens.parentId,
                        ) ?: reviews.writeReviewUrl
                    } else {
                        null
                    }
                )
            } else {
                ProductReviews.Content.Reviews(
                    totalCount = reviews.rating?.count ?: reviews.reviews.size,
                    rating = rating,
                    reviews = reviews.reviews.take(config.reviewCount),
                    allReviewsUrl = config.allReviewsUrl.replace(
                        ProductReviews.Config.URL_PRODUCT_ID_PLACEHOLDER,
                        lens.parentId,
                    ),
                )
            }
            State.Done(content)
        }.withInitialValue(
            State.Loading(Placeholders.productReviews),
        ).map {
            ProductReviews(
                state = it,
                config = config,
            )
        }
    }

    private fun Flow<Product>.createAddToBasketButton(): Flow<AddToBasketButton?> {
        data class ProductLens(
            val id: String,
            val individualDimensions: List<Dimension>,
            val availability: Availability
        )
        return mapWithLens(
            mapping = { ProductLens(it.id, it.individualDimensions, it.availability) },
        ) { lens ->
            when {
                lens.individualDimensions.any { it.values.isEmpty() } -> {
                    // adding to basket is not possible if any dimension does not offer values
                    null
                }

                lens.availability.state.ordinal >= Availability.State.TEMPORARILY_OUT_OF_STOCK.ordinal -> {
                    lens.availability.notifyMeUrl?.let { url -> State.Done(AddToBasketButton.Content.NotifyMe(url)) }
                }

                else -> {
                    State.Done(AddToBasketButton.Content.AddToBasket(productId = lens.id))
                }
            }
        }.withInitialValue(
            State.Loading(AddToBasketButton.Content.AddToBasket(productId = Placeholders.productId)),
        ).mapOrNull {
            AddToBasketButton(state = it)
        }
    }

    private fun Flow<Product>.createShopUsps(config: ShopUsps.Config): Flow<ShopUsps> {
        data class ProductLens(val paybackPoints: Int?)
        return mapWithLens(
            mapping = {
                ProductLens(it.paybackPoints)
            },
        ) { lens ->
            State.Done(ShopUsps.Content(paybackPoints = lens.paybackPoints))
        }.withInitialValue(
            State.Loading(ShopUsps.Content(paybackPoints = 50)),
        ).map {
            ShopUsps(state = it, config)
        }
    }

    private fun Flow<Product>.createProductTitle(): Flow<ProductTitle> {
        data class ProductLens(val title: String)
        return mapWithLens(
            mapping = { ProductLens(it.title) },
        ) { lens ->
            State.Done(ProductTitle.Content(lens.title))
        }.withInitialValue(
            State.Loading(ProductTitle.Content(Placeholders.productTitle))
        ).map { ProductTitle(it) }
    }
}

@OptIn(ExperimentalCoroutinesApi::class, FlowPreview::class)
internal fun Flow<Product>.createPromoBanner(
    config: PromoBannerConfig,
    promoBannerRepository: PromoBannerRepository,
    nativeTrackingProvider: NativeTrackingProvider,
): Flow<PromoBanner<*>?> {
    data class ProductLens(val sku: String, val eCommerceItem: ECommerceItem)
    return filter {
        // do not fetch a new banner for a fake product we only display while loading the real one
        !it.isOptimisticFake
    }.flatMapWithLens(
        mapping = {
            val eCommerceItem = nativeTrackingProvider.nativeTracking.toECommerceItem(it)
            ProductLens(it.sku, eCommerceItem)
        },
    ) { lens ->
        val promoBannerOperationFlow = when (config) {
            is DynamicYieldPromoBanner.Config -> promoBannerRepository.getDynamicYieldBanner(
                lens.sku,
                lens.eCommerceItem,
                config
            )

            is ContentfulPromoBanner.Config -> promoBannerRepository.getContentfulBanner()
        }

        promoBannerOperationFlow.map { promoBannerOperation ->
            val state = when (promoBannerOperation) {
                is Operation.Complete -> when (val result = promoBannerOperation.result) {
                    is Failure -> null

                    is Success -> {
                        State.Done(result.value)
                    }
                }

                Operation.InProgress -> {
                    // banners do not have a visible loading state
                    null
                }
            }

            if (state == null) return@map null

            when (config) {
                is DynamicYieldPromoBanner.Config -> DynamicYieldPromoBanner(
                    state = state,
                    config = config,
                )

                is ContentfulPromoBanner.Config -> ContentfulPromoBanner(
                    state = state,
                    config = config,
                )
            }
        }
    }.debounce(500.milliseconds) // to avoid flicker when changing between product variants
        .withInitialValue(null) // immediately emit before doing the request to unblock PDP loading
}

private fun Product.debugLogRepresentation(): String {
    return "Product(id='$id', sku='$sku', title='$title', shortTitle='$shortTitle', isOptimisticFake=$isOptimisticFake, " +
        "price=$price, availability=$availability, " +
        "#individualDimensions=${individualDimensions.size}, #fusedDimensions=${fusedDimensions.size}, " +
        "sizeMatrix=[${sizeMatrix?.entries?.joinToString { it.child.values.size.toString() }}], " +
        "#reviews=${reviews.reviews.size})"
}
