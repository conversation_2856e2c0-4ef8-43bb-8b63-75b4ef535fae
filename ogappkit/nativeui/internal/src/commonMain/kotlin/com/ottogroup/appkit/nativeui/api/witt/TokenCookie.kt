package com.ottogroup.appkit.nativeui.api.witt

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.resultFor
import com.ottogroup.appkit.nativeui.config.OGNativeConfig

internal suspend fun OGNativeConfig.Witt.bearerTokenHeaders(): Result<Map<String, String>> {
    val url = cookies.url
    val cookieName = cookies.tokenCookieName

    return resultFor {
        val token = requireNotNull(
            cookiesBridge.getCookies(url)[cookieName]
        ) { "Cookie $cookieName not found for URL $url" }

        mapOf("Authorization" to "Bearer $token")
    }
}
