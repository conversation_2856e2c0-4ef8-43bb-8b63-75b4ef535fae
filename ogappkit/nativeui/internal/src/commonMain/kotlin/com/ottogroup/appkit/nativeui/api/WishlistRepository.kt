package com.ottogroup.appkit.nativeui.api

import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.nativeui.model.domain.Wishlist
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.Flow

internal interface WishlistRepository {
    suspend fun addProductToWishlist(id: String): Result<Wishlist>
    suspend fun removeProductFromWishlist(id: String): Result<Wishlist>
    fun isProductOnWishlist(id: String): Flow<Result<Boolean>>
}

/**
 * A repository providing access to the wishlist and allowing to observe
 * changes to it.
 */
internal class WishlistRepositoryImpl(
    private val nativeApiProvider: NativeApiProvider,
    coroutineScope: CoroutineScope,
) : WishlistRepository {

    private val nativeApi get() = nativeApiProvider.nativeApi

    override suspend fun addProductToWishlist(id: String): Result<Wishlist> {
        return nativeApi.addProductToWishlist(id)
    }

    override suspend fun removeProductFromWishlist(id: String): Result<Wishlist> {
        return nativeApi.removeProductFromWishlist(id)
    }

    override fun isProductOnWishlist(id: String): Flow<Result<Boolean>> {
        return nativeApi.isProductOnWishlist(id)
    }
}
