package com.ottogroup.appkit.nativeui.api.witt

import com.ottogroup.appkit.base.util.suffix
import com.ottogroup.appkit.nativeui.api.lascana.DimensionValueComparator
import com.ottogroup.appkit.nativeui.api.lascana.DimensionValueNameComparator
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.config.config
import com.ottogroup.appkit.nativeui.model.domain.ArticleStandards
import com.ottogroup.appkit.nativeui.model.domain.Availability
import com.ottogroup.appkit.nativeui.model.domain.Brand
import com.ottogroup.appkit.nativeui.model.domain.CompanyOfOrigin
import com.ottogroup.appkit.nativeui.model.domain.Dimension
import com.ottogroup.appkit.nativeui.model.domain.Flag
import com.ottogroup.appkit.nativeui.model.domain.Image
import com.ottogroup.appkit.nativeui.model.domain.Information
import com.ottogroup.appkit.nativeui.model.domain.Link
import com.ottogroup.appkit.nativeui.model.domain.Price
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.ProductReviews
import com.ottogroup.appkit.nativeui.model.domain.Rating
import com.ottogroup.appkit.nativeui.model.domain.Review
import com.ottogroup.appkit.nativeui.model.domain.Reviews
import com.ottogroup.appkit.nativeui.model.domain.SizeDimensionMatrix
import com.ottogroup.appkit.nativeui.util.stripHtml
import com.ottogroup.appkit.nativeui.witt.GetEuImportersQuery.EuImporterByProduct
import com.ottogroup.appkit.nativeui.witt.ProductDetailQuery
import com.ottogroup.appkit.nativeui.witt.ProductReviewsQuery
import com.ottogroup.appkit.nativeui.witt.fragment.AvailabilityFields
import com.ottogroup.appkit.nativeui.witt.fragment.PriceFields
import com.ottogroup.appkit.nativeui.witt.fragment.ProductPriceFields
import com.ottogroup.appkit.nativeui.witt.fragment.ProductRatingFields
import com.ottogroup.appkit.nativeui.witt.fragment.ProductRatingStatisticsFields
import com.ottogroup.appkit.nativeui.witt.type.AvailabilityState
import com.ottogroup.appkit.nativeui.witt.type.ProductFlag
import io.ktor.http.URLBuilder
import io.ktor.http.path
import kotlinx.datetime.Instant

internal class WittModelMapping(
    private val configProvider: OGNativeConfigProvider,
) {
    private val config get() = configProvider.config<OGNativeConfig.Witt>()

    fun toProduct(
        queryProduct: ProductDetailQuery.ProductBy,
        siblings: List<ProductDetailQuery.ProductBy>,
        reviews: List<ProductDetailQuery.ProductRating>,
        rating: ProductDetailQuery.ProductRatingStatistics?,
        requestedId: String,
        euImporters: List<EuImporterByProduct>
    ): Product {
        val parsedId = WittProductId.from(requestedId)

        val newId = if (!parsedId.isComplete()) {
            val variant = queryProduct.findPrimaryVariant()
            WittProductId(
                productId = parsedId.productId,
                variantId = variant.id,
                displayNumber = variant.displayNumber(),
                promotion = variant.promotion,
            )
        } else {
            parsedId
        }

        return with(queryProduct.copy(id = newId.toString())) {
            val currentVariant = findCurrentVariant()
            Product(
                id = id,
                mkz = mkz,
                title = mainTitle ?: name,
                shortTitle = name,
                webShopUrl = URLBuilder(config.webShopBaseUrl).apply {
                    path("p/${parsedId.productId}")
                }.buildString(),
                parentId = newId.productId,
                images = images.orEmpty().map {
                    Image(
                        url = it.imageFields.hash.mainImageUrl,
                        thumbnailUrl = it.imageFields.hash.thumbnailUrl,
                    )
                },
                flags = getFlags(),
                brand = brand?.let { Brand(name = it.name, description = null) },
                price = currentVariant.price.priceFields.toPrice(),
                fusedDimensions = getDimensions(siblings + queryProduct),
                individualDimensions = getDimensions(siblings + queryProduct),
                sizeMatrix = getSizeMatrix(siblings + queryProduct),
                availability = currentVariant.availability.availabilityFields.toAvailability(),
                information = getInformation(euImporters),
                reviews = reviews.map { it.productRatingFields }.getReviews(rating?.productRatingStatisticsFields),
                sku = "",
                shopTheLook = null,
                moreFromTheSeries = null,
                breadcrumbs = emptyList(),
                categoryId = breadcrumb.lastOrNull()?.id,
                paybackPoints = currentVariant.price.priceFields.getPaybackPoints(),
                sizeAdvisorUrl = "app://productDetail/sizeAdvisor",
                companyOfOrigin = getCompanyOfOrigin()
            )
        }
    }

    private fun List<ProductRatingFields>.getReviews(rating: ProductRatingStatisticsFields?): Reviews {
        rating?.let {
            return Reviews(
                rating = it.getRating(),
                reviews = this.map { review ->
                    Review(
                        rating = review.rating?.rating?.toInt() ?: 0,
                        text = review.text?.stripHtml() ?: "",
                        title = review.title?.stripHtml(),
                        reviewerName = review.userNickname.stripHtml(),
                        dateTime = try {
                            Instant.parse(review.submissionTime)
                        } catch (_: IllegalArgumentException) {
                            null
                        }
                    )
                },
                writeReviewUrl = "customer#customerLoginModal"
            )
        }
        return Reviews(
            rating = null,
            reviews = emptyList(),
            writeReviewUrl = "customer#customerLoginModal"
        )
    }

    fun toProductReviews(
        queryProductReviews: ProductReviewsQuery.Data,
    ): ProductReviews {
        return with(queryProductReviews) {
            ProductReviews(
                id = WittProductId.from(productBy.id).productId,
                title = productBy.name,
                brandName = productBy.brand?.name,
                reviews = productRatings?.map { it.productRatingFields }
                    ?.getReviews(productRatingStatistics?.productRatingStatisticsFields) ?: Reviews(
                    rating = null,
                    reviews = emptyList(),
                    writeReviewUrl = ""
                )
            )
        }
    }

    private fun ProductRatingStatisticsFields.getRating(): Rating? {
        if (totalReviewCount == 0 || totalReviewCount == null) return null
        return Rating(
            averageRating = averageOverallRating?.toFloat() ?: 0f,
            count = totalReviewCount,
            ratingDistribution = getRatingDistribution()
        )
    }

    private fun ProductRatingStatisticsFields.getRatingDistribution(): Map<Int, Pair<Int, Double>> {
        return ratingDistribution?.associate { distribution ->
            distribution.rating to Pair(distribution.count, distribution.percentage)
        } ?: emptyMap()
    }

    private fun ProductDetailQuery.ProductBy.getDimensions(siblings: List<ProductDetailQuery.ProductBy>): List<Dimension> {
        val siblingToUse = siblings.find { it.parsedId.productId == this.parsedId.productId } ?: return emptyList()

        val allDimensions = siblings.flatMap { it.dimensions }
        val currentDimensionAndVariant = allDimensions.firstNotNullOfOrNull { dimension ->
            val thisVariantInDimension = dimension.variants.filterAvailable().find { variant -> variant.id == this.parsedId.variantId }
            thisVariantInDimension?.let { dimension to it }
        }

        val (currentDimension, currentVariant) = if (currentDimensionAndVariant != null) {
            currentDimensionAndVariant
        } else {
            val d = dimensions.first()
            d to d.variants.findPrimaryVariant()
        }

        val colorDimension = Dimension(
            name = COLOR_VARIANT_LABEL,
            type = Dimension.DimensionType.COLOR,
            values = siblings.mapNotNull { siblingProduct ->
                val parsedSiblingId = WittProductId.from(siblingProduct.id)
                val siblingObjectInSiblingProduct =
                    siblingProduct.siblings?.find { it.id == parsedSiblingId.productId } ?: return@mapNotNull null
                val matchingDimensionInSiblingProduct =
                    siblingProduct.dimensions.find { it.label == currentDimension.label }
                        ?: siblingProduct.dimensions.first()
                val matchingSizeVariantInSiblingProduct = matchingDimensionInSiblingProduct.variants
                    .filterAvailable()
                    .find { it.size.label == currentVariant.size.label }
                    ?: matchingDimensionInSiblingProduct.variants.findPrimaryVariant()

                Dimension.Value(
                    text = siblingObjectInSiblingProduct.label,
                    productId = WittProductId(
                        productId = parsedSiblingId.productId,
                        variant = matchingSizeVariantInSiblingProduct,
                    ).toString(),
                    thumbnailUrl = siblingProduct.images?.firstOrNull()?.imageFields?.hash?.thumbnailUrl,
                    availability = siblingObjectInSiblingProduct.availability?.availabilityFields?.toAvailability()
                        ?: Availability.Unknown,
                    price = siblingObjectInSiblingProduct.price?.productPriceFields?.toPrice() ?: Price.None,
                )
            }.sortedWith(DimensionValueComparator)
        )

        val sizeDimension = Dimension(
            name = FUSED_SIZE_DIMENSION_NAME,
            type = Dimension.DimensionType.DEFAULT,
            values = siblingToUse.dimensions.flatMap { dimension ->
                dimension.variants
                    .filterAvailable()
                    .map { variant ->
                        Dimension.Value(
                            text = createDimensionValueLabel(dimension.label, variant.size.label),
                            productId = WittProductId(
                                productId = this.parsedId.productId,
                                variant = variant,
                            ).toString(),
                            thumbnailUrl = null,
                            availability = variant.availability.availabilityFields.toAvailability(),
                            price = variant.price.priceFields.toPrice(),
                        )
                    }.sortedWith(DimensionValueComparator)
            }
        )

        return listOf(
            colorDimension,
            sizeDimension
        )
    }

    private fun createDimensionValueLabel(dimensionLabel: String, variantLabel: String): String {
        val isCupSize = dimensionLabel.contains(CUP_VARIANT_LABEL, true)
        return if (isCupSize) {
            val dimensionLabelWithoutCup = dimensionLabel.replace(CUP_VARIANT_LABEL, "").trim()
            variantLabel.suffix(dimensionLabelWithoutCup)
        } else {
            "$variantLabel $dimensionLabel"
        }
    }

    private fun ProductDetailQuery.ProductBy.getSizeMatrix(siblings: List<ProductDetailQuery.ProductBy>): SizeDimensionMatrix? {
        val siblingToUse = siblings.find { it.parsedId.productId == this.parsedId.productId } ?: return null

        with(siblingToUse) {
            if (dimensions.size == 1) return null

            return SizeDimensionMatrix(
                // TODO: we don't have a name for this. Maybe use longest common substring of all dimension names?
                parentDimensionName = FUSED_SIZE_DIMENSION_NAME,
                entries = dimensions.map { dimension ->
                    val dimensionLabel = dimension.label.replace(CUP_VARIANT_LABEL, "").trim()
                    SizeDimensionMatrix.Entry(
                        name = " $dimensionLabel",
                        child = Dimension(
                            name = dimension.label,
                            type = Dimension.DimensionType.DEFAULT,
                            values = dimension.variants.filterAvailable().map { variant ->
                                Dimension.Value(
                                    text = variant.size.label.replace(dimensionLabel, "").trim(),
                                    productId = WittProductId(
                                        productId = this.parsedId.productId,
                                        variant = variant,
                                    ).toString(),
                                    thumbnailUrl = null,
                                    availability = variant.availability.availabilityFields.toAvailability(),
                                    price = variant.price.priceFields.toPrice(),
                                )
                            }.sortedWith(DimensionValueComparator)
                        )
                    )
                }.sortedWith(
                    compareBy(
                        comparator = DimensionValueNameComparator,
                        selector = { it.name }
                    )
                )
            )
        }
    }

    private fun AvailabilityFields.toAvailability(): Availability {
        return Availability(
            state = when (state) {
                AvailabilityState.available -> {
                    if (quantity != null && quantity <= 10) {
                        Availability.State.LOW_STOCK
                    } else {
                        Availability.State.IN_STOCK
                    }
                }

                AvailabilityState.delayed -> Availability.State.PRE_ORDERABLE
                AvailabilityState.outsold -> Availability.State.TEMPORARILY_OUT_OF_STOCK
                AvailabilityState.outOfStock -> Availability.State.PERMANENTLY_OUT_OF_STOCK
                else -> Availability.State.UNKNOWN
            },
            quantity = quantity ?: 0,
            message = WittL10n.get(key, "deliverability"),
        )
    }

    internal fun ProductPriceFields.toPrice(): Price {
        return Price(
            currency = currency.name,
            value = min,
            oldValue = old.takeIf { it > 0 },
            isStartPrice = priceRange,
        )
    }

    private fun PriceFields.toPrice(): Price {
        val reducedBy = reductions.sumOf { it.withTax }
        return Price(
            currency = currency.name,
            value = withTax,
            oldValue = if (reducedBy > 0) withTax + reducedBy else null,
        )
    }

    private fun PriceFields.getPaybackPoints(): Int? {
        if (!config.displayPaybackPoints) return null
        return (withTax / 100 / 2)
    }

    private fun ProductDetailQuery.ProductBy.getFlags(): List<Flag> {
        val domainFlag = when (flag) {
            ProductFlag.new -> Flag.New
            ProductFlag.sale -> Flag.Sale
            ProductFlag.exclusiveOnline -> Flag.ExclusiveOnline
            ProductFlag.priceHighlighting -> Flag.PriceHighlight
            ProductFlag.none,
            ProductFlag.UNKNOWN__ -> null
        }

        val salesUnitFlag = salesUnit?.let { salesUnitString ->
            val number = "\\d+".toRegex().find(salesUnitString)?.value?.toIntOrNull()
            number?.let {
                Flag.SalesUnit(salesUnitCount = it)
            }
        }

        return listOfNotNull(domainFlag, salesUnitFlag)
    }

    private fun ProductDetailQuery.ProductBy.getInformation(euImporters: List<EuImporterByProduct>): Information {
        val dimension = findCurrentDimension()
        val allAttributes =
            attributes.map { it.key to it.value } + findCurrentVariant().attributes.map { it.key to it.value }
        return Information(
            articleNumber = dimension.getArticleNumber(),
            description = description ?: "",
            bulletPoints = emptyList(),
            attributesTable = Information.AttributesTable(
                listOf(
                    Information.AttributesTable.Section(
                        title = "", // API doesn't give one,
                        entries = allAttributes.map { (key, value) ->
                            Information.AttributesTable.Section.Entry(
                                key = WittL10n.get(key, "product.attribute", "product.detail"),
                                values = listOf(value)
                            )
                        }
                    )
                )
            ),
            distributingCompanies = euImporters.toDistributingCompanies(),
            documents = if (disposalNote) {
                // TODO temporary. We don't have a good URL or link text from the API.
                listOf(
                    Link(
                        text = "Entsorgungshinweis",
                        url = "https://www.witt-weiden.de/content/entsorgung"
                    )
                )
            } else {
                emptyList()
            },
            articleStandards = getArticleStandards()
        )
    }

    private fun ProductDetailQuery.ProductBy.getArticleStandards(): ArticleStandards? {
        val sealUrls = sustainabilityLogos?.takeIf { it.isNotEmpty() }?.map { it.sustainabilityImageUrl }
        if (sealUrls.isNullOrEmpty()) return null

        return ArticleStandards.SealUrls(sealUrls)
    }

    private fun List<EuImporterByProduct>.toDistributingCompanies(): List<Information.DistributingCompany> {
        return map { importer ->
            Information.DistributingCompany(
                name = importer.companyName,
                address = buildString {
                    append(importer.street)
                    importer.houseNumber?.let { append(" $it") }
                    appendLine()
                    importer.additionalAddress?.let {
                        append(" $it")
                        appendLine()
                    }
                    append("${importer.zipCode} ${importer.city}, ${importer.countryCode}")
                },
                email = importer.email?.takeIf { it.isNotBlank() },
                phone = null,
                url = importer.url?.takeIf { it.isNotBlank() },
            )
        }
    }

    private fun ProductDetailQuery.Dimension.getArticleNumber(): String {
        return (id + promotion).chunked(3).joinToString(".")
    }

    // Using widths referenced by breakpoints in the web PDP so they might be cached better by the CDN.
    internal val String.mainImageUrl: String get() = imageUrl(1240)
    internal val String.recoImageUrl: String get() = imageUrl(576)
    internal val String.thumbnailUrl: String get() = imageUrl(256)
    internal val String.sustainabilityImageUrl: String get() = "https://witt-gruppe-res.cloudinary.com/c_limit,w_128,h_128/l/$this"

    private fun String.imageUrl(width: Int): String {
        return "https://cdn.witt.info/$this?brightness=0.97&width=$width"
    }

    private val ProductDetailQuery.ProductBy.parsedId: WittProductId get() = WittProductId.from(this.id)

    companion object {
        const val COLOR_VARIANT_LABEL = "Farbe"
        const val CUP_VARIANT_LABEL = "Cup"
        const val FUSED_SIZE_DIMENSION_NAME = "Größe"
    }
}

private fun ProductDetailQuery.ProductBy.findCurrentVariant(): ProductDetailQuery.Variant {
    val parsedId = WittProductId.from(id)
    return dimensions.flatMap { it.variants }.filterAvailable().find { it.id == parsedId.variantId } ?: findPrimaryVariant()
}

internal fun ProductDetailQuery.ProductBy.findPrimaryVariant(): ProductDetailQuery.Variant {
    return dimensions.flatMap { it.variants }.findPrimaryVariant()
}

private fun List<ProductDetailQuery.Variant>.findPrimaryVariant(): ProductDetailQuery.Variant {
    return sortedWith(
        compareBy(
            {
                // sort available to the front, then pre-orderable, then anything not available
                when (it.availability.availabilityFields.state) {
                    AvailabilityState.available -> -2
                    AvailabilityState.delayed -> -1
                    else -> 1
                }
            },
            {
                // sort cheapest to the front
                it.price.priceFields.withTax
            }
        )
    ).first()
}

private fun ProductDetailQuery.ProductBy.findCurrentDimension(): ProductDetailQuery.Dimension {
    val currentVariant = findCurrentVariant()
    return dimensions.find { d -> d.variants.any { v -> v.id == currentVariant.id } } ?: dimensions.first()
}

internal fun ProductDetailQuery.Variant.displayNumber(): String {
    return iid.orEmpty().substringBefore(".") + promotion
}

private fun ProductDetailQuery.ProductBy.getCompanyOfOrigin(): CompanyOfOrigin {
    return CompanyOfOrigin.entries.find { it.name.equals(companyOfOrigin, ignoreCase = true) }
        ?: CompanyOfOrigin.DEFAULT
}

private fun List<ProductDetailQuery.Variant>.filterAvailable(): List<ProductDetailQuery.Variant> {
    return filter {
        it.availability.availabilityFields.state != AvailabilityState.outsold &&
            it.availability.availabilityFields.state != AvailabilityState.outOfStock
    }
}
