package com.ottogroup.appkit.nativeui.tracking.witt

import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.tracking.NativeTracking
import com.ottogroup.appkit.tracking.event.ECommerceItem
import com.ottogroup.appkit.tracking.event.Interaction
import com.ottogroup.appkit.tracking.event.View

internal class WittNativeTracking : NativeTracking {
    override fun toECommerceItem(product: Product): ECommerceItem {
        return ECommerceItem(
            name = "TODO",
            id = product.id,
        )
    }

    override fun viewItem(product: Product) {
        // TODO
    }

    override fun addItemToCart(productId: String) {
        // TODO
    }

    override fun addItemToWishlist(productId: String) {
        // TODO
    }

    override fun getViewPromotionEvent(
        item: ECommerceItem,
        creativeName: String?,
        creativeSlot: String?,
        promotionId: String?,
        promotionName: String?
    ): View.ProductDetailPromotion {
        return View.ProductDetailPromotion(item)
    }

    override fun getSelectPromotionEvent(
        item: ECommerceItem,
        creativeName: String?,
        creativeSlot: String?,
        promotionId: String?,
        promotionName: String?
    ): Interaction.ProductDetailSelectPromotion {
        return Interaction.ProductDetailSelectPromotion(item)
    }
}
