package com.ottogroup.appkit.nativeui.tracking.lascana

import com.ottogroup.appkit.base.getOrNull
import com.ottogroup.appkit.nativeui.DY_ALTERNATIVE_ID_PARAMETER
import com.ottogroup.appkit.nativeui.api.NativeApi
import com.ottogroup.appkit.nativeui.api.lascana.LascanaNativeApi
import com.ottogroup.appkit.nativeui.api.lascana.ModelMapping.LascanaFlagType
import com.ottogroup.appkit.nativeui.model.domain.Product
import com.ottogroup.appkit.nativeui.model.domain.singleColorDimension
import com.ottogroup.appkit.nativeui.model.domain.singleDefaultDimension
import com.ottogroup.appkit.nativeui.tracking.NativeTracking
import com.ottogroup.appkit.nativeui.util.safeParentId
import com.ottogroup.appkit.tracking.OGTracking
import com.ottogroup.appkit.tracking.event.CustomParameter
import com.ottogroup.appkit.tracking.event.ECommerceItem
import com.ottogroup.appkit.tracking.event.Interaction
import com.ottogroup.appkit.tracking.event.View
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch

internal class LascanaNativeTracking(
    private val ogTracking: OGTracking,
    private val nativeApi: LascanaNativeApi,
    private val coroutineScope: CoroutineScope,
) : NativeTracking {

    private val additionalEventParameters = mapOf(
        "view_context" to CustomParameter("native_app"),
    )

    override fun viewItem(product: Product) {
        ogTracking.track(
            View.ProductDetailViewItem(
                item = toECommerceItem(product = product),
                additionalParameters = additionalEventParameters + mapOf(
                    "product_availability" to CustomParameter(product.availability.state.name.lowercase()),
                )
            )
        )
    }

    override fun addItemToCart(productId: String) {
        coroutineScope.launch {
            getProduct(productId)?.let {
                ogTracking.track(
                    Interaction.AddItemToCart(
                        item = toECommerceItem(it),
                        additionalParameters = additionalEventParameters
                    )
                )
            }
        }
    }

    override fun addItemToWishlist(productId: String) {
        coroutineScope.launch {
            getProduct(productId)?.let {
                ogTracking.track(
                    Interaction.AddItemToWishlist(
                        item = toECommerceItem(it),
                        additionalParameters = additionalEventParameters,
                    )
                )
            }
        }
    }

    override fun getViewPromotionEvent(
        item: ECommerceItem,
        creativeName: String?,
        creativeSlot: String?,
        promotionId: String?,
        promotionName: String?
    ): View.ProductDetailPromotion {
        return View.ProductDetailPromotion(
            item,
            creativeName,
            creativeSlot,
            promotionId,
            promotionName,
            additionalParameters = additionalEventParameters
        )
    }

    override fun getSelectPromotionEvent(
        item: ECommerceItem,
        creativeName: String?,
        creativeSlot: String?,
        promotionId: String?,
        promotionName: String?
    ): Interaction.ProductDetailSelectPromotion {
        return Interaction.ProductDetailSelectPromotion(
            item,
            creativeName,
            creativeSlot,
            promotionId,
            promotionName,
            additionalParameters = additionalEventParameters
        )
    }

    private suspend fun getProduct(productId: String): Product? {
        return nativeApi.getProduct(
            productId,
            resolveVariant = true,
            includeShopTheLook = false,
            cachePolicy = NativeApi.CachePolicy.CacheFirst, // avoid extra network request if product is still cached
        ).first().getOrNull()
    }

    override fun toECommerceItem(product: Product): ECommerceItem {
        return with(product) {
            ECommerceItem(
                name = "$shortTitle:$safeParentId",
                id = safeParentId,
                price = price.value / 100f,
                currency = price.currency,
                discount = price.oldValue?.let { old -> (old - price.value) / 100f },
                brand = brand?.name,
                category = breadcrumbs.joinToString("::"),
                category2 = trackingSizeName(),
                category3 = colorName(),
                category4 = id,
                category5 = flags.mapNotNull { flag ->
                    LascanaFlagType.entries.find { it.domainFlag == flag }?.apiValue?.lowercase()
                }.joinToString(" | ").takeIf { it.isNotBlank() },
                variant = colorName(),
                quantity = 1,
                hasColors = singleColorDimension()?.values.orEmpty().size > 1,
                hasSizes = singleDefaultDimension()?.values.orEmpty().size > 1,
                additionalParameters = buildMap {
                    reviews?.rating?.averageRating?.let {
                        put("custom_review_rating", CustomParameter(it))
                    }

                    reviews?.reviews?.size?.let {
                        put("custom_review_number", CustomParameter(it))
                    }

                    put(DY_ALTERNATIVE_ID_PARAMETER, CustomParameter(product.sku))
                }
            )
        }
    }
}

private fun Product.trackingSizeName(): String? = singleDefaultDimension()
    ?.values?.find { it.productId == id }
    ?.text

private fun Product.colorName(): String? = singleColorDimension()
    ?.values?.find { it.productId == id }
    ?.text
