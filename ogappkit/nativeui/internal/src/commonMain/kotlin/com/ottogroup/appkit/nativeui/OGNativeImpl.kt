package com.ottogroup.appkit.nativeui

import com.ottogroup.appkit.base.Operation
import com.ottogroup.appkit.base.Result
import com.ottogroup.appkit.base.asOperation
import com.ottogroup.appkit.base.di.InternalKoinComponent
import com.ottogroup.appkit.nativeui.api.NativeApiProvider
import com.ottogroup.appkit.nativeui.api.ProductLoader
import com.ottogroup.appkit.nativeui.api.ProductReviewsRepository
import com.ottogroup.appkit.nativeui.api.ProductScreensRepository
import com.ottogroup.appkit.nativeui.api.WishlistRepository
import com.ottogroup.appkit.nativeui.config.OGNativeConfig
import com.ottogroup.appkit.nativeui.config.OGNativeConfigProvider
import com.ottogroup.appkit.nativeui.creator.AddToBasketSuccessScreenCreator
import com.ottogroup.appkit.nativeui.creator.ProductReviewsScreenCreator
import com.ottogroup.appkit.nativeui.creator.productdetail.ProductDetailScreenCreator
import com.ottogroup.appkit.nativeui.model.domain.Basket
import com.ottogroup.appkit.nativeui.model.domain.ProductReviews
import com.ottogroup.appkit.nativeui.model.domain.Wishlist
import com.ottogroup.appkit.nativeui.model.ui.AddToBasketSuccessComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.AddToBasketSuccessScreen
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.ComponentConfigs
import com.ottogroup.appkit.nativeui.model.ui.ProductDetailComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.ProductDetailScreen
import com.ottogroup.appkit.nativeui.model.ui.ProductReviewsComponentConfig
import com.ottogroup.appkit.nativeui.model.ui.ProductReviewsScreen
import com.ottogroup.appkit.nativeui.model.ui.ReviewsList
import com.ottogroup.appkit.nativeui.model.ui.ReviewsSortingOptions
import com.ottogroup.appkit.nativeui.model.ui.ShopTheLookRecommendations
import com.ottogroup.appkit.nativeui.model.ui.parseComponentConfigFromJson
import com.ottogroup.appkit.nativeui.tracking.NativeTrackingProvider
import com.ottogroup.appkit.tracking.OGTracking
import com.ottogroup.appkit.tracking.services.OGTrackingServiceId
import com.ottogroup.appkit.tracking.services.dynamicyield.DynamicYieldConfig
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.flow.onEach

internal class OGNativeImpl(
    private val configProvider: OGNativeConfigProvider,
    private val nativeApiProvider: NativeApiProvider,
    private val productDetailScreenCreator: ProductDetailScreenCreator,
    private val productReviewsScreenCreator: ProductReviewsScreenCreator,
    private val addToBasketSuccessScreenCreator: AddToBasketSuccessScreenCreator,
    private val productIdsFromUrlParser: ProductIdsFromUrlParser,
    private val wishlistRepository: WishlistRepository,
    private val productLoader: ProductLoader,
    private val productScreensRepository: ProductScreensRepository,
    private val ogTracking: OGTracking,
    private val nativeTrackingProvider: NativeTrackingProvider,
) : OGNative, InternalKoinComponent {

    private val productReviewsRepositories = mutableMapOf<String, ProductReviewsRepository>()

    override fun configure(config: OGNativeConfig) {
        configProvider.update(config)

        ogTracking.onUpdateConfig(
            OGTrackingServiceId.DynamicYield,
            (config as? OGNativeConfig.Lascana)?.dynamicYield?.let {
                DynamicYieldConfig(
                    trackPageViewUrl = it.trackPageViewUrl,
                    apiKey = it.apiKey,
                    cookiesDomain = it.cookiesUrl,
                    cookiesBridge = config.cookiesBridge,
                    itemIdAlternativeParameter = DY_ALTERNATIVE_ID_PARAMETER,
                    isEnabled = true
                )
            } ?: DynamicYieldConfig(isEnabled = false)
        )
    }

    @OptIn(ExperimentalUuidApi::class)
    override fun getProductDetailScreen(
        id: String,
        secondaryId: String?,
        componentConfigs: ComponentConfigs<ProductDetailComponentConfig>,
    ): Flow<Result<ProductDetailScreen>> {
        val includeShopTheLook = componentConfigs.components.any { it is ShopTheLookRecommendations.Config }
        val state = productScreensRepository.loadNewProduct(
            id = id,
            secondaryId = secondaryId,
            includeShopTheLook = includeShopTheLook,
        )

        return productDetailScreenCreator.createScreen(
            productFlow = state.flow,
            componentConfigs = componentConfigs,
            screenId = state.screenId,
        )
    }

    @OptIn(ExperimentalUuidApi::class)
    override fun updateProductDetailScreen(
        screenId: String,
        productId: String,
        context: ProductDetailScreenRequestContext?
    ) {
        productScreensRepository.updateScreen(
            screenId = screenId,
            productId = productId,
            context = context,
        )
    }

    override fun getProductDetailScreen(
        id: String,
        secondaryId: String?,
        componentConfigsJson: String,
    ): Flow<Result<ProductDetailScreen>> {
        return flowWithParsedComponentConfigs(componentConfigsJson) { configs ->
            getProductDetailScreen(id, secondaryId, configs)
        }
    }

    override fun getProductDetailScreenByUrl(
        url: String,
        componentConfigsJson: String
    ): Flow<Result<ProductDetailScreen>> {
        val ids = productIdsFromUrlParser.parse(url)
        val id = when (ids) {
            is Result.Success -> ids.value.variantId ?: ids.value.productId
            is Result.Failure -> return flowOf(Result.Failure(ids.failure))
        }
        val secondaryId = if (id == ids.value.variantId) ids.value.productId else null
        return getProductDetailScreen(id, secondaryId, componentConfigsJson)
    }

    override suspend fun getBasket(): Result<Basket> {
        return nativeApiProvider.nativeApi.getBasket().first()
    }

    override suspend fun addProductToBasket(id: String): Result<Basket> {
        return nativeApiProvider.nativeApi.addProductToBasket(id)
            .onSuccess { nativeTrackingProvider.nativeTracking.addItemToCart(id) }
    }

    override suspend fun addVoucherToBasket(id: String, customName: String?): Result<Basket> {
        return nativeApiProvider.nativeApi.addVoucherToBasket(id, customName)
    }

    override suspend fun getWishlist(): Result<Wishlist> {
        return nativeApiProvider.nativeApi.getWishlist().first()
    }

    override suspend fun addProductToWishlist(id: String): Result<Wishlist> {
        return wishlistRepository.addProductToWishlist(id)
            .onSuccess { nativeTrackingProvider.nativeTracking.addItemToWishlist(id) }
    }

    override suspend fun removeProductFromWishlist(id: String): Result<Wishlist> {
        return wishlistRepository.removeProductFromWishlist(id)
    }

    override fun getProductReviewsScreen(id: String, componentConfigsJson: String): Flow<Result<ProductReviewsScreen>> {
        return flowWithParsedComponentConfigs(componentConfigsJson) {
            getProductReviewsScreen(id, it)
        }
    }

    override fun sortProductReviews(screenId: String, sortingOption: ReviewsSortingOptions.SortingOption) {
        productReviewsRepositories[screenId]?.sortProductReviews(sortingOption)
    }

    override fun filterProductReviews(screenId: String, filterRating: Int?) {
        productReviewsRepositories[screenId]?.filterProductReviews(filterRating)
    }

    override fun showMoreReviews(screenId: String) {
        productReviewsRepositories[screenId]?.showMoreReviews()
    }

    @OptIn(ExperimentalUuidApi::class)
    override fun getProductReviewsScreen(
        id: String,
        componentConfigs: ComponentConfigs<ProductReviewsComponentConfig>
    ): Flow<Result<ProductReviewsScreen>> {
        val screenId = Uuid.random().toString()
        val reviewsPerPage = (
            componentConfigs.components.firstNotNullOfOrNull { it as? ReviewsList.Config }
                ?: ReviewsList.Config()
            ).reviewsPerPage
        val repository = ProductReviewsRepository(reviewsPerPage = reviewsPerPage)
        productReviewsRepositories[screenId] = repository
        val reviewsFlow = loadProductReviews(productId = id, screenId = screenId)
        return productReviewsScreenCreator.createScreen(
            reviewsFlow = reviewsFlow,
            componentConfigs = componentConfigs,
            productReviewsRepository = repository,
            screenId = screenId
        ).onCompletion { productReviewsRepositories.remove(screenId) }
    }

    /**
     * Load product reviews and add them to the local repository if successful.
     */
    private fun loadProductReviews(productId: String, screenId: String): Flow<Operation<ProductReviews>> {
        return nativeApiProvider.nativeApi.getReviews(productId).onEach {
            (it as? Result.Success<ProductReviews>)?.value?.reviews?.reviews?.let { reviews ->
                productReviewsRepositories[screenId]?.setProductReviews(reviews)
            }
        }.asOperation()
    }

    override fun getAddToBasketSuccessScreen(
        id: String,
        componentConfigs: ComponentConfigs<AddToBasketSuccessComponentConfig>,
        productDetailScreenId: String?,
    ): Flow<Result<AddToBasketSuccessScreen>> {
        val originalProductId = productDetailScreenId?.let { screenId ->
            productScreensRepository.getScreenState(screenId)?.originalProductId
        }
        val productFlow = productLoader.load(
            originalProductId = originalProductId ?: id,
            id = id,
            secondaryId = null,
            includeShopTheLook = componentConfigs.components.any { it is ShopTheLookRecommendations.Config },
        )

        return addToBasketSuccessScreenCreator.createScreen(
            productFlow = productFlow,
            componentConfigs = componentConfigs,
        )
    }

    override fun getAddToBasketSuccessScreen(
        id: String,
        componentConfigsJson: String,
        productDetailScreenId: String?,
    ): Flow<Result<AddToBasketSuccessScreen>> {
        return flowWithParsedComponentConfigs(componentConfigsJson) {
            getAddToBasketSuccessScreen(id, it, productDetailScreenId)
        }
    }
}

private inline fun <reified C : ComponentConfig, T : Any> flowWithParsedComponentConfigs(
    json: String,
    block: (ComponentConfigs<C>) -> Flow<Result<T>>
): Flow<Result<T>> {
    return try {
        block(parseComponentConfigFromJson(json))
    } catch (t: Throwable) {
        flowOf(Result.Failure(t))
    }
}

private fun <T : Any> Result<T>.onSuccess(block: (T) -> Unit): Result<T> = also {
    if (it is Result.Success) block(it.value)
}

internal const val DY_ALTERNATIVE_ID_PARAMETER = "dy_sku"
