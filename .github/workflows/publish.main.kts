#!/usr/bin/env kotlin

@file:Repository("https://repo.maven.apache.org/maven2/")
@file:DependsOn("io.github.typesafegithub:github-workflows-kt:3.4.0")
@file:Repository("https://bindings.krzeminski.it")
@file:DependsOn("actions:checkout:v4")
@file:DependsOn("actions:setup-java:v4")
@file:DependsOn("gradle:actions__setup-gradle:v4")
@file:DependsOn("gradle:actions__wrapper-validation:v4")
@file:DependsOn("softprops:action-gh-release:v2")
@file:DependsOn("touchlab:ga-update-release-tag:v1")

import io.github.typesafegithub.workflows.actions.actions.Checkout
import io.github.typesafegithub.workflows.actions.actions.SetupJava
import io.github.typesafegithub.workflows.actions.gradle.ActionsSetupGradle
import io.github.typesafegithub.workflows.actions.gradle.ActionsWrapperValidation
import io.github.typesafegithub.workflows.actions.softprops.ActionGhRelease
import io.github.typesafegithub.workflows.actions.touchlab.GaUpdateReleaseTag_Untyped
import io.github.typesafegithub.workflows.domain.Concurrency
import io.github.typesafegithub.workflows.domain.RunnerType
import io.github.typesafegithub.workflows.domain.triggers.WorkflowDispatch
import io.github.typesafegithub.workflows.dsl.expressions.expr
import io.github.typesafegithub.workflows.dsl.workflow
import io.github.typesafegithub.workflows.yaml.DEFAULT_CONSISTENCY_CHECK_JOB_CONFIG
import kotlin.math.exp

enum class Input {
    RELEASE_VERSION,
    IS_DEBUG,
}

fun inputValue(input: Input) = expr("inputs.${input.name}")
val publishTasks =
    "publishKotlinMultiplatformPublicationToGitHubPackagesRepository publishAndroidDebugPublicationToGitHubPackagesRepository publishAndroidReleasePublicationToGitHubPackagesRepository kmmBridgePublish"

workflow(
    sourceFile = __FILE__,
    name = "Publish",
    on = listOf(
        WorkflowDispatch(
            mapOf(
                Input.RELEASE_VERSION.name to WorkflowDispatch.Input(
                    description = "Release version name. MUST be semver compliant.",
                    required = true,
                    type = WorkflowDispatch.Type.String,
                ),
                Input.IS_DEBUG.name to WorkflowDispatch.Input(
                    description = "If set to true, the Xcode framework is debuggable. For release builds, setting to false produces more optimized binaries.",
                    required = false,
                    default = "false",
                    type = WorkflowDispatch.Type.Boolean,
                )
            )
        )
    ),
    concurrency = Concurrency(group = "publish-${expr { github.ref_name }}", cancelInProgress = false),
    consistencyCheckJobConfig =  DEFAULT_CONSISTENCY_CHECK_JOB_CONFIG.copy(
        useLocalBindingsServerAsFallback = true,
    ),
) {
    job(
        id = "publish",
        name = "Publish",
        runsOn = RunnerType.MacOSLatest,
        timeoutMinutes = 60,
    ) {
        uses(
            name = "Checkout",
            action = Checkout(
                fetchDepth = Checkout.FetchDepth.Infinite,
                fetchTags = true,
            )
        )
        uses(
            name = "Gradle Wrapper validation",
            action = ActionsWrapperValidation()
        )
        uses(
            name = "Setup Java",
            action = SetupJava(
                javaVersion = "21",
                distribution = SetupJava.Distribution.Temurin
            )
        )
        val releaseAction = uses(
            name = "Create or find artifact release",
            action = ActionGhRelease(
                token = expr { secrets.GITHUB_TOKEN },
                tagName = inputValue(Input.RELEASE_VERSION),
                prerelease_Untyped = expr { "${github.ref} != 'refs/heads/main'" },
            )
        )
        uses(
            name = "Setup Gradle",
            action = ActionsSetupGradle()
        )
        run(
            name = "Publish",
            command = listOf(
                "./gradlew",
                publishTasks,
                "-PLIBRARY_VERSION=${inputValue(Input.RELEASE_VERSION)}",
                "-PNATIVE_BUILD_TYPE=${expr("inputs.${Input.IS_DEBUG.name} && 'DEBUG' || 'RELEASE'")}",
                "-PGITHUB_ARTIFACT_RELEASE_ID=${expr(releaseAction.outputs.id)}",
                "-PGITHUB_PUBLISH_TOKEN=${expr { secrets.GITHUB_TOKEN }}",
                "-PGITHUB_REPO=${expr { github.repository }}",
                "-PENABLE_PUBLISHING=true",
                "--no-daemon --stacktrace",
            ).joinToString(" ")
        )
        /* The GitHub Release hosts the Xcode binary, but we need to query GitHub for the URL after the binary is
         * uploaded, then generate the Package.swift file. Once that is committed, we need to point the release tag at
         * the final commit.
         */
        uses(
            name = "Update release tag",
            action = GaUpdateReleaseTag_Untyped(
                commitMessage_Untyped = "KMP SPM package release for ${inputValue(Input.RELEASE_VERSION)}",
                tagMessage_Untyped = "KMP release version ${inputValue(Input.RELEASE_VERSION)}",
                tagVersion_Untyped = inputValue(Input.RELEASE_VERSION),
            )
        )
    }
}

Unit // prevent printing the Workflow to stdout
