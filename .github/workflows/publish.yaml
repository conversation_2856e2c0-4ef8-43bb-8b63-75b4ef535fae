# This file was generated using Kotlin DSL (.github/workflows/publish.main.kts).
# If you want to modify the workflow, please change the Kotlin file and regenerate this YAML file.
# Generated with https://github.com/typesafegithub/github-workflows-kt

name: 'Publish'
on:
  workflow_dispatch:
    inputs:
      RELEASE_VERSION:
        description: 'Release version name. MUST be semver compliant.'
        type: 'string'
        required: true
      IS_DEBUG:
        description: 'If set to true, the Xcode framework is debuggable. For release builds, setting to false produces more optimized binaries.'
        type: 'boolean'
        required: false
        default: 'false'
concurrency:
  group: 'publish-${{ github.ref_name }}'
  cancel-in-progress: false
jobs:
  check_yaml_consistency:
    name: 'Check YAML consistency'
    runs-on: 'ubuntu-latest'
    steps:
    - id: 'step-0'
      name: 'Check out'
      uses: 'actions/checkout@v4'
    - id: 'step-1'
      name: 'Execute script'
      continue-on-error: true
      run: 'rm ''.github/workflows/publish.yaml'' && ''.github/workflows/publish.main.kts'''
    - id: 'step-2'
      name: '[Fallback] Start the local server'
      run: 'docker run -p 8080:8080 krzema12/github-workflows-kt-jit-binding-server &'
      if: '${{ steps.step-1.outcome != ''success'' }}'
    - id: 'step-3'
      name: '[Fallback] Wait for the server'
      run: 'curl --head -X GET --retry 60 --retry-all-errors --retry-delay 1 http://localhost:8080/status'
      if: '${{ steps.step-1.outcome != ''success'' }}'
    - id: 'step-4'
      name: '[Fallback] Replace server URL in script'
      run: 'sed -i -e ''s/https:\/\/bindings.krzeminski.it/http:\/\/localhost:8080/g'' .github/workflows/publish.main.kts'
      if: '${{ steps.step-1.outcome != ''success'' }}'
    - id: 'step-5'
      name: '[Fallback] Execute script again'
      run: 'rm -f ''.github/workflows/publish.yaml'' && ''.github/workflows/publish.main.kts'''
      if: '${{ steps.step-1.outcome != ''success'' }}'
    - id: 'step-6'
      name: 'Consistency check'
      run: 'git diff --exit-code ''.github/workflows/publish.yaml'''
  publish:
    name: 'Publish'
    runs-on: 'macos-latest'
    needs:
    - 'check_yaml_consistency'
    timeout-minutes: 60
    steps:
    - id: 'step-0'
      name: 'Checkout'
      uses: 'actions/checkout@v4'
      with:
        fetch-depth: '0'
        fetch-tags: 'true'
    - id: 'step-1'
      name: 'Gradle Wrapper validation'
      uses: 'gradle/actions/wrapper-validation@v4'
    - id: 'step-2'
      name: 'Setup Java'
      uses: 'actions/setup-java@v4'
      with:
        java-version: '21'
        distribution: 'temurin'
    - id: 'step-3'
      name: 'Create or find artifact release'
      uses: 'softprops/action-gh-release@v2'
      with:
        tag_name: '${{ inputs.RELEASE_VERSION }}'
        prerelease: '${{ github.ref != ''refs/heads/main'' }}'
        token: '${{ secrets.GITHUB_TOKEN }}'
    - id: 'step-4'
      name: 'Setup Gradle'
      uses: 'gradle/actions/setup-gradle@v4'
    - id: 'step-5'
      name: 'Publish'
      run: './gradlew publishKotlinMultiplatformPublicationToGitHubPackagesRepository publishAndroidDebugPublicationToGitHubPackagesRepository publishAndroidReleasePublicationToGitHubPackagesRepository kmmBridgePublish -PLIBRARY_VERSION=${{ inputs.RELEASE_VERSION }} -PNATIVE_BUILD_TYPE=${{ inputs.IS_DEBUG && ''DEBUG'' || ''RELEASE'' }} -PGITHUB_ARTIFACT_RELEASE_ID=${{ steps.step-3.outputs.id }} -PGITHUB_PUBLISH_TOKEN=${{ secrets.GITHUB_TOKEN }} -PGITHUB_REPO=${{ github.repository }} -PENABLE_PUBLISHING=true --no-daemon --stacktrace'
    - id: 'step-6'
      name: 'Update release tag'
      uses: 'touchlab/ga-update-release-tag@v1'
      with:
        commitMessage: 'KMP SPM package release for ${{ inputs.RELEASE_VERSION }}'
        tagMessage: 'KMP release version ${{ inputs.RELEASE_VERSION }}'
        tagVersion: '${{ inputs.RELEASE_VERSION }}'
