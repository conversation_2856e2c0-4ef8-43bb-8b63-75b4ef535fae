{"features": [{"custom": {"numberOfTapsToTrigger": 4}, "identifier": "airshipChannelIdDialog", "isEnabled": true}, {"custom": {"supportedUrls": ["app://settings"]}, "identifier": "settings", "isEnabled": true}, {"custom": {"accountUrl": "my-account/", "loginUrl": "app://login", "logoutUrl": "my-account/logout/", "secretIdentifier": "navigationApiKey", "serviceEndpoint": "navigation/service", "showAsLoggedIn": false, "staticEntriesBottom": [{"children": [{"l10n": "account.settings.title", "type": "link", "url": "app://settings"}, {"l10n": "countrySelection.title", "type": "link", "url": "app://pushTenantChooser"}, {"l10n": "account.rate.title", "type": "link", "url": "https://apps.apple.com/app/id1317009356?action=write-review"}, {"l10n": "navigation.recommendation.title", "type": "link", "url": "app://recommendation"}], "l10n": "account.settings.sectionTitle", "type": "section"}], "staticEntriesTop": [], "supportedUrls": ["app://openAccount", "app://openAccount/service", "app://account"]}, "identifier": "account", "isEnabled": true}, {"custom": {"collectorUrl": "", "namespace": "", "pushPath": ""}, "identifier": "snowplow", "isEnabled": false}, {"custom": {"eventIdMapping": {"purchase_completed": "5egn2d"}, "uninstall": true, "urlPrefix": "https://ahgm.adj.st/"}, "identifier": "adjustTracking", "isEnabled": true}, {"custom": {"attributeIdMapping": {}, "eventIdMapping": {"purchase_completed": "purchase_completed", "user_info": "user_info"}, "keepContactAssociation": false}, "identifier": "airshipTracking", "isEnabled": true}, {"custom": {"cloudSite": "eu"}, "identifier": "airship", "isEnabled": true}, {"custom": {"actionText": null, "bodyText": null, "externalTargetUrl": "itms-apps://itunes.apple.com/app/id1317009356", "headlineText": null, "isAllowedToStart": true}, "identifier": "appUpdate", "isEnabled": true}, {"custom": {"assortmentEndpoint": "navigation/assortment", "displayBanners": true, "secretIdentifier": "navigationApiKey", "staticEntriesTop": [], "supportedUrls": ["app://assortment"]}, "identifier": "assortment", "isEnabled": true}, {"custom": {"configEndpoint": null, "joinRecommendationWithInfoString": null, "measureUrl": null, "recommendationEndpoint": null, "secretIdentifier": "", "supportedUrls": ["app://openBraFittingGuide"]}, "identifier": "braFittingGuide", "isEnabled": false}, {"custom": {"catalogEndpoint": "", "catalogOrderUrl": "", "secretIdentifier": "", "supportedUrls": ["app://openCatalogScanner"]}, "identifier": "catalogScanner", "isEnabled": false}, {"custom": {"behaviors": [{"action": {"type": "navigation", "url": "app://pushPromotionLayer?og_origin=onboarding"}, "conditions": [{"start": 1, "type": "screenViews"}], "disabledUrls": ["login", "login.*", "regex:^https:\\/\\/www\\.bonprix.(hu|pl|ro|ua|sk|cz)(\\/|\\/?my-account\\/logout\\/?)?$"], "id": "openPushPromoAfterWelcomeScreen", "maxInvocations": 1, "precondition": "pushDisabled"}, {"action": {"type": "navigation", "url": "app://pushPromotionLayer?og_origin=orderconfirmation"}, "conditions": [{"type": "webBridgeCall", "webBridgeCallName": "purchaseCompleted"}], "id": "openPushPromoAfterPurchaseCompletedWebCall", "precondition": "pushDisabled"}, {"action": {"type": "navigation", "url": "app://review"}, "conditions": [{"period": 10, "start": 10, "type": "appStarts"}], "id": "openAppRating"}, {"action": {"type": "navigation", "url": "app://review"}, "conditions": [{"type": "webBridgeCall", "webBridgeCallName": "purchaseCompleted"}], "id": "openAppRatingAfterPurchaseCompletedWebCall"}]}, "identifier": "coordinator", "isEnabled": true}, {"custom": {"enableLogs": true}, "identifier": "crashReporting", "isEnabled": true}, {"custom": {"apiUrl": "https://bon.aac.ninja/api/", "secretIdentifier": "backend<PERSON><PERSON><PERSON>ey", "supportedUrls": ["app://deals"]}, "identifier": "deals", "isEnabled": true}, {"custom": {"supportedUrls": ["https://apps.apple.com/app/id1317009356?action=write-review", "itms-apps://itunes.apple.com/app/id1317009356", "https://twitter.com/bonprixPL", "https://vk.com/", "https://www.facebook.com/bonprixHU", "https://www.facebook.com/bonprixRO", "https://www.facebook.com/bonprixSK", "https://www.facebook.com/bonprixUA", "https://www.pinterest.com/bonprixPL/", "https://www.tumblr.com/blog/bonprixpl", "https://www.youtube.com/bonprix", "https://plus.google.com/u/0/107689392111309087360", "https://www.facebook.com/bonprixPL", "https://www.instagram.com/bonprix/", "https://www.trustedshops.pl/shop/certificate.php?shop_id=XCAFC712E9EFD41B7591CCFE24DD94702", "regex:^tel:.*", "regex:^mailto:.*", "https://secure.payu.com", "https://www.computop-paygate.com", "https://www.payu.pl", "https://www.paypal.com"]}, "identifier": "externalBrowser", "isEnabled": true}, {"custom": {}, "identifier": "firebaseTracking", "isEnabled": true}, {"custom": {"imageEndpoint": "https://bon.aac.ninja/image-v2/scaled?scaleUp=false&bucketPower=2", "secretIdentifier": "appConfigToken"}, "identifier": "imageAPI", "isEnabled": true}, {"custom": {"supportedUrls": []}, "identifier": "inAppBrowser", "isEnabled": true}, {"custom": {"deleteMessagesAfterDays": 30, "deleteMessagesAfterTenantChange": true, "forceMessageWebView": false, "shouldShowThumbnails": false, "supportedUrls": ["app://openInbox", "app://inbox/overview"]}, "identifier": "inbox", "isEnabled": false}, {"custom": {"supportedUrls": ["app://login"], "webPath": "my-account/login/"}, "identifier": "login", "isEnabled": true}, {"custom": {"navigationTitle": {"enabledUrls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?bonprix\\.(hu|pl|ro|ru|sk)(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$"]}}, "identifier": "loginButton", "isEnabled": true}, {"custom": {"login": "app://login", "registration": "", "showLoginButton": true, "showRegisterButton": false, "toShop": ""}, "identifier": "onboarding", "isEnabled": true}, {"custom": {"promoPattern": "", "webbridgeTrigger": false}, "identifier": "promoBanner", "isEnabled": false}, {"custom": {"defaultNotificationChannel": null, "defaultOptIn": true, "deliveryStatusItemEnabled": false, "openSystemSettingsFromAppSettings": false, "showAndroid13OptInDialog": true, "showOptInDialog": false, "showOptInDialogAfterCheckout": false}, "identifier": "push", "isEnabled": true}, {"custom": {"supportedUrls": ["app://showPushDialog"]}, "identifier": "pushDialog", "isEnabled": true}, {"custom": {"supportedUrls": ["regex:app:\\/\\/pushPromotionLayer(\\?.*)?"]}, "identifier": "pushPromotion", "isEnabled": true}, {"custom": {"showAfterDays": 0}, "identifier": "pushPromotionBanner", "isEnabled": false}, {"custom": {"message": "", "supportedUrls": ["app://openRecommendation", "app://recommendation"], "url": "https://app.adjust.com/3nmw5o_p2ibir"}, "identifier": "recommendation", "isEnabled": true}, {"custom": {"supportedUrls": ["app://review", "app://openRating"]}, "identifier": "review", "isEnabled": true}, {"custom": {"debounceMillis": 500, "maxHistoryItems": 5, "minCharacters": 2, "secretIdentifier": "backend<PERSON><PERSON><PERSON>ey", "shortcuts": [{"iconName": "icon24x24Ae1", "l10n": "search.shortcuts.accountNewIn", "url": "/kategoria/c-1860/"}, {"iconName": "icon24x24Ae2", "l10n": "search.shortcuts.sale", "url": "/sale/c-3794/"}, {"iconName": "icon24x24Ae3", "l10n": "search.account.title", "url": "/my-account"}], "suggestionsApiUrl": "https://bon.aac.ninja/api/search/suggestions?query=", "supportedUrls": ["app://search"], "webPath": "/mobile-api/search/?term="}, "identifier": "search", "isEnabled": true}, {"custom": {"clearSessionCookieOnAppStart": false, "sessionTimeout": 0}, "identifier": "session", "isEnabled": true}, {"custom": {"regionLowerLeftLatitude": 45.68858, "regionLowerLeftLongitude": 5.91474, "regionUpperRightLatitude": 55.25106, "regionUpperRightLongitude": 17.1109, "storesEndpoint": "https://las.aac.ninja/v1/shops/", "supportedUrls": ["app://openShopFinder"]}, "identifier": "storeFinder", "isEnabled": false}, {"custom": {"hideSearchOnTabs": ["basket", "wishlist", "profile"], "noBackstackInTabs": [], "tabRouting": [{"allowedIds": ["shop"], "urls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?bonprix\\.(hu|pl|ro|ru|sk)(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$", "my-account/logout/"]}, {"allowedIds": ["basket"], "urls": ["cart.*"]}, {"allowedIds": ["wishlist"], "urls": ["wishlist.*"]}, {"allowedIds": ["assortment"], "urls": ["regex:^app://assortment/.*"]}, {"allowedIds": ["account"], "urls": ["my-account.*"]}, {"allowedIds": ["shop", "basket", "wishlist", "assortment", "account"], "urls": ["regex:^https?://.*\\.bonprix\\.(hu|pl|ro|ru|sk)", "app://login"]}], "tabs": [{"activeImageName": "icon24x24TabBarActiveTab1", "hasLogo": true, "identifier": "shop", "inactiveImageName": "icon24x24TabBarInactiveTab1", "l10n": "navigation.shop.title", "url": "/"}, {"activeImageName": "icon24x24TabBarActiveTab2", "identifier": "assortment", "inactiveImageName": "icon24x24TabBarInactiveTab2", "l10n": "navigation.assortment.title", "url": "app://assortment"}, {"activeImageName": "icon24x24TabBarActiveTab3", "identifier": "wishlist", "inactiveImageName": "icon24x24TabBarInactiveTab3", "l10n": "navigation.wishlist.title", "url": "wishlist"}, {"activeImageName": "icon24x24TabBarActiveTab4", "identifier": "basket", "inactiveImageName": "icon24x24TabBarInactiveTab4", "l10n": "navigation.cart.title", "url": "cart"}, {"activeImageName": "icon24x24TabBarActiveTab5", "identifier": "account", "inactiveImageName": "icon24x24TabBarInactiveTab5", "l10n": "navigation.profile.title", "url": "app://openAccount", "webBridgeName": "voucher"}]}, "identifier": "tabBar", "isEnabled": true}, {"custom": {"ignoreSettingsTrackingWebbridge": false, "requestATT": false, "viewEventMapping": {"Basket": ["regex:^https:\\/\\/www\\.bonprix.(hu|pl|ro|ua|sk|cz)\\/cart\\/?(\\?.*)?$"], "Checkout": ["regex:^https:\\/\\/www\\.bonprix.(hu|pl|ro|ua|sk|cz)\\/checkout(#\\/.*)?\\/?(\\?.*)?$"], "Home": ["regex:^https:\\/\\/www\\.bonprix.(hu|pl|ro|ua|sk|cz)(\\/|\\/?my-account\\/logout\\/?)?$"], "Login": ["regex:^https:\\/\\/www\\.bonprix.(hu|pl|ro|ua|sk|cz)\\/(my-account/login\\/?)$"], "OrderConfirmation": ["regex:^https:\\/\\/www\\.bonprix.(hu|pl|ro|ua|sk|cz)\\/confirm\\/?(\\?.*)?$"], "ProductDetailPage": ["regex:^https:\\/\\/www\\.bonprix.(hu|pl|ro|ua|sk|cz)\\/(.*p-[0-9]+\\/?(\\/.*)?)$"], "ProductList": ["regex:^https:\\/\\/www\\.bonprix.(hu|pl|ro|ua|sk|cz)\\/(.*(c|t)-[0-9]+\\/?(\\/.*)?)$"], "ProductListOfSearchResult": ["regex:^https:\\/\\/www\\.bonprix.(hu|pl|ro|ua|sk|cz)\\/(search\\/.*)$"], "Registration": ["regex:^https:\\/\\/www\\.bonprix.(hu|pl|ro|ua|sk|cz)\\/(.*\\?step=register)$"], "Wishlist": ["regex:^https:\\/\\/www\\.bonprix.(hu|pl|ro|ua|sk|cz)\\/wishlist\\/?(\\?.*)?$"]}}, "identifier": "tracking", "isEnabled": true}, {"custom": {"isAdjustEnabled": true, "isAdvertisingIdEnabled": true, "isAirshipEnabled": true}, "identifier": "userAgent", "isEnabled": true}, {"custom": {"salutationUrls": ["regex:^https:\\/\\/(www\\.)?bonprix(\\.hu|\\.pl|\\.ro|\\.ua|\\.sk)\\/(\\?.*)?$"]}, "identifier": "salutation", "isEnabled": true}, {"custom": {"supportedUrls": ["regex:^https:\\/\\/www\\.bonprix.(hu|pl|ro|ua|sk|cz)\\/cart\\/?(\\?.*)?$", "regex:^https:\\/\\/www\\.bonprix.(hu|pl|ro|ua|sk|cz)\\/checkout(#\\/.*)?\\/?(\\?.*)?$"]}, "identifier": "copyCodeBanner", "isEnabled": true}, {"custom": {"disableSwipeToRefresh": [], "disabledBackButtonUrls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?bonprix\\.(hu|pl|ro|ru|sk)(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$", "confirm/", "cart/"], "emitFakePageEventsForCSR": false, "enableLargeScreenOptimization": [], "enabledSearchUrls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?bonprix\\.(hu|pl|ro|ru|sk)(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$"], "forceLogo": false, "logoDisplayString": "<PERSON><PERSON><PERSON>", "maxWebViewSavedStateSize": 80000, "redirectUrls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?bonprix\\.(hu|pl|ro|ru|sk)(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$", "https://secure.payu.com", "https://www.computop-paygate.com", "https://www.payu.pl", "https://www.paypal.com"], "reloadOnAppearUrls": ["wishlist/", "cart/", "my-account/", "confirm/"], "restrictWebViewSavedStateSize": true, "sharingType": "TOOLBAR", "supportedUrls": ["^https.*", "^file.*"]}, "identifier": "web", "isEnabled": true}]}