{"features": [{"custom": {"isAdjustEnabled": true, "isAdvertisingIdEnabled": true, "isAirshipEnabled": true}, "identifier": "userAgent", "isEnabled": true}, {"custom": {"graphQLApiUrl": "https://www.lascana.de/graphql", "lascana": {"dynamicYield": {"cookiesUrl": "https://www.lascana.de", "trackPageViewUrl": "https://direct.dy-api.eu/v2/serve/user/choose"}, "restApiUrl": "https://www.lascana.de/api/"}, "productIdRegex": "^https://www\\.lascana\\.de\\/[a-zA-Z0-9-]+-(?<productId>\\d+)\\.html(?:\\?[^#]*?variantId=(?<variantId>[0-9_]+)[^#]*)?"}, "identifier": "nativeAPI", "isEnabled": true}, {"custom": {"basketSuccessComponents": [{"name": "AddedProduct"}, {"name": "DynamicYieldRecommendations", "selectorGroups": [], "selectorNames": ["App Alternativen für dich"], "subtitleL10n": "productDetail.recommendation.recommendationSubTitle", "titleL10n": "productDetail.recommendation.recommendationTitle"}, {"basketUrl": "checkout/warenkorb.html", "name": "ShowBasketButton"}, {"name": "ContinueShoppingButton"}], "components": [{"name": "ProductHeader"}, {"name": "DynamicYieldBanner", "selectorNames": ["ADS Banner API"], "slotId": "App_PDP_Top"}, {"name": "ProductGallery"}, {"name": "ProductColorDimension"}, {"allReviewsUrl": "app://productReviewDetail?productId={productId}", "name": "ProductRating"}, {"conditionsUrl": "lieferung", "name": "ProductPrice"}, {"name": "ProductAvailability"}, {"name": "ProductColor"}, {"name": "ProductVariant"}, {"name": "ProductDimensions", "style": "nested"}, {"name": "AddToBasketButton"}, {"name": "ShopUsps", "paybackPointsIndex": 3}, {"name": "ProductInformation"}, {"allReviewsUrl": "app://productReviewDetail?productId={productId}&reviewIndex=", "name": "ProductReviews", "reviewCount": 6}, {"name": "ShopTheLookRecommendations", "subtitleL10n": "productDetail.recommendation.shopTheLookSubTitle", "titleL10n": "productDetail.recommendation.shopTheLookTitle"}, {"name": "MoreFromTheSeriesRecommendations", "subtitleL10n": "productDetail.recommendation.moreFromTheSeriesSubTitle", "titleL10n": "productDetail.recommendation.moreFromTheSeriesTitle"}, {"name": "DynamicYieldRecommendations", "selectorGroups": [], "selectorNames": ["App Alternativen für dich"], "subtitleL10n": "productDetail.recommendation.recommendationSubTitle", "titleL10n": "productDetail.recommendation.recommendationTitle"}, {"name": "DynamicYieldRecommendations", "selectorNames": ["app zuletzt gesehen"], "subtitleL10n": "productDetail.recommendation.recommendationSubTitle", "titleL10n": "productDetail.recommendation.recentlyViewedTitle"}, {"name": "DynamicYieldRecommendations", "selectorNames": ["App Passend Dazu"], "subtitleL10n": "productDetail.recommendation.recommendationSubTitle", "titleL10n": "productDetail.recommendation.matchingTitle"}], "supportedUrls": ["regex:^https:\\/\\/(www\\.lascana\\.de|stage-www-lascana-de-ottolasc\\.unbelievable-machine\\.net|stage-www-lascana-de-temp\\.unbelievable-machine\\.net)\\/[a-zA-Z0-9\\-]+\\d[0-9_]+\\.html(\\?.*)?$"]}, "identifier": "productDetail", "isEnabled": false}, {"custom": {"components": [{"name": "ReviewsInformation"}, {"name": "ReviewsSortingOptions"}, {"name": "ReviewsList", "reviewsPerPage": 15}, {"name": "WriteReviewButton"}], "supportedUrls": ["app://productReviewDetail"]}, "identifier": "productReviewDetail", "isEnabled": true}, {"custom": {"numberOfTapsToTrigger": 4}, "identifier": "airshipChannelIdDialog", "isEnabled": true}, {"identifier": "push", "isEnabled": true}, {"custom": {"supportedUrls": ["app://settings", "app://openSettings"]}, "identifier": "settings", "isEnabled": true}, {"custom": {"accountUrl": "account/index.html", "loginUrl": "index.php?cl=account", "logoutUrl": "index.php?cl=account&fnc=logout", "secretIdentifier": "navigationApiKey", "serviceEndpoint": "https://api.lascana.de/navigation/service", "showAsLoggedIn": false, "staticEntriesBottom": [], "staticEntriesTop": [], "supportedUrls": ["app://openAccount", "app://openAccount/service", "app://account"]}, "identifier": "account", "isEnabled": true}, {"custom": {"collectorUrl": "", "namespace": "", "pushPath": ""}, "identifier": "snowplow", "isEnabled": false}, {"custom": {"eventIdMapping": {}, "eventTokenMapping": {"InteractionAddItemToCart": "o10bv4", "InteractionAddItemToWishlist": "t3ickr", "InteractionAddToCart": "o10bv4", "InteractionAddToWishlist": "t3ickr", "InteractionProductDetailSelectVariant": "v9okmx", "InteractionPurchase": "9jr44c", "ScreenCategoryMenu": "qfwlv9", "ScreenHome": "bzxr4f", "ScreenProductDetailPage": "iqijbc", "ViewProductDetailViewItem": "rkjpyb"}, "uninstall": false, "urlPrefix": "https://q4qu.adj.st/"}, "identifier": "adjustTracking", "isEnabled": true}, {"custom": {"attributeIdMapping": {}, "eventIdMapping": {"basket_badge_count": true, "purchase_completed": true, "user_info": true, "wishlist_badge_count": true}, "keepContactAssociation": true}, "identifier": "airshipTracking", "isEnabled": true}, {"custom": {"cloudSite": "eu"}, "identifier": "airship", "isEnabled": true}, {"custom": {"domains": []}, "identifier": "allowedDomains", "isEnabled": true}, {"custom": {"actionText": null, "bodyText": null, "externalTargetUrl": "itms-apps://itunes.apple.com/app/id1227733644", "headlineText": null, "isAllowedToStart": true}, "identifier": "appUpdate", "isEnabled": false}, {"custom": {"assortmentEndpoint": "https://api.lascana.de/navigation/assortment", "displayBanners": true, "staticEntriesBottom": [{"l10n": "assortment.services.title", "type": "section"}, {"l10n": "navigation.assortment.entry.bfg.title", "type": "link", "url": "app://bh-berater"}, {"l10n": "catalogScanner.title", "type": "link", "url": "app://openCatalogScanner"}, {"l10n": "shopFinder.title", "type": "link", "url": "app://filialfinder"}], "staticEntriesTop": [], "supportedUrls": ["app://assortment"]}, "identifier": "assortment", "isEnabled": true}, {"custom": {"api": "https://api.lascana.de/", "web": "https://www.lascana.de/"}, "identifier": "baseUrl", "isEnabled": true}, {"custom": {"configEndpoint": "https://las.aac.ninja/api", "joinRecommendationWithInfoString": null, "measureUrl": "/groessenberater/", "recommendationEndpoint": null, "secretIdentifier": "backend<PERSON><PERSON><PERSON>ey", "supportedUrls": ["/bh-berater/", "app://bh-berater", "app://openBraFittingGuide"]}, "identifier": "braFittingGuide", "isEnabled": true}, {"custom": {"catalogEndpoint": "https://las.aac.ninja/v1/catalogscanner/all_catalogs.json", "catalogOrderUrl": "/katalog/anfordern/", "secretIdentifier": "catalogScannerApiKey", "supportedUrls": ["app://openCatalogScanner"]}, "identifier": "catalogScanner", "isEnabled": true}, {"custom": {"enableLogs": true}, "identifier": "crashReporting", "isEnabled": true}, {"custom": {"apiUrl": "https://las.aac.ninja/api", "secretIdentifier": "backend<PERSON><PERSON><PERSON>ey", "supportedUrls": ["app://deals"]}, "identifier": "deals", "isEnabled": true}, {"custom": {"supportedUrls": ["lascana.de/top-themen/lovemoments-contest/", "bilder.lascana.de/wysiwigpro/170101_AGB.pdf", "lascana.de/out/pictures/wysiwigpro/<PERSON>lma<PERSON>_Hermes_PaketShop_Paket_abholen.pdf", "sovendus.com", "https://www.instagram.com/lascanaofficial/", "https://www.facebook.com/lascana", "https://www.instagram.com/lascanaofficial/", "https://www.pinterest.de/lascanade/", "https://www.youtube.com/user/lascana", "https://www.lascana.de/blog/"]}, "identifier": "externalBrowser", "isEnabled": true}, {"custom": {}, "identifier": "firebaseTracking", "isEnabled": true}, {"custom": {"imageEndpoint": "https://las.aac.ninja/image-v2/scaled?scaleUp=false&bucketPower=2", "secretIdentifier": "appConfigToken"}, "identifier": "imageAPI", "isEnabled": true}, {"custom": {"supportedUrls": ["ads.heias.com", "cdn.exactag.com", "m.exactag.com", "scs.webtrends.com", "static.criteo.net", "statse.webtrendslive.com", "widgets.trustedshops.com", "www.econda-monitor.de", "www.google-analytics.com", "2684671.fls.doubleclick.net", "6318523.fls.doubleclick.net", "banner.ahoi-performance.de", "bid.g.doubleclick.net", "connect.facebook.net", "eu-sonar.sociomantic.com", "frontend.payment-transaction.net/creditcard.aspx", "frontend.payment-transaction.net/giropay.aspx", "paypal.com", "sofort.com", "maps.google.com", "maps-api-ssl.google.com", "news.lascana.de", "bilder.lascana.de", "act.webmasterplan.com", "p.webmasterplan.com", "sslh.teradatadmc.com", "sslwidget.criteo.com", "static.criteo.net", "t.c4tw.net", "track.adform.net", "tracker.twenga.de", "widgets.trustedshops.com", "www.facebook.com/2008/fbml", ".facebook.com/tr", "www.googleadservices.com", "www.googletagmanager.com", "www.hermespaketshop.de/paketshopfinder/de/popup/index.html", "googletagmanager.com", "api.sovendus.com", "trustbadge.api.etrusted.com"]}, "identifier": "inAppBrowser", "isEnabled": true}, {"custom": {"deleteMessagesAfterDays": 30, "deleteMessagesAfterTenantChange": true, "forceMessageWebView": false, "shouldShowThumbnails": false, "supportedUrls": ["app://openInbox", "app://inbox/overview"]}, "identifier": "inbox", "isEnabled": true}, {"custom": {"distributionID": null, "timeout": 10}, "identifier": "l10nRemoteService", "isEnabled": false}, {"custom": {"supportedUrls": ["regex:.*index.php\\?cl=account$", "app://login"], "webPath": "account/index.html"}, "identifier": "login", "isEnabled": true}, {"custom": {"navigationTitle": {"enabledUrls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?lascana\\.de(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$"]}}, "identifier": "loginButton", "isEnabled": true}, {"custom": {"appendQueryParameterToDeeplinks": null, "login": "app://login", "registration": "app://register", "showLoginButton": true, "showRegisterButton": true, "toShop": "https://www.lascana.de/"}, "identifier": "onboarding", "isEnabled": true}, {"custom": {"supportedUrls": ["app://showPushDialog"]}, "identifier": "pushDialog", "isEnabled": true}, {"custom": {"supportedUrls": ["regex:app:\\/\\/pushPromotionLayer(\\?.*)?"]}, "identifier": "pushPromotion", "isEnabled": true}, {"custom": {"showAfterDays": 0}, "identifier": "pushPromotionBanner", "isEnabled": false}, {"custom": {"message": "Hey, die LASCANA App könnte dir gefallen. Lade die App jetzt im App Store oder bei Google Play herunter:", "supportedUrls": ["app://openRecommendation"], "url": "https://app.adjust.com/lyc7how_2on1ycy?fallback=https%3A%2F%2Fwww.lascana.de%2Fapp%2F"}, "identifier": "recommendation", "isEnabled": true}, {"custom": {"supportedUrls": ["regex:.*index.php\\?cl=account$", "app://register"], "urlPath": "account/registrierung.html"}, "identifier": "register", "isEnabled": true}, {"custom": {"supportedUrls": ["app://review", "app://openRating"]}, "identifier": "review", "isEnabled": true}, {"custom": {"debounceMillis": 500, "maxHistoryItems": 5, "minCharacters": 2, "secretIdentifier": "backend<PERSON><PERSON><PERSON>ey", "shortcuts": [{"iconName": "icon24x24Ae1", "l10n": "search.shortcuts.accountOrders", "url": "account/bestellhistorie.html"}, {"iconName": "icon24x24Ae2", "l10n": "search.shortcuts.accountBalance", "url": "account/kontostand.html"}, {"iconName": "icon24x24Ae3", "l10n": "search.shortcuts.accountAddress", "url": "account/adressdaten.html"}], "suggestionsApiUrl": "https://las.aac.ninja/api/search/suggestions?query=", "supportedUrls": ["app://search"], "webPath": "suche/?searchparam="}, "identifier": "search", "isEnabled": true}, {"custom": {"supportedUrls": ["app://openSettings", "app://settings"]}, "identifier": "settings", "isEnabled": true}, {"custom": {"regionLowerLeftLatitude": 45.68858, "regionLowerLeftLongitude": 5.91474, "regionUpperRightLatitude": 55.25106, "regionUpperRightLongitude": 17.1109, "secretIdentifier": "storeFinderApiKey", "storesEndpoint": "https://las.aac.ninja/v1/shops", "supportedUrls": ["app://storeFinder", "app://openShopFinder", "app://filialfinder"]}, "identifier": "storeFinder", "isEnabled": true}, {"custom": {"hideSearchOnTabs": ["basket", "wishlist", "account"], "noBackstackInTabs": [], "tabRouting": [{"allowedIds": ["cart", "basket"], "urls": ["checkout/.*"]}, {"allowedIds": ["wishlist"], "urls": ["merkzettel.*"]}, {"allowedIds": ["account", "profile"], "urls": ["account.*", "mein-konto.*", "konto-er<PERSON><PERSON>nen.*", "index.php?login=1", "app://openAccount"]}, {"allowedIds": ["assortment"], "urls": ["regex:^app:\\/\\/assortment\\/.*"]}, {"allowedIds": ["shop", "wishlist", "assortment", "account", "profile"], "urls": ["app://login", "app://register"]}, {"allowedIds": ["shop"], "urls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?lascana\\.de(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$", "index.php?cl=account&fnc=logout"]}], "tabs": [{"activeImageName": "icon24x24TabBarActiveTab1", "hasLogo": true, "identifier": "shop", "inactiveImageName": "icon24x24TabBarInactiveTab1", "l10n": "navigation.shop.title", "url": "/"}, {"activeImageName": "icon24x24TabBarActiveTab2", "identifier": "assortment", "inactiveImageName": "icon24x24TabBarInactiveTab2", "l10n": "navigation.assortment.title", "url": "app://assortment"}, {"activeImageName": "icon24x24TabBarActiveTab3", "identifier": "wishlist", "inactiveImageName": "icon24x24TabBarInactiveTab3", "l10n": "navigation.wishlist.title", "url": "merkzettel"}, {"activeImageName": "icon24x24TabBarActiveTab4", "identifier": "basket", "inactiveImageName": "icon24x24TabBarInactiveTab4", "l10n": "navigation.cart.title", "url": "checkout/warenkorb.html"}, {"activeImageName": "icon24x24TabBarActiveTab5", "identifier": "account", "inactiveImageName": "icon24x24TabBarInactiveTab5", "l10n": "navigation.profile.title", "url": "app://openAccount"}]}, "identifier": "tabBar", "isEnabled": true}, {"custom": {"ignoreSettingsTrackingWebbridge": false, "onlyGlobalOptIn": false, "requestATT": false, "showPreConsent": false, "viewEventMapping": {"Basket": ["regex:^https:\\/\\/www\\.lascana\\.de\\/checkout\\/warenkorb\\.html(\\?.*)?$"], "Checkout": ["regex:^https:\\/\\/www\\.lascana\\.de\\/(index\\.php\\?.*cl=payment.*|checkout\\/(?!warenkorb|danke).*)$"], "Home": ["regex:^https:\\/\\/www\\.lascana\\.de\\/?((\\/index\\.php(\\?cl=start.*)?)|(\\?.*)?)$"], "Login": ["regex:^https:\\/\\/www\\.lascana\\.de\\/account\\/index\\.html(\\?.*)?$"], "OrderConfirmation": ["regex:^https:\\/\\/www\\.lascana\\.de\\/checkout\\/danke.html(\\?.*)?$"], "ProductDetailPage": ["regex:^https:\\/\\/www\\.lascana\\.de\\/[a-zA-Z0-9\\-]+\\d{6,}\\.html(\\?.*)?$"], "ProductList": ["regex:^https:\\/\\/www\\.lascana\\.de\\/(?!inspiration|PAYBACK|store|account|checkout|merkzettel|suche|katalog|impressum|nutzungsbedingungen|Datenschutz|allgemeine-geschaeftsbedingungen|nachhaltigkeit|awin-partner-werden|gutschein|presse|ueber-uns|beratung|pflege-und-waschhinweise|hilfebereich|service|bezahlung|groessenberater|lieferung|ruecksendung|bestellung|[a-zA-Z0-9\\-]+\\d{6,}\\.html|index\\.php|\\?).+$"], "ProductListOfSearchResult": ["regex:^https:\\/\\/www\\.lascana\\.de\\/(index\\.php\\?.*cl=search.*|\\?cl=search.*|suche\\/.*)$"], "Registration": ["regex:^https:\\/\\/www\\.lascana\\.de\\/account\\/registrierung.html(\\?.*)?$"], "Wishlist": ["regex:^https:\\/\\/www\\.lascana\\.de\\/merkzettel\\/?(\\?.*)?$"]}}, "identifier": "tracking", "isEnabled": true}, {"custom": {"supportedUrls": ["regex:^https:\\/\\/www\\.lascana\\.de\\/checkout\\/warenkorb\\.html(\\?.*)?$", "regex:^https:\\/\\/www\\.lascana\\.de\\/(index\\.php\\?.*cl=payment.*|checkout\\/(?!warenkorb|danke).*)$"]}, "identifier": "copyCodeBanner", "isEnabled": true}, {"custom": {"salutationUrls": ["regex:^https:\\/\\/(www\\.)?lascana\\.de(\\?.*)?$"]}, "identifier": "salutation", "isEnabled": true}, {"custom": {"blockedUrls": [], "disableSwipeToRefresh": [], "disabledBackButtonUrls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?lascana\\.de(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$", "/checkout/danke.html"], "emitFakePageEventsForCSR": false, "enableLargeScreenOptimization": [], "enabledSearchUrls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?lascana\\.de(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$"], "forceLogo": true, "isEmptyPageRedirectEnabled": true, "logoDisplayString": "<PERSON><PERSON><PERSON>", "maxWebViewSavedStateSize": 80000, "pathThroughUrls": ["https://tbs.tradedoubler.com"], "redirectUrls": ["regex:^https:\\/\\/([A-Za-z0-9]+\\.)?lascana\\.de(\\?.*|;.*|\\#.*|\\/|\\/mobileapp|\\/\\?.*|\\/;.*|\\/\\#.*)?$", "regex:^https:\\/\\/(www\\.lascana\\.de|stage-www-lascana-de-ottolasc\\.unbelievable-machine\\.net|stage-www-lascana-de-temp\\.unbelievable-machine\\.net)\\/[a-zA-Z0-9\\-]+\\d[0-9_]+\\.html(\\?.*)?$"], "reloadOnAppearUrls": ["merkzettel", "checkout/warenkorb.html", "account/index.html"], "restrictWebViewSavedStateSize": true, "sharingType": "TOOLBAR", "supportedUrls": ["^https.*"]}, "identifier": "web", "isEnabled": true}, {"custom": {"behaviors": [{"action": {"type": "navigation", "url": "app://pushPromotionLayer?og_origin=screenviews"}, "conditions": [{"start": 10, "type": "screenViews"}], "disabledUrls": ["onboarding"], "id": "openPushPromoAfterWelcomeScreen", "maxInvocations": 1, "precondition": "pushDisabled"}, {"action": {"type": "navigation", "url": "app://pushPromotionLayer?og_origin=orderconfirmation"}, "conditions": [{"type": "webBridgeCall", "webBridgeCallName": "purchaseCompleted"}], "disabledUrls": ["onboarding", "app://login"], "id": "openPushPromoAfterPurchaseCompletedWebCall", "precondition": "pushDisabled"}, {"action": {"type": "navigation", "url": "app://review"}, "conditions": [{"start": 10, "type": "appStarts"}, {"type": "webBridgeCall", "webBridgeCallName": "purchaseCompleted"}], "id": "review", "maxInvocations": 1, "precondition": "none"}], "debounceMs": 500}, "identifier": "coordinator", "isEnabled": true}, {"custom": {"promoPattern": "", "webbridgeTrigger": false}, "identifier": "promoBanner", "isEnabled": false}]}