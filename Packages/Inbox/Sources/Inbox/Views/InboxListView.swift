import OGL10n
import SwiftUI
import UICatalog

struct InboxListView: View {
  @StateObject private var viewStore: Store

  public init() {
    _viewStore = StateObject(wrappedValue: Self.make())
  }

  var body: some View {
    List(viewStore.messages, id: \.self) { message in
      InboxRow(id: message.id.value)
        .background(OGColors.backgroundBackground0.color)
        .swipeActions(edge: .trailing, allowsFullSwipe: false) {
          Button(role: .destructive) {
            Task {
              await viewStore.dispatch(.delete(messageId: message.id.value))
            }
          } label: {
            Label(ogL10n.Inbox.Dialog.Delete, systemImage: "trash")
              .foregroundColor(OGColors.textOnDark.color)
              .font(for: .copyMRegular)
          }
          .accessibilityLabel(ogL10n.Inbox.Dialog.Delete)
        }
        .listRowSeparator(.hidden)
        .listRowInsets(EdgeInsets(.zero))
        .separator()
    }
    .listStyle(.plain)
    .frame(maxWidth: .infinity, maxHeight: .infinity)
    .edgesIgnoringSafeArea(.all)
    .accessibilityElement(children: .contain)
  }
}
