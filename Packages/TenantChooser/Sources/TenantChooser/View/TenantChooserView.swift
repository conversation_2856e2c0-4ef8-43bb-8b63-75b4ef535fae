import OGL10n
import OGNavigationBar
import OGTenantCore
import Swift<PERSON>
import UICatalog

// MARK: - TenantChooserView

public struct TenantChooserView: View {
  @StateObject private var viewStore: Self.Store
  private let isModalPresentationStyle: Bool
  private let title: String

  init(presentationStyle: PresentationStyle, title: String) {
    self.isModalPresentationStyle = presentationStyle == .modal
    self.title = title
    _viewStore = StateObject(wrappedValue: Self.make(presentationStyle: presentationStyle))
  }

  public var body: some View {
    VStack(spacing: .zero) {
      if isModalPresentationStyle {
        BottomSheetHeader(title: title, titlePosition: .center) {
          Task {
            await viewStore.dispatch(.close)
          }
        }
        .accessibilityHint(viewStore.accessibilityHintOverlay)
      }
      show(tenantsInfo: viewStore.tenantsInfo)
        .accessibilityElement(children: .contain)
        .accessibilityLabel(ogL10n.General.List.Items.Accessibility(amount: String(viewStore.tenantsInfo.tenants.count)))
    }
    .background(OGColors.backgroundBackground0.color)
    .if(!isModalPresentationStyle) { view in
      OGNavigationBar(titleView: Text(title)) {
        view
      }
    }
    .onAppear {
      Task {
        await viewStore.dispatch(.didAppear)
      }
    }
  }

  @ViewBuilder
  private func show(tenantsInfo: TenantsInfo) -> some View {
    List {
      Section {
        Text(ogL10n.CountrySelection.Hint)
          .font(for: .copyS)
          .foregroundColor(OGColors.textBlack60.color)
          .multilineTextAlignment(.leading)
          .lineLimit(nil)
          .fixedSize(horizontal: false, vertical: true)
          .padding([.leading, .trailing, .top], UILayoutConstants.Default.padding2x)
          .padding(.bottom, 12)
          .accessibilityAddTraits(.isStaticText)
          .listRowInsets(EdgeInsets())
          .background(Color.clear)
      }

      ForEach(tenantsInfo.tenants) { tenant in
        TenantRow(
          with: tenant,
          isModalPresentationStyle: isModalPresentationStyle,
          isSelected: tenant.identifier == tenantsInfo.selectedTenantId
        )
        .listRowInsets(EdgeInsets())
        .listRowSeparator(.hidden)
        .if(!isModalPresentationStyle) { view in
          view.accessibilityHint(ogL10n.CountryList.RegionChangeHint.Accessibility)
        }
      }
    }
    .listStyle(.plain)
  }
}

// MARK: - TenantChooserView_Previews

struct TenantChooserView_Previews: PreviewProvider {
  static var previews: some View {
    TenantChooserView(presentationStyle: .modal, title: "Title")
  }
}

// MARK: - UILayoutConstants.TenantChooserView

extension UILayoutConstants {
  enum TenantChooserView {
    static let fakeNavigationBarHeight = 56.0
    static let cellHeight = 44.0
  }
}
