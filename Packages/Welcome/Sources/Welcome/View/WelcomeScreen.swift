import AppCore
import OGDIService
import OGL10n
import SwiftUI
import UICatalog

// MARK: - WelcomeScreen

public struct WelcomeScreen: View {
  @EnvironmentObject private var deviceState: DeviceState
  @StateObject private var viewStore: Self.Store
  @Environment(\.screenSize) private var screenSize: CGSize
  private let buttonStyleResolver: ButtonStyleFactory

  public init(
    buttonStyleResolver: ButtonStyleFactory = WelcomeContainer.shared.buttonStyleResolver()
  ) {
    _viewStore = StateObject(wrappedValue: Self.make())
    self.buttonStyleResolver = buttonStyleResolver
  }

  public var body: some View {
    ZStack(alignment: deviceState.isLandscapeOrientation ? .top : .topTrailing) {
      WelcomeBackgroundView()
        .ignoresSafeArea()
        .accessibilityHidden(true)
      WelcomeGradientsView()
        .ignoresSafeArea()
        .accessibilityHidden(true)
      ScrollView {
        content
      }
      .basedOnSizeScrollBehavior()
    }
    .navigationBarHidden(true)
    .onAppear {
      Task {
        await viewStore.dispatch(.didAppear)
      }
    }
  }

  private var content: some View {
    VStack(
      alignment: .leading,
      spacing: deviceState.isLandscapeOrientation ?
        UILayoutConstants.WelcomeScreenView.landscapeSpacing :
        UILayoutConstants.WelcomeScreenView.portraitSpacing
    ) {
      if !deviceState.isLandscapeOrientation, viewStore.isTenantChooserButtonEnabled {
        tenantButton
          .accessibility(sortPriority: 6)
      }
      if viewStore.startAnimation {
        Spacer()

        if deviceState.isLandscapeOrientation {
          HStack(alignment: .top) {
            title
              .frame(maxHeight: screenSize.height * UILayoutConstants.WelcomeScreenView.titleHeightRatio)
              .minimumScaleFactor(UILayoutConstants.WelcomeScreenView.landscapeScaleFactor)
              .lineLimit(2)
              .padding(.leading, UILayoutConstants.Default.padding2x)
              .padding(.top, UILayoutConstants.Default.padding4x)
              .accessibility(sortPriority: 5)
            Spacer()
            if viewStore.isTenantChooserButtonEnabled {
              tenantButton
                .accessibility(sortPriority: 6)
            }
          }
        } else {
          title
            .padding(.leading, UILayoutConstants.Default.padding2x)
            .padding(.trailing, UILayoutConstants.Default.padding2x)
            .accessibility(sortPriority: 5)
        }

        if viewStore.isBulletPointsEnabled {
          bulletPointsSection
        }

        buttonsSection
      }
    }
    .frame(minHeight: screenSize.height * UILayoutConstants.WelcomeScreenView.contentRatio)
    .padding(
      .vertical,
      deviceState.isLandscapeOrientation ?
        UILayoutConstants.WelcomeScreenView.landscapeVerticalPadding :
        UILayoutConstants.WelcomeScreenView.portraitVerticalPadding
    )
  }

  private var bulletPointsSection: some View {
    VStack {
      bulletPoints
        .frame(maxHeight: deviceState.isLandscapeOrientation ?
          screenSize.height * UILayoutConstants.WelcomeScreenView.bulletPointsHeightRatio : nil)
        .minimumScaleFactor(deviceState.isLandscapeOrientation ?
          UILayoutConstants.WelcomeScreenView.bulletPointsScaleFactor : 1.0)
        .padding(.leading, UILayoutConstants.Default.padding2x)
        .padding(.trailing, UILayoutConstants.Default.padding2x)
    }
    .padding(
      .bottom,
      deviceState.isLandscapeOrientation ?
        UILayoutConstants.WelcomeScreenView.landscapeBulletPointsBottomPadding :
        UILayoutConstants.Default.padding3x
    )
    .accessibility(sortPriority: 4)
    .accessibilityElement(children: .combine)
    .accessibilityAddTraits(.isSummaryElement)
  }

  private var buttonsSection: some View {
    VStack(spacing: UILayoutConstants.WelcomeScreenView.buttonSpacing) {
      if viewStore.isShowLoginButtonEnabled {
        loginButton
      }
      if viewStore.isShowRegisterButtonEnabled {
        registerButton
      }
      shopButton
    }
    .padding(.horizontal, UILayoutConstants.Default.padding2x)
  }

  private var loginButton: some View {
    C2AButton(
      title: viewStore.login,
      accessibilityIdentifier: "onboarding_button_login"
    ) {
      Task {
        await viewStore.dispatch(.didTapLogin)
      }
    }
    .register(buttonStyleResolver.primaryButtonStyle)
    .dynamicTypeSize(...DynamicTypeSize.xxxLarge)
    .slideTransition(at: 4)
    .accessibility(sortPriority: 3)
  }

  @ViewBuilder private var buttons: some View {
    VStack(spacing: UILayoutConstants.WelcomeScreenView.buttonSpacing) {
      if viewStore.isShowLoginButtonEnabled {
        loginButton
      }
      if viewStore.isShowRegisterButtonEnabled {
        registerButton
      }
      shopButton
    }
  }

  @ViewBuilder private var title: some View {
    Text(viewStore.title)
      .fixedSize(horizontal: false, vertical: true)
      .font(for: .headlineXXL)
      .foregroundColor(OGColors.backgroundBackground0.color)
      .accessibilityIdentifier("onboarding_text_title")
      .accessibilityAddTraits(.isHeader)
      .slideTransition(at: 0)
      .dynamicTypeSize(...DynamicTypeSize.xxxLarge)
  }

  @ViewBuilder private var bulletPoints: some View {
    ForEach(Array(viewStore.bulletPoints.enumerated()), id: \.element) { index, bulletPoint in
      BulletPointView(
        title: bulletPoint,
        accessibilityIdentifier: "onboarding_text_subtitle\(index + 1)"
      )
      .slideTransition(at: index + 1)
      .dynamicTypeSize(...DynamicTypeSize.xxxLarge)
    }
  }

  private var registerButton: some View {
    C2AButton(
      title: viewStore.register,
      accessibilityIdentifier: "onboarding_button_register"
    ) {
      Task {
        await viewStore.dispatch(.didTapRegister)
      }
    }
    .register(buttonStyleResolver.secondaryButtonStyle)
    .slideTransition(at: 5)
    .accessibility(sortPriority: 3)
    .dynamicTypeSize(...DynamicTypeSize.xxxLarge)
  }

  private var shopButton: some View {
    C2AButton(
      title: viewStore.shop,
      accessibilityIdentifier: "onboarding_button_goToShop"
    ) {
      Task {
        await viewStore.dispatch(.didTapShop)
      }
    }
    .register(buttonStyleResolver.tertiaryButtonStyle)
    .slideTransition(at: 6)
    .accessibility(sortPriority: 1)
    .dynamicTypeSize(...DynamicTypeSize.xxxLarge)
  }

  @ViewBuilder private var tenantButton: some View {
    HStack(spacing: .zero) {
      Spacer()
      Button {
        Task {
          await viewStore.dispatch(.didTapTenantChooser)
        }
      } label: {
        HStack(
          alignment: .center,
          spacing: UILayoutConstants.Default.stackSpacing
        ) {
          viewStore
            .flagImage?
            .image
            .frame(height: UILayoutConstants.WelcomeScreenView.tenantButtonHeight)
            .padding(UILayoutConstants.Default.paddingHalf)
          if let tenantName = viewStore.tenantName {
            Text(tenantName)
              .font(for: .section)
              .foregroundColor(OGColors.backgroundBackground0.color)
              .padding(.trailing, UILayoutConstants.WelcomeScreenView.tenantButtonTrailingPadding)
              .accessibilityIdentifier("onboarding_button_changeCountry")
              .dynamicTypeSize(...DynamicTypeSize.xxxLarge)
          }
        }
        .padding(UILayoutConstants.Default.paddingHalf)
      }
      .frostedGlassBackground()
      .cornerRadius(64.0)
      .if(viewStore.tenantName != nil, transform: { view in
        view.accessibilityLabel("\(viewStore.accessibilityTenantChooser) \(viewStore.tenantName ?? "")")
      })
    }
    .padding(.trailing, UILayoutConstants.Default.padding2x)
  }
}

// MARK: - UILayoutConstants.WelcomeScreenView

extension UILayoutConstants {
  enum WelcomeScreenView {
    static let tenantButtonHeight = 20.0
    static let tenantButtonTrailingPadding = 12.0
    static let contentMargin = 18.0
    static let landscapeSpacing = 4.0
    static let portraitSpacing = 16.0
    static let landscapeVerticalPadding = 4.0
    static let portraitVerticalPadding = 24.0
    static let buttonSpacing = 16.0
    static let landscapeScaleFactor = 0.3
    static let bulletPointsScaleFactor = 0.5
    static let landscapeBulletPointsBottomPadding = 8.0

    /// Height ratios
    static let titleHeightRatio = 0.25
    static let bulletPointsHeightRatio = 0.3
    static let buttonsHeightRatio = 0.2
    static let contentRatio = 0.9
  }
}
