import OGAppEnvironment
import OGL10n
import SwiftUI

// MARK: - BottomSheetView

public struct BottomSheetView<Content: View>: View {
  @Environment(\.screenSize) var screenSize
  private let title: String
  private let content: Content

  @SwiftUI.State private var contentHeight: CGFloat = 0

  public init(title: String, @ViewBuilder content: () -> Content) {
    self.title = title
    self.content = content()
  }

  public var body: some View {
    if #available(iOS 16.0, *) {
      sheetContent
        .presentationDetents(contentHeight.presentationDetents(for: screenSize))
    } else {
      sheetContent
    }
  }

  @ViewBuilder private var sheetContent: some View {
    VStack(spacing: UILayoutConstants.Default.padding) {
      BottomSheetHeader(title: title)

      content
        .padding(.horizontal, UILayoutConstants.Default.padding3x)
    }
    .background(
      SizeReader { size in
        guard size.height > contentHeight else { return }
        contentHeight = size.height
      }
    )
  }
}

// MARK: - UILayoutConstants.BottomSheetView

extension UILayoutConstants {
  public enum BottomSheetView {
    public static let detentFractionHeight: CGFloat = 0.95
  }
}

// MARK: - CGFloat + PresentationDetents

@available(iOS 16.0, *)
extension CGFloat {
  fileprivate func presentationDetents(for screenSize: CGSize) -> Set<PresentationDetent> {
    let maxHeight = screenSize.height * UILayoutConstants.BottomSheetView.detentFractionHeight
    return self <= maxHeight ? [.height(self)] : [.height(maxHeight)]
  }
}
