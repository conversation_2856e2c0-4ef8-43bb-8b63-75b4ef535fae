import OGL10n
import SwiftUI

/// A reusable header view for bottom sheets, with a customizable title and close action.
public struct BottomSheetHeader: View {
  public enum TitlePosition {
    case leading
    case center
  }

  @Environment(\.dismiss) private var dismiss

  let title: String?
  let titlePosition: TitlePosition
  let customAction: (() -> Void)?

  /// Creates a new bottom sheet header.
  /// - Parameters:
  ///   - title: The title to display (optional).
  ///   - titlePosition: The alignment for the title (`.leading` or `.center`).
  ///   - customAction: An optional custom close action. Defaults to dismiss.
  public init(
    title: String? = nil,
    titlePosition: TitlePosition = .leading,
    customAction: (() -> Void)? = nil
  ) {
    self.title = title
    self.titlePosition = titlePosition
    self.customAction = customAction
  }

  public var body: some View {
    if #available(iOS 16.0, *) {
      content
        .presentationDragIndicator(.visible)
    } else {
      content
    }
  }

  private var content: some View {
    ZStack {
      HStack {
        headerTitle
        if titlePosition == .leading, title != nil {
          Spacer()
        }
      }
      HStack {
        Spacer()
        closeButton
      }
    }
    .dynamicTypeSize(...DynamicTypeSize.xxLarge)
    .padding(
      .leading,
      UILayoutConstants.Default.padding3x
    )
    .padding(
      [.top, .trailing],
      UILayoutConstants.Default.padding2x
    )
  }

  private var headerTitle: some View {
    Group {
      if let title {
        Text(title)
          .font(for: .titleM)
          .foregroundStyle(OGColors.textOnLight.color)
          .offset(y: UILayoutConstants.Default.padding)
          .accessibilityAddTraits(.isHeader)
          .accessibilitySortPriority(1)
      }
    }
  }

  private var closeButton: some View {
    Button {
      if let customAction {
        customAction()
      } else {
        dismiss()
      }
    } label: {
      OGImages.icon32x32CloserPrimary.image
    }
    .padding(
      .bottom,
      UILayoutConstants.Default.padding1_5x
    )
    .accessibilityElement()
    .accessibilityAddTraits(.isButton)
    .accessibilityLabel(ogL10n.General.Close)
    .accessibilitySortPriority(2)
  }
}
