import SwiftUI

public struct ProductDetailCloseButton: View {
  let action: () -> Void
  let accessibilityLabel: String
  let dropShadow: Bool
  public init(accessibilityLabel: String, dropShadow: Bool = true, action: @escaping () -> Void) {
    self.action = action
    self.accessibilityLabel = accessibilityLabel
    self.dropShadow = dropShadow
  }

  public var body: some View {
    Button(action: {
      action()
    }, label: {
      if dropShadow {
        OGImages.icon24x24CloserPrimary.image
          .circleBackground(size: CGSize(width: 32, height: 32), dropShadow: dropShadow)
      } else {
        OGImages.icon24x24CloserOnLight.image
          .circleBackground(size: CGSize(width: 32, height: 32), dropShadow: dropShadow)
      }
    })
    .accessibilityElement()
    .accessibilityAddTraits(.isButton)
    .accessibilityLabel(accessibilityLabel)
  }
}
