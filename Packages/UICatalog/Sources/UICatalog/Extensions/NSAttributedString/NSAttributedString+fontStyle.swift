import Foundation
import UIKit

extension NSAttributedString {
  static func styledBadgeTextAttributes() -> [NSAttributedString.Key: Any] {
    let style = FontResolverContainer.shared.fontResolver().font(.label)
    let font = UIFont.boldSystemFont(ofSize: style.size)
    return [
      NSAttributedString.Key.font: font,
      NSAttributedString.Key.foregroundColor: OGColors.textBadge.color.uiColor()
    ]
  }

  static func styledBarButtonItemAttributes() -> [NSAttributedString.Key: Any] {
    let style = FontResolverContainer.shared.fontResolver().font(.copyL)
    let font = UIFont(name: style.name, size: CGFloat(style.size)) ?? UIFont.systemFont(ofSize: 16)
    return [
      NSAttributedString.Key.font: font,
      NSAttributedString.Key.foregroundColor: OGColors.navigationBarElementAction.color.uiColor()
    ]
  }

  static func styledTabBarAttributes(color: UIColor) -> [NSAttributedString.Key: Any] {
    let style = FontResolverContainer.shared.fontResolver().font(.tabnavigation)
    let font = UIFont(name: style.name, size: CGFloat(style.size)) ?? UIFont.systemFont(ofSize: 10)

    var attributes: [NSAttributedString.Key: Any] = [.font: font]

    let paragraphStyle = NSMutableParagraphStyle()
    paragraphStyle.maximumLineHeight = CGFloat(style.lineHeight)
    paragraphStyle.minimumLineHeight = CGFloat(style.lineHeight)
    paragraphStyle.alignment = .left
    attributes[.paragraphStyle] = paragraphStyle
    attributes[.foregroundColor] = color

    return attributes
  }

  static func styledTitleAttributes() -> [NSAttributedString.Key: Any] {
    let style = FontResolverContainer.shared.fontResolver().font(.titleM)
    let font =
      UIFont(name: style.name, size: CGFloat(style.size)) ?? UIFont.boldSystemFont(ofSize: 16)
    return [
      NSAttributedString.Key.font: font,
      NSAttributedString.Key.foregroundColor: OGColors.navigationBarElementTitle.color.uiColor()
    ]
  }

  static func styledLargeTitleAttributes() -> [NSAttributedString.Key: Any] {
    let style = FontResolverContainer.shared.fontResolver().font(.headlineXL)
    let font =
      UIFont(name: style.name, size: CGFloat(style.size)) ?? UIFont.boldSystemFont(ofSize: 28)
    return [
      NSAttributedString.Key.font: font,
      NSAttributedString.Key.foregroundColor: OGColors.navigationBarElementTitle.color.uiColor()
    ]
  }
}
