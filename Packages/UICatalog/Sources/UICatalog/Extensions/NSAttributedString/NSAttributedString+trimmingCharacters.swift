import Foundation
import UIKit

extension NSAttributedString {
  /// Returns a copy of the attributed string with characters from the given character set trimmed from both the beginning and the end.
  /// - Parameter characterSet: The set of characters to trim.
  /// - Returns: An `NSAttributedString` with the specified characters trimmed.
  public func trimmingCharacters(in characterSet: CharacterSet) -> NSAttributedString {
    let string = string as NSString // Access the underlying plain string representation
    let length = string.length
    var start = 0 // Index for the start of the trimmed range
    var end = length - 1 // Index for the end of the trimmed range

    // Move 'start' forward while the character at 'start' is in the character set
    while start < length, let scalar = UnicodeScalar(string.character(at: start)), characterSet.contains(scalar) { start += 1 }
    // Move 'end' backward while the character at 'end' is in the character set
    while end > start, let scalar = UnicodeScalar(string.character(at: end)), characterSet.contains(scalar) { end -= 1 }

    if start <= end {
      // If there is a range to return, extract the attributed substring
      return attributedSubstring(from: NSRange(location: start, length: end - start + 1))
    } else {
      // If all characters are trimmed, return an empty attributed string
      return NSAttributedString(string: "")
    }
  }
}
