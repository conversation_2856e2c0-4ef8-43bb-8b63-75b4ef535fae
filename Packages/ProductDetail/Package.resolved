{"pins": [{"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "state": {"revision": "bbe8b69694d7873315fd3a4ad41efe043e1c07c5", "version": "1.2024072200.0"}}, {"identity": "adjust_signature_sdk", "kind": "remoteSourceControl", "location": "https://github.com/adjust/adjust_signature_sdk.git", "state": {"revision": "a84f08b41f7e38624815816f6e93cadc9ea22a92", "version": "3.47.0"}}, {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "61b85103a1aeed8218f17c794687781505fbbef5", "version": "11.2.0"}}, {"identity": "bettercodable", "kind": "remoteSourceControl", "location": "https://github.com/marksands/BetterCodable", "state": {"revision": "61153170668db7a46a20a87e35e70f80b24d4eb5", "version": "0.4.0"}}, {"identity": "factory", "kind": "remoteSourceControl", "location": "https://github.com/hmlongco/Factory", "state": {"revision": "061b3afe0358a0da7ce568f8272c847910be3dd7", "version": "2.2.0"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk", "state": {"revision": "3663b1aa6c7a1bed67ee80fd09dc6d0f9c3bb660", "version": "11.13.0"}}, {"identity": "google-ads-on-device-conversion-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/googleads/google-ads-on-device-conversion-ios-sdk.git", "state": {"revision": "70a7857886f065a40486a7607268781c49db04ae", "version": "2.0.0"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "543071966b3fb6613a2fc5c6e7112d1e998184a7", "version": "11.13.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "617af071af9aa1d6a091d59a202910ac482128f9", "version": "10.1.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "60da361632d0de02786f709bdc0c4df340f7613e", "version": "8.1.0"}}, {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "state": {"revision": "cc0001a0cf963aa40501d9c2b181e7fc9fd8ec71", "version": "1.69.0"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "a2ab612cb980066ee56d90d60d8462992c07f24b", "version": "3.5.0"}}, {"identity": "interop-ios-for-google-sdks", "kind": "remoteSourceControl", "location": "https://github.com/google/interop-ios-for-google-sdks.git", "state": {"revision": "040d087ac2267d2ddd4cca36c757d1c6a05fdbfe", "version": "101.0.0"}}, {"identity": "ios-library", "kind": "remoteSourceControl", "location": "https://github.com/urbanairship/ios-library", "state": {"revision": "53040c77617a2acc5d9a7b69cf5bbbf36f94ad4b", "version": "17.7.3"}}, {"identity": "ios_sdk", "kind": "remoteSourceControl", "location": "https://github.com/adjust/ios_sdk", "state": {"revision": "a21ecccfcf15e6333b14797b6693bec5fc0e03e9", "version": "5.4.1"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1", "version": "1.22.5"}}, {"identity": "lottie-ios", "kind": "remoteSourceControl", "location": "https://github.com/airbnb/lottie-ios", "state": {"revision": "fe4c6fe3a0aa66cdeb51d549623c82ca9704b9a5", "version": "4.5.0"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1", "version": "2.30910.0"}}, {"identity": "navigationbackport", "kind": "remoteSourceControl", "location": "https://github.com/johnpatrickmorgan/NavigationBackport", "state": {"revision": "fb860a404f8c0aabeca1016400e41ece5363a369", "version": "0.9.0"}}, {"identity": "og-dx_aac-ios-module-otto_group_commerce_kit", "kind": "remoteSourceControl", "location": "**************:aacml/og-dx_aac-ios-module-otto_group_commerce_kit.git", "state": {"branch": "OGAK-3736-WIT-PoC", "revision": "ad5aef3808c8ba5a454eb6bf929eb5a81fceea12"}}, {"identity": "og-dx_aac-multiplatform-sdk", "kind": "remoteSourceControl", "location": "https://github.com/aacml/og-dx_aac-multiplatform-sdk.git", "state": {"revision": "3ab801b92f163b11f251d0a32dabd2a5ab6212ec", "version": "6.2.0-9.alpha.witt.4"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "snowplow-ios-tracker", "kind": "remoteSourceControl", "location": "https://github.com/snowplow/snowplow-ios-tracker", "state": {"revision": "20b1fea9c58334e569cb63d71875d3c2d0243483", "version": "6.0.7"}}, {"identity": "swift-async-algorithms", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-async-algorithms", "state": {"revision": "9cfed92b026c524674ed869a4ff2dcfdeedf8a2a", "version": "0.1.0"}}, {"identity": "swift-collections", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-collections.git", "state": {"revision": "9bf03ff58ce34478e66aaee630e491823326fd06", "version": "1.1.3"}}, {"identity": "swift-concurrency-extras", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-concurrency-extras", "state": {"revision": "bb5059bde9022d69ac516803f4f227d8ac967f71", "version": "1.1.0"}}, {"identity": "swift-http-types", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-http-types.git", "state": {"revision": "1827dc94bdab2eb5f2fc804e9b0cb43574282566", "version": "1.0.2"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "564597ad2fe2513a94dd8f3ba27ea2ff4be3cb37", "version": "1.28.0"}}, {"identity": "swift-syntax", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-syntax.git", "state": {"revision": "6ad4ea24b01559dde0773e3d091f1b9e36175036", "version": "509.0.2"}}, {"identity": "swiftbackports", "kind": "remoteSourceControl", "location": "https://github.com/shaps80/SwiftBackports", "state": {"revision": "ddca6a237c1ba2291d5a3cc47ec8480ce6e9f805", "version": "1.0.3"}}, {"identity": "swiftuibackports", "kind": "remoteSourceControl", "location": "https://github.com/shaps80/SwiftUIBackports.git", "state": {"revision": "3042f0cf4b9f0d5b0bb08ad17f742a43bc4fdfc5", "version": "2.8.0"}}, {"identity": "ziparchive", "kind": "remoteSourceControl", "location": "https://github.com/ZipArchive/ZipArchive.git", "state": {"revision": "38e0ce0598e06b034271f296a8e15b149c91aa19", "version": "2.4.3"}}], "version": 2}