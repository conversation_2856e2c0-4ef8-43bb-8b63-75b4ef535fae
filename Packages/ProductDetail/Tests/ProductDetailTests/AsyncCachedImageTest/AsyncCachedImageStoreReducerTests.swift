import SwiftUI
import XCTest
@testable import ProductDetail

final class AsyncCachedImageStoreReducerTests: XCTestCase {
  // MARK: - Reducer Tests

  /// Test: When _receivedImage event is passed, the state should update with the image and success loading
  func test_GIVEN_receivedImageEvent_WHEN_reduced_THEN_stateUpdatesWithImage() {
    // Arrange
    var state = AsyncCachedImageViewState()
    let testUIImage = UIImage(systemName: "circle")!

    // Act
    AsyncCachedImage<EmptyView, EmptyView>.Reducer.reduce(&state, with: ._receivedImage(testUIImage))

    // Assert
    XCTAssertEqual(state.loading, .success)
    XCTAssertNotNil(state.image)
  }

  /// Test: When loadImage event is passed, the state should remain unchanged
  func test_GIVEN_loadImageEvent_WHEN_reduced_THEN_stateRemainsUnchanged() {
    // Arrange
    var state = AsyncCachedImageViewState()
    let initialState = state

    // Act
    AsyncCachedImage<EmptyView, EmptyView>.Reducer.reduce(&state, with: .loadImage(nil))

    // Assert
    XCTAssertEqual(state, initialState)
  }

  /// Test: When _startLoad event is passed, the loading state should change to progress
  func test_GIVEN_startLoadImageEvent_WHEN_reduced_THEN_stateLoadingIsProgress() {
    // Arrange
    var state = AsyncCachedImageViewState()

    // Act
    AsyncCachedImage<EmptyView, EmptyView>.Reducer.reduce(&state, with: ._startLoadImage(.stub))

    // Assert
    XCTAssertEqual(state.loading, .progress)
  }

  // MARK: - Memory Management Tests

  /// Test: When clearMemory event is passed, the state should clear image and set memory cleared flag
  func test_GIVEN_clearMemoryEvent_WHEN_reduced_THEN_stateMemoryIsCleared() {
    // Arrange
    var state = AsyncCachedImageViewState(
      image: Image(systemName: "circle"),
      loading: .success
    )

    // Act
    AsyncCachedImage<EmptyView, EmptyView>.Reducer.reduce(&state, with: .clearMemory)

    // Assert
    XCTAssertNil(state.image)
    XCTAssertEqual(state.loading, .initial)
    XCTAssertTrue(state.isMemoryCleared)
  }

  /// Test: When reloadIfNeeded event is passed, the memory cleared flag should be reset
  func test_GIVEN_reloadIfNeededEvent_WHEN_reduced_THEN_memoryClearedFlagIsReset() {
    // Arrange
    var state = AsyncCachedImageViewState(isMemoryCleared: true)

    // Act
    AsyncCachedImage<EmptyView, EmptyView>.Reducer.reduce(&state, with: .reloadIfNeeded(.stub))

    // Assert
    XCTAssertFalse(state.isMemoryCleared)
  }

  /// Test: When _receivedImage event is passed, the memory cleared flag should be reset
  func test_GIVEN_receivedImageEvent_WHEN_reduced_THEN_memoryClearedFlagIsReset() {
    // Arrange
    var state = AsyncCachedImageViewState(isMemoryCleared: true)
    let testUIImage = UIImage(systemName: "circle")!

    // Act
    AsyncCachedImage<EmptyView, EmptyView>.Reducer.reduce(&state, with: ._receivedImage(testUIImage))

    // Assert
    XCTAssertFalse(state.isMemoryCleared)
    XCTAssertNotNil(state.image)
    XCTAssertEqual(state.loading, .success)
  }
}
