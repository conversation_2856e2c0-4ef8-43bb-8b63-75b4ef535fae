import OGAppKitSDK
import OGDIService
import OGL10n
import Swift<PERSON>
import UICatalog

// MARK: - ShopUspsView

struct ShopUspsView: SwiftUI.View {
  @StateObject private var viewStore: Store
  @OGInjected(\StylesContainer.cornerRadius) private var cornerRadius
  @State private var orientation = UIInterfaceOrientation.unknown
  init(shopUsps: ShopUsps) {
    _viewStore = StateObject(wrappedValue: Self.makeStore(shopUsps: shopUsps))
  }

  var body: some SwiftUI.View {
    LazyVGrid(columns: orientation.isLandscape || UIDevice.current.userInterfaceIdiom == .pad ? [
      GridItem(.flexible(), spacing: UILayoutConstants.Default.padding),
      GridItem(.flexible(), spacing: UILayoutConstants.Default.padding)
    ] : [
      GridItem(.flexible(), spacing: UILayoutConstants.Default.padding)
    ], spacing: UILayoutConstants.ShopUsps.spacing) {
      ForEach(Array(viewStore.usps.enumerated()), id: \.offset) { index, usp in
        uspItemView(usp: usp, index: index)
      }
    }

    .accessibilityElement(children: .combine)
    .accessibilityAddTraits(.isSummaryElement)
    .accessibilityAddTraits(.isStaticText)
    .padding(UILayoutConstants.Default.padding2x)
    .frame(maxWidth: .infinity, alignment: .leading)
    .background(viewStore.isLoading ? .clear : OGColors.backgroundBackground10.color)
    .cornerRadius(cornerRadius.medium)
    .onRotate { orientation in
      self.orientation = orientation
    }
  }

  @ViewBuilder
  private func uspItemView(usp: UspsItem, index: Int) -> some SwiftUI.View {
    HStack(spacing: UILayoutConstants.Default.padding2x) {
      Image(usp.icon)
        .accessibilityHidden(true)
        .shimmering(active: viewStore.isLoading, cornerRadius: UILayoutConstants.Default.padding3x)
      Text(usp.text)
        .font(for: .copyMRegular)
        .foregroundColor(Color(usp.color))
        .accessibilityLabel(index == 0 ? "\(ogL10n.ProductDetail.ShopUsps.Title.Accessibility). \(usp.accessibilityLabel)" : usp.accessibilityLabel)
        .shimmering(active: viewStore.isLoading)
    }
    .frame(maxWidth: .infinity, alignment: .leading)
  }
}

// MARK: - UILayoutConstants.ShopUsps

extension UILayoutConstants {
  enum ShopUsps {
    static let spacing: CGFloat = 20
  }
}
