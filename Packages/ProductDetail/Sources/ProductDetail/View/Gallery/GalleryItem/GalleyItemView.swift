import Foundation
import SwiftUI
import UICatalog

struct GalleyItemView: View {
  let url: URL
  let isFullscreen: Bool
  @Binding var currentScale: CGFloat
  @State private var isVisible: Bool = true

  var body: some View {
    ZStack {
      if isFullscreen {
        ZoomContainer(scale: $currentScale) {
          imageView
        }
        .background(OGColors.backgroundBackground10.color)
        .frame(minWidth: 0, maxWidth: .infinity, minHeight: 0, maxHeight: .infinity, alignment: .center)
        .ignoresSafeArea()
      } else {
        imageView
      }
    }
    .onAppear {
      isVisible = true
    }
    .onDisappear {
      isVisible = false
    }
  }

  private var imageView: some View {
    AsyncCachedImage(
      url: url,
      targetAspectRatio: UILayoutConstants.GalleryView.aspectRatio,
      isVisible: $isVisible,
      enableMemoryManagement: true
    ) { image in
      if isFullscreen {
        image
          .resizable()
          .aspectRatio(contentMode: .fit)
          .frame(minWidth: 0, maxWidth: .infinity, minHeight: 0, maxHeight: .infinity, alignment: .center)
      } else {
        image
          .resizable()
      }
    } placeholder: { loadingState in
      if case .failure = loadingState {
        OGImages.icon24x24PlaceholderImg.image
          .accessibilityHidden(true)
      } else {
        Rectangle()
          .fill(.clear)
          .shimmering(cornerRadius: .zero)
      }
    }
  }
}
