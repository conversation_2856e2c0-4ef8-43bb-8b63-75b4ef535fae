import Foundation
import OGAppKitSDK
import OGDIService
import OGL10n
import OGRouter
import SwiftUI
import UICatalog

// MARK: - GalleryView

struct GalleryView: SwiftUI.View {
  @StateObject private var viewStore: Self.Store
  @State private var indexOfCurrentImage: Int
  @State private var currentScale: CGFloat = 1.0
  private let lastGalleryIndex: ((Int) -> Void)?
  @OGInjected(\StylesContainer.cornerRadius) private var cornerRadius
  enum Focusable {
    case image
    case nextButton
    case previousButton
  }

  init(
    gallery: ProductGallery,
    isFullscreen: Bool = false,
    indexOfCurrentImage: Int = 0,
    route: OGRoute? = nil,
    lastGalleryIndex: ((Int) -> Void)? = nil
  ) {
    _viewStore = StateObject(
      wrappedValue: Self.makeStore(
        gallery: gallery,
        indexOfCurrentImage: indexOfCurrentImage,
        route: route,
        isFullscreen: isFullscreen
      )
    )
    self.indexOfCurrentImage = indexOfCurrentImage
    self.lastGalleryIndex = lastGalleryIndex
  }

  var body: some SwiftUI.View {
    if viewStore.isFullscreen {
      ZStack {
        gallery
          .background(OGColors.backgroundBackground10.color)
          .ignoresSafeArea()
          .accessibilitySortPriority(1)

        VStack {
          HStack {
            Spacer()
            dismissButton
              .accessibilitySortPriority(10)
          }
          Spacer()
        }

        VStack {
          HStack {
            imageCount
              .accessibilityHidden(true)
            Spacer()
          }
          Spacer()
        }
      }
      .onChange(of: currentScale) { newValue in
        Task {
          await viewStore.dispatch(.currentScale(newValue))
        }
      }
      .onChange(of: viewStore.currentScale) { newValue in
        currentScale = newValue
      }
      .transition(.asymmetric(
        insertion: .opacity.animation(.easeInOut(duration: 0.5)),
        removal: .opacity.animation(.easeInOut(duration: 0.3))
      ))
      .onAppear {
        Task {
          await viewStore.dispatch(.trackScreenView)
        }
      }
    } else {
      ZStack {
        gallery
          .aspectRatio(UILayoutConstants.GalleryView.aspectRatio, contentMode: .fill)
          .frame(maxWidth: .infinity)
          .shimmering(active: viewStore.isLoading, cornerRadius: .zero)
          .onTapGesture {
            Task {
              await viewStore.dispatch(.tapedImage)
            }
          }
          .overlay(alignment: .topLeading) {
            imageCount
              .accessibilityHidden(true)
          }
          .overlay(alignment: .bottomLeading) {
            flagView
              .accessibilityHidden(true)
          }

        if !viewStore.isLoading {
          VStack {
            Spacer()
            HStack {
              Spacer()
              WishlistButtonView(
                isWishlisted: viewStore.isWishlisted,
                productId: viewStore.productIdForWishlisting,
                hasCircleBackground: true
              )
              .monitorVisibility(withID: MonitoredView.galleryWishlistIcon)
              .padding(UILayoutConstants.GalleryView.floatingButtonPadding)
              .accessibilitySortPriority(2)
            }
          }
        }
      }
    }
  }

  @ViewBuilder private var flagView: some SwiftUI.View {
    if !viewStore.isFullscreen, !viewStore.flags.isEmpty {
      VStack(alignment: .leading, spacing: UILayoutConstants.GalleryView.flagStackSpacing) {
        ForEach(viewStore.flags, id: \.self) { flag in
          flagView(
            text: flag.name,
            textColor: flag.textColor,
            backgroundColor: flag.backgroundColor
          )
        }
      }
      .padding([.leading, .bottom], UILayoutConstants.Default.padding2x)
    }
  }

  func flagView(text: String, textColor: Color, backgroundColor: Color) -> some SwiftUI.View {
    Text(text)
      .lineLimit(UILayoutConstants.GalleryView.flagTextLineLimit)
      .font(for: .label)
      .foregroundColor(textColor)
      .padding(.horizontal, UILayoutConstants.GalleryView.flagHorizontalPadding)
      .padding(.vertical, UILayoutConstants.GalleryView.flagVerticalPadding)
      .background(
        RoundedRectangle(cornerRadius: cornerRadius.small)
          .fill(backgroundColor)
      )
  }

  @ViewBuilder private var dismissButton: some SwiftUI.View {
    ProductDetailCloseButton(accessibilityLabel: ogL10n.General.Close) {
      Task {
        await viewStore.dispatch(.dismiss)
      }
    }
    .padding(.trailing, UILayoutConstants.Default.padding2x)
    .padding(.top, UILayoutConstants.Default.padding)
  }

  @ViewBuilder private var nextButton: some SwiftUI.View {
    Button(action: {
      Task {
        await viewStore.dispatch(.moveToNextImage)
      }
    }, label: {
      OGImages.icon24x24ChevronRightPrimary.image
        .circleBackground()
    })
    .padding(UILayoutConstants.GalleryView.floatingButtonPadding)
  }

  @ViewBuilder private var previousButton: some SwiftUI.View {
    Button(action: {
      Task {
        await viewStore.dispatch(.moveToPreviousImage)
      }
    }, label: {
      OGImages.icon24x24ChevronLeftPrimary.image
        .circleBackground()
    })
    .padding(UILayoutConstants.GalleryView.floatingButtonPadding)
  }

  @ViewBuilder private var imageCount: some SwiftUI.View {
    if viewStore.shouldShowImageCount, !viewStore.isLoading {
      HStack(alignment: .center, spacing: UILayoutConstants.GalleryView.imageCountSpacing) {
        Text(viewStore.imageCountCurrentLabel)
          .font(for: OGFonts.copyMRegular)
          .foregroundColor(OGColors.textBlack.color)
          .multilineTextAlignment(.trailing)

        Text("/")
          .font(for: OGFonts.copyMRegular)
          .foregroundColor(OGColors.textBlack60.color)
          .multilineTextAlignment(.trailing)

        Text(viewStore.imageCountMaxLabel)
          .font(for: OGFonts.copyMRegular)
          .foregroundColor(OGColors.textBlack60.color)
          .multilineTextAlignment(.leading)
      }
      .padding(UILayoutConstants.GalleryView.imageCountPadding)
      .frame(minHeight: UILayoutConstants.GalleryView.imageCountMinHeight)
      .background(OGColors.backgroundBackground0.color)
      .cornerRadius(cornerRadius.small)
      .padding(UILayoutConstants.Default.padding2x)
    }
  }

  @ViewBuilder private var gallery: some SwiftUI.View {
    TabView(selection: $indexOfCurrentImage) {
      ForEach(Array(viewStore.state.urls.enumerated()), id: \.offset) { index, url in
        GalleyItemView(
          url: url,
          isFullscreen: viewStore.isFullscreen,
          currentScale: $currentScale
        )
        .id(index)
        .onAppear {
          Task {
            await viewStore.dispatch(.prefetchNextImageFor(index))
          }
        }
      }
    }
    .tabViewStyle(PageTabViewStyle(indexDisplayMode: .never))
    .accessibilityElement(children: .combine)
    .accessibilityAddTraits(viewStore.isFullscreen ? [] : .isButton)
    .accessibilitySortPriority(1)
    .accessibilityValue(viewStore.accessibilityValue)
    .accessibilityAdjustableAction { direction in
      switch direction {
      case .increment where viewStore.showsNextButton:
        Task {
          await viewStore.dispatch(.moveToNextImage)
        }
      case .decrement where viewStore.showsPreviousButton:
        Task {
          await viewStore.dispatch(.moveToPreviousImage)
        }
      default:
        break
      }
    }
    .onChange(of: indexOfCurrentImage, perform: { newValue in
      Task {
        await viewStore.dispatch(.indexOfCurrentImage(newValue))
      }
    })
    .onChange(of: viewStore.indexOfCurrentImage, perform: { newValue in
      withAnimation {
        if indexOfCurrentImage != newValue {
          indexOfCurrentImage = newValue
        }
      }
      lastGalleryIndex?(newValue)
    })
  }
}

// MARK: - UILayoutConstants.GalleryView

extension UILayoutConstants {
  enum GalleryView {
    static let flagHorizontalPadding: CGFloat = 12
    static let flagVerticalPadding: CGFloat = 4
    static let flagStackSpacing: CGFloat = 4
    static let flagTextLineLimit: Int = 1
    static let imageCountMinHeight: CGFloat = 26.0
    static let imageCountPadding: EdgeInsets = .init(top: 2.0, leading: 8.0, bottom: 2.0, trailing: 8.0)
    static let imageCountSpacing: CGFloat = 2.0
    static let aspectRatio: CGSize = .init(width: 5, height: 7)
    static let floatingButtonPadding: CGFloat = 19
  }
}
