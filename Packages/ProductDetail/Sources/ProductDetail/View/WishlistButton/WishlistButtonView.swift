import OGL10n
import SwiftUI
import UICatalog
struct WishlistButtonView: SwiftUI.View {
  @State var isWishlisted: Bool
  let productId: String?
  let hasCircleBackground: Bool
  let circleBackgroundSize: CGSize
  @StateObject private var viewStore: Self.Store = Self.makeStore()
  @AccessibilityFocusState private var isAccessibilityFocused: Bool

  init(
    isWishlisted: Bool,
    productId: String?,
    hasCircleBackground: Bool = false,
    circleBackgroundSize: CGSize = CGSize(width: 48, height: 48)
  ) {
    _isWishlisted = State(wrappedValue: isWishlisted)

    self.productId = productId
    self.hasCircleBackground = hasCircleBackground
    self.circleBackgroundSize = circleBackgroundSize
  }

  private var accessibilityLabel: String {
    isWishlisted ? ogL10n.ProductDetail.Wishlist.Button.Title.IsWishlisted.Accessibility : ogL10n.ProductDetail.Wishlist.Button.Title.IsNotWishlisted.Accessibility
  }

  var body: some SwiftUI.View {
    Button {
      isWishlisted.toggle()
      isAccessibilityFocused = true
      Task {
        await viewStore.dispatch(.toggleWishlist(!isWishlisted))
      }
    } label: {
      if hasCircleBackground {
        imageWithCircleBackground
      } else {
        image
      }
    }
    .onChange(of: viewStore.isWishlisted) { newValue in
      isWishlisted = newValue
    }
    .task {
      await viewStore.dispatch(.setProductID(productId))
    }
    .accessibilityLabel(accessibilityLabel)
    .accessibilityFocused($isAccessibilityFocused)
    .accessibilityAddTraits(.isButton)
  }

  private var image: some SwiftUI.View {
    Image(isWishlisted ? OGImages.icon24x24WishlistFilled.name : OGImages.icon24x24Wishlist.name)
  }

  private var imageWithCircleBackground: some SwiftUI.View {
    image
      .circleBackground(size: circleBackgroundSize)
  }
}
