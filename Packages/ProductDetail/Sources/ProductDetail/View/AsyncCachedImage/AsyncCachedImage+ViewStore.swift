import Combine
import Foundation
import OGViewStore
import SwiftUI
import UICatalog
extension AsyncCachedImage {
  typealias Store = OGViewStore<AsyncCachedImageViewState, Event>
  @MainActor
  static func makeStore(targetSize: CGSize? = nil, targetAspectRatio: CGSize? = nil) -> Store {
    AsyncCachedImage.Store(
      reducer: AsyncCachedImage.Reducer.reduce,
      middleware: AsyncCachedImage.Middleware(targetSize: targetSize, targetAspectRatio: targetAspectRatio)
    )
  }
}

// MARK: - AsyncCachedImageViewState

public struct AsyncCachedImageViewState: OGViewState {
  private(set) var image: Image?
  private(set) var loading: Loadable = .initial
  private(set) var isMemoryCleared: Bool = false
  private(set) var lastLoadedURL: URL?

  public init(
    image: Image? = nil,
    loading: Loadable = .initial,
    isMemoryCleared: Bool = false,
    lastLoadedURL: URL? = nil
  ) {
    self.image = image
    self.loading = loading
    self.isMemoryCleared = isMemoryCleared
    self.lastLoadedURL = lastLoadedURL
  }

  mutating func update(
    image: Image? = nil,
    loading: Loadable? = nil,
    isMemoryCleared: Bool? = nil,
    lastLoadedURL: URL? = nil
  ) {
    if let image = image {
      self.image = image
    }
    if let loading = loading {
      self.loading = loading
    }
    if let isMemoryCleared = isMemoryCleared {
      self.isMemoryCleared = isMemoryCleared
    }
    if let lastLoadedURL = lastLoadedURL {
      self.lastLoadedURL = lastLoadedURL
    }
  }

  mutating func clearMemory() {
    self.image = nil
    self.loading = .initial
    self.isMemoryCleared = true
  }

  public static var initial = AsyncCachedImageViewState()

  // MARK: - Equatable

  public static func == (lhs: AsyncCachedImageViewState, rhs: AsyncCachedImageViewState) -> Bool {
    lhs.loading == rhs.loading &&
    lhs.isMemoryCleared == rhs.isMemoryCleared &&
    lhs.lastLoadedURL == rhs.lastLoadedURL
    // Note: We don't compare images directly as SwiftUI.Image doesn't conform to Equatable
  }
}

// MARK: - AsyncCachedImage.Event

extension AsyncCachedImage {
  enum Event: OGViewEvent {
    case loadImage(URL?)
    case clearMemory
    case reloadIfNeeded(URL?)
    /// private
    case _startLoadImage(URL)
    case _receivedImage(UIImage)
    case _loadingFailed
    case _memoryCleared

    // MARK: - Equatable

    static func == (lhs: Event, rhs: Event) -> Bool {
      switch (lhs, rhs) {
      case let (.loadImage(lhsURL), .loadImage(rhsURL)):
        return lhsURL == rhsURL
      case (.clearMemory, .clearMemory):
        return true
      case let (.reloadIfNeeded(lhsURL), .reloadIfNeeded(rhsURL)):
        return lhsURL == rhsURL
      case let (._startLoadImage(lhsURL), ._startLoadImage(rhsURL)):
        return lhsURL == rhsURL
      case let (._receivedImage(lhsImage), ._receivedImage(rhsImage)):
        // UIImage doesn't conform to Equatable, so we compare by reference
        return lhsImage === rhsImage
      case (._loadingFailed, ._loadingFailed):
        return true
      case (._memoryCleared, ._memoryCleared):
        return true
      default:
        return false
      }
    }
  }
}

extension AsyncCachedImage {
  enum Reducer {
    static func reduce(
      _ state: inout AsyncCachedImageViewState,
      with event: AsyncCachedImage.Event
    ) {
      switch event {
      case .loadImage:
        break
      case .clearMemory:
        state.clearMemory()
      case .reloadIfNeeded:
        // Reset memory cleared flag when reloading
        state.update(isMemoryCleared: false)
      case let ._startLoadImage(url):
        state.update(loading: .progress, lastLoadedURL: url)
      case let ._receivedImage(image):
        state.update(image: Image(uiImage: image), loading: .success, isMemoryCleared: false)
      case ._loadingFailed:
        state.update(loading: .failure(AsyncCachedImageDownloadError.failed))
      case ._memoryCleared:
        break
      }
    }
  }

  struct Middleware: OGViewStoreMiddleware {
    private let targetSize: CGSize?
    private let targetAspectRatio: CGSize?
    private let imageDownloader: AsyncCachedImageDownloadable
    init(
      targetSize: CGSize? = nil,
      targetAspectRatio: CGSize? = nil,
      imageDownloader: AsyncCachedImageDownloadable = AsyncCachedImageDownloader()
    ) {
      self.targetSize = targetSize
      self.targetAspectRatio = targetAspectRatio
      self.imageDownloader = imageDownloader
    }

    func callAsFunction(
      event: AsyncCachedImage.Event,
      for state: AsyncCachedImageViewState
    ) async
      -> AsyncCachedImage.Event? {
      switch event {
      case let .loadImage(url):
        guard let url else {
          return ._loadingFailed
        }
        return ._startLoadImage(url)
      case .clearMemory:
        return ._memoryCleared
      case let .reloadIfNeeded(url):
        // Only reload if memory was cleared and we have a URL
        guard state.isMemoryCleared, let url else {
          return nil
        }
        return ._startLoadImage(url)
      case let ._startLoadImage(url):
        do {
          let data = try await imageDownloader.downloadImage(url: url)
          guard !data.isEmpty, let originalImage = UIImage(data: data) else {
            return ._loadingFailed
          }

          let scaledImage: UIImage
          if let targetSize, let targetAspectRatio {
            // Use aspect ratio scaling with size constraints and transparent padding
            scaledImage = originalImage.scaledToFitWithAspectRatio(
              targetSize: targetSize,
              targetAspectRatio: targetAspectRatio
            ) ?? originalImage
          } else if let targetSize {
            // Use regular scaling without aspect ratio enforcement
            scaledImage = originalImage.scaledDownToFit(targetSize: targetSize) ?? originalImage
          } else if let targetAspectRatio {
            // Use aspect ratio padding without size constraints
            scaledImage = originalImage.paddedToAspectRatio(targetAspectRatio) ?? originalImage
          } else {
            // Keep original image if it's already reasonably sized
            scaledImage = originalImage
          }

          return ._receivedImage(scaledImage)

        } catch {
          return ._loadingFailed
        }
      case ._loadingFailed, ._receivedImage, ._memoryCleared:
        return nil
      }
    }
  }
}

extension UIImage {
  func scaledDownToFit(targetSize: CGSize) -> UIImage? {
    let originalSize = size

    if originalSize.width <= targetSize.width, originalSize.height <= targetSize.height {
      return self
    }

    let widthScale = targetSize.width / originalSize.width
    let heightScale = targetSize.height / originalSize.height
    let scaleFactor = min(widthScale, heightScale)

    let newSize = CGSize(
      width: originalSize.width * scaleFactor,
      height: originalSize.height * scaleFactor
    )

    let format = UIGraphicsImageRendererFormat()
    format.scale = UIScreen.mainScreen.scale
    format.opaque = true

    let renderer = UIGraphicsImageRenderer(size: newSize, format: format)
    return renderer.image { _ in
      self.draw(in: CGRect(origin: .zero, size: newSize))
    }
  }

  func scaledToFitWithAspectRatio(targetSize: CGSize, targetAspectRatio: CGSize) -> UIImage? {
    let scaledImage = scaledDownToFit(targetSize: targetSize) ?? self
    return scaledImage.paddedToAspectRatio(targetAspectRatio) ?? scaledImage
  }

  func paddedToAspectRatio(_ targetAspectRatio: CGSize) -> UIImage? {
    let originalSize = size
    let targetRatio = targetAspectRatio.width / targetAspectRatio.height
    let originalRatio = originalSize.width / originalSize.height
    let aspectRatioTolerance: CGFloat = 0.05

    if abs(targetRatio - originalRatio) < aspectRatioTolerance {
      return self
    }
    let canvasSize: CGSize
    if targetRatio > originalRatio {
      canvasSize = CGSize(width: originalSize.height * targetRatio, height: originalSize.height)
    } else {
      canvasSize = CGSize(width: originalSize.width, height: originalSize.width / targetRatio)
    }

    let format = UIGraphicsImageRendererFormat()
    format.scale = scale
    format.opaque = false

    let renderer = UIGraphicsImageRenderer(size: canvasSize, format: format)
    return renderer.image { context in
      context.cgContext.clear(CGRect(origin: .zero, size: canvasSize))
      let x = (canvasSize.width - originalSize.width) / 2
      let y = (canvasSize.height - originalSize.height) / 2
      self.draw(in: CGRect(x: x, y: y, width: originalSize.width, height: originalSize.height))
    }
  }
}
