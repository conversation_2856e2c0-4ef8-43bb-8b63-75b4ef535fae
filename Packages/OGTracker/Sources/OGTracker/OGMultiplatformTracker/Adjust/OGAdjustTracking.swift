import AdjustSdk
import Combine
import OGAdjustReporter
import <PERSON>GAppKitSDK
import OGDIService

public typealias OGAdjustTracking = OGAppKitSDK.AdjustAnalytics

// MARK: - OGAdjustTracker

public final class OGAdjustTracker: OGAdjustTracking {
  @OGInjected(\OGAdjustReporterFeatureAdapterContainer.adapter) private var featureAdapter

  private let tracker: OGTracking
  private var cancellables = Set<AnyCancellable>()
  public init(tracker: OGTracking = OGAppKitSdk.shared.tracking()) {
    self.tracker = tracker
    observeFeatureAdapter()
  }

  private func observeFeatureAdapter() {
    featureAdapter
      .configuration
      .removeDuplicates(by: { lhs, rhs in
        lhs.isEnabled == rhs.isEnabled
      })
      .sink { [weak self] config in

        let config = AdjustConfig(
          isEnabled: config.isEnabled,
          eventTokenMapping: config.eventTokenMapping,
          globalContext: []
        )

        self?.tracker.onUpdateConfig(id: .adjust, config: config)
      }
      .store(in: &cancellables)
  }

  public func addGlobalPartnerParameter(key: String, value: String) {
    Adjust.addGlobalPartnerParameter(value, forKey: key)
  }

  public func clearAllGlobalPartnerParameters() {
    Adjust.removeGlobalPartnerParameters()
  }

  public func logEvent(event: OGAppKitSDK.AdjustEvent) {
    let adjustEvent = ADJEvent(eventToken: event.token)
    if let revenue = event.revenue {
      adjustEvent?.setRevenue(revenue.amount, currency: revenue.currency)
    }
    event.partnerParameters.forEach { key, value in
      adjustEvent?.addPartnerParameter(key, value: value)
    }
    Adjust.trackEvent(adjustEvent)
  }

  public func removeGlobalPartnerParameter(key: String) {
    Adjust.removeGlobalPartnerParameter(forKey: key)
  }

  public func setEnabled(enabled: Bool) {
    if enabled {
      Adjust.enable()
    } else {
      Adjust.disable()
    }
  }

  public func setThirdPartySharingEnabled(enabled: Bool) {
    guard let trackThirdPartySharing = ADJThirdPartySharing(isEnabled: NSNumber(booleanLiteral: enabled)) else { return }
    Adjust.trackThirdPartySharing(trackThirdPartySharing)
  }

  public func setUserProperty(key _: String, value _: String?) {}

  public func openDeepLinkUri(uri: String) {
    URL(string: uri)
      .flatMap(ADJDeeplink.init)
      .map(Adjust.processDeeplink)
  }
}
