import OGNavigation
import SwiftUI

// MARK: - PushPromotionProvider

public struct PushPromotionProvider: OGDestinationProvidable {
  public private(set) var identifier: OGIdentifier

  public init() {
    self.identifier = OGIdentifier.pushPromotion
  }

  public func provide(_ route: OGRoute) -> some View {
    if let model: PushPromotionModel = route.getData() {
      return PushPromotionView(from: .promotionWebBridge, with: model)
    }

    let pushRouteOrigin = PushRouteOrigin(fromRawValue: route.url.valueOf("og_origin"))
    return PushPromotionView(from: pushRouteOrigin)
  }

  public func presentationType() -> OGPresentationType {
    .sheet()
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}

extension OGRoute {
  public static let pushPromotion = OGRoute(OGIdentifier.pushPromotion.value)
}

extension URL {
  func valueOf(_ queryParameterName: String) -> String? {
    guard let url = URLComponents(string: absoluteString) else { return nil }
    return url.queryItems?.first(where: { $0.name == queryParameterName })?.value
  }
}
