import OGAsyncImage
import OGCore
import OGDIService
import OGL10n
import Swift<PERSON>
import UICatalog

// MARK: - PushPromotionView

struct PushPromotionView: View {
  @StateObject private var viewStore: Self.Store
  @Environment(\.dynamicTypeSize) var dynamicTypeSize

  public init(from pushRouteOrigin: PushRouteOrigin = .none, with pushPromotionModel: PushPromotionModel? = nil) {
    _viewStore = StateObject(
      wrappedValue: Self.make(
        with: pushRouteOrigin,
        remotePushPromotionModel: pushPromotionModel
      )
    )
  }

  var body: some View {
    VStack(spacing: UILayoutConstants.PushPromotionView.zeroSpacing) {
      BottomSheetHeader {
        Task {
          await viewStore.dispatch(.dismiss)
        }
      }

      ScrollView {
        VStack(spacing: UILayoutConstants.PushPromotionView.zeroSpacing) {
          imageStack
            .padding(UILayoutConstants.Default.padding3x)
          promotionText
            .padding(.bottom, UILayoutConstants.Default.padding2x)
            .padding(.horizontal, UILayoutConstants.Default.padding3x)
          uspStack
            .padding(.bottom, UILayoutConstants.Default.padding2x)
            .padding(.horizontal, UILayoutConstants.Default.padding4x)
          actionButton
            .padding(.bottom, UILayoutConstants.PushPromotionView.buttonBottomPadding)
            .padding(.horizontal, UILayoutConstants.Default.padding3x)
        }
      }
      .basedOnSizeScrollBehavior()
    }
    .navigationBarHidden(true)
    .background(OGColors.backgroundBackground0.color)
    .accessibilityElement(children: .contain)
    .accessibilityAddTraits(.isModal)
    .accessibilityLabel(ogL10n.PushPromotion.OptIn.PageTitle.Accessibility)
    .accessibilityHint(ogL10n.PushPromotion.OptIn.Title)
    .onAppear {
      Task {
        await viewStore.dispatch(.didAppear(.infoLayer))
      }
    }
  }

  private var imageStack: some View {
    ZStack {
      OGImages.pushPromotionHand.image
        .resizable()
        .aspectRatio(contentMode: .fit)
        .accessibilityHidden(true)
        .frame(maxWidth: UILayoutConstants.PushPromotionView.maxFrameSize, maxHeight: UILayoutConstants.PushPromotionView.maxFrameSize)
      OGImages.pushPromotionMessage.image
        .resizable()
        .aspectRatio(contentMode: .fit)
        .accessibilityHidden(true)
        .frame(maxWidth: UILayoutConstants.PushPromotionView.maxFrameSize, maxHeight: UILayoutConstants.PushPromotionView.maxFrameSize)
    }
  }

  private var promotionText: some View {
    Text(viewStore.headline)
      .lineSpacing(UILayoutConstants.Default.lineSpacing)
      .font(for: .headlineLEmphasized)
      .foregroundColor(OGColors.textOnLight.color)
      .multilineTextAlignment(.center)
      .accessibilityAddTraits(.isHeader)
      .accessibilityHint(viewStore.accessibilityHintOverlay)
  }

  private func rows<T>(_ items: [T], columns: Int) -> [[T]] {
    stride(from: 0, to: items.count, by: columns).map {
      Array(items[$0 ..< min($0 + columns, items.count)])
    }
  }

  private var uspStack: some View {
    let count = columnCount(for: dynamicTypeSize)
    return VStack(alignment: .center) {
      ForEach(Array(rows(viewStore.uspArguments, columns: count).enumerated()), id: \.offset) { _, row in
        HStack(alignment: .top) {
          ForEach(Array(row.enumerated()), id: \.offset) { _, usp in
            uspVStack(usp: usp)
          }
        }
        .frame(maxWidth: .infinity, alignment: .center)
      }
    }
    .frame(maxWidth: .infinity, alignment: .center)
  }

  private func columnCount(for typeSize: DynamicTypeSize) -> Int {
    switch typeSize {
    case ..<DynamicTypeSize.accessibility1: return 3
    case ..<DynamicTypeSize.accessibility3: return 2
    default: return 1
    }
  }

  private func uspVStack(usp: USPViewModel) -> some View {
    VStack(alignment: .center) {
      AnyView(usp.image)
        .frame(
          width: UILayoutConstants.PushPromotionView.uspIconSize,
          height: UILayoutConstants.PushPromotionView.uspIconSize
        )
        .accessibilityHidden(true)
      Text(usp.text)
        .lineSpacing(UILayoutConstants.Default.lineSpacing)
        .font(for: .copyMRegular)
        .foregroundColor(OGColors.textOnLight.color)
        .multilineTextAlignment(.center)
    }
    .accessibilityElement(children: .combine)
    .multilineTextAlignment(.center)
    .frame(maxWidth: .infinity)
  }

  private var actionButton: some View {
    C2AButton(
      title: ogL10n.PushPromotion.OptIn.Button,
      accessibilityIdentifier: AccessibilityIdentifier.actionButton
    ) {
      Task {
        await viewStore.dispatch(.didTapConfirm)
      }
    }
    .dynamicTypeSize(...DynamicTypeSize.accessibility1)
  }
}

// MARK: PushPromotionView.AccessibilityIdentifier

extension PushPromotionView {
  enum AccessibilityIdentifier {
    static let actionButton: String = "pushLayer_button_activate"
  }
}

// MARK: - UILayoutConstants.PushPromotionView

extension UILayoutConstants {
  enum PushPromotionView {
    static let zeroSpacing: CGFloat = 0.0
    static let imageSpacingBetweenVStack: CGFloat = 40.0
    static let uspIconSize: CGFloat = 24.0
    static let buttonBottomPadding: CGFloat = 54.0
    static let maxFrameSize: CGFloat = .infinity
  }
}
