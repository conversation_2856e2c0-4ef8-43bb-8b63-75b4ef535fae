import SwiftUI
import UICatalog

// MARK: - DealLegalView

struct DealLegalView: View {
  @Environment(\.screenSize) private var screenSize

  @StateObject private var viewStore: Store

  @SwiftUI.State private var legalText: AttributedString = ""
  @SwiftUI.State private var contentSize: CGSize = .zero

  init(deal: Deal) {
    _viewStore = StateObject(wrappedValue: Self.make(deal: deal))
  }

  var body: some View {
    BottomSheetView(title: viewStore.legalHeaderText) {
      ScrollView {
        VStack(spacing: .zero) {
          styledLegalText
          Spacer(minLength: UILayoutConstants.Default.padding3x)
        }
      }
      .frame(height: clampedContentHeight)
    }
    .onAppear(perform: loadLegalText)
  }

  private var styledLegalText: some View {
    HStack {
      Text(String(legalText.characters))
        .font(for: .copyMRegular)
        .foregroundStyle(Color.clear)
        .overlay {
          Text(legalText)
            .font(for: .copyMRegular)
            .foregroundStyle(OGColors.textOnLight.color)
            .multilineTextAlignment(.leading)
            .accessibilityHidden(true)
        }

      Spacer()
    }
    .dynamicTypeSize(...DynamicTypeSize.large)
    .background(
      SizeReader { size in
        contentSize = size
      }
    )
  }

  private var clampedContentHeight: CGFloat {
    let maxContentHeight = screenSize.height * UILayoutConstants.BottomSheetView.detentFractionHeight
    return min(contentSize.height, maxContentHeight)
  }

  private func loadLegalText() {
    viewStore.legalText.htmlToMutableAttributedString { mutableString in
      if let mutableString, let font = OGFonts.copyMRegular.font {
        mutableString.setFont(font)
        legalText = AttributedString(mutableString)
      }
    }
  }
}
