import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

// MARK: - ProductReviewCardView

struct ProductReviewCardView: SwiftUI.View {
  let review: Review
  let minHeight: CGFloat
  let maxLines: Int
  private let isExpanded: Bool
  init(
    review: Review,
    isExpanded: Bool,
    minHeight: CGFloat = UILayoutConstants.ProductReview.cardMinHeight,
    maxLines: Int = UILayoutConstants.ProductReviewCardView.maxLines
  ) {
    self.review = review
    self.minHeight = minHeight
    self.maxLines = maxLines
    self.isExpanded = isExpanded
  }

  var body: some SwiftUI.View {
    VStack(alignment: .leading, spacing: UILayoutConstants.ProductReview.cardSpacing) {
      ProductReviewHeaderView(review: review)
        .padding(.trailing, UILayoutConstants.Default.padding2x)

      if let title = review.title {
        Text(title)
          .font(for: .titleM)
          .foregroundColor(OGColors.textOnLight.color)
          .padding(.trailing, UILayoutConstants.Default.padding2x)
          .multilineTextAlignment(.leading)
      }

      TruncableTextView(
        text: review.text,
        showMoreTitle: ogL10n.ProductReview.Card.ShowMoreReview.Button.Title,
        showLessTitle: ogL10n.ProductReview.Card.ShowLessReview.Button.Title,
        trailingTitle: review.reviewerName,
        contentSpacing: UILayoutConstants.ProductReviewCardView.contentSpacing,
        maxLines: maxLines,
        isExpanded: isExpanded,
        isTopAligned: false
      )
      .frame(minHeight: minHeight)
      .padding(.trailing, UILayoutConstants.Default.padding2x)
      .padding(.bottom, UILayoutConstants.Default.padding)
      Divider()
        .background(OGColors.backgroundBackground20.color)
        .padding(.trailing, UILayoutConstants.Default.padding2x)
    }
    .accessibilityElement(children: .ignore)
    .accessibilityLabel(accessibilityLabelForCard)
    .accessibilityAddTraits(.isStaticText)
  }

  private var accessibilityLabelForCard: String {
    var rating = ogL10n.ProductDetail.Review.Rating.Accessibility(rating: String(review.rating), date: review.formattedDate)
    if let reviewerName = review.reviewerName {
      rating += " " + ogL10n.ProductDetail.Review.ReviewerName.Accessibility(reviewerName: reviewerName)
    }
    rating += " \(review.title ?? "") \(review.text)"
    return rating
  }
}

// MARK: - UILayoutConstants.ProductReviewCardView

extension UILayoutConstants {
  enum ProductReviewCardView {
    static let contentSpacing: CGFloat = 8
    static let maxLines: Int = 3
  }
}
