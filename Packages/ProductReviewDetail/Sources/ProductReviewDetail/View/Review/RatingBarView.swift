import OGL10n
import SwiftUI
import UICatalog

struct RatingBarView: View {
  let rating: Int
  let count: Int
  let percentage: Double
  @State private var barWidth: CGFloat = 0
  var body: some View {
    AdaptiveHStack {
      Text(ogL10n.ProductReview.Filter.Stars(count: "\(rating)"))
        .font(for: .copyMRegular)
        .foregroundStyle(OGColors.textOnLight.color)
        .frame(minWidth: UILayoutConstants.ProductReview.ratingLabelWidth, alignment: .leading)
      bar
      Text("\(count)")
        .font(for: .copyMRegular)
        .foregroundStyle(OGColors.textOnLight.color)
        .frame(minWidth: UILayoutConstants.ProductReview.reviewCountWidth, alignment: .trailing)
    }
    .frame(maxWidth: .infinity)
    .accessibilityElement(children: .ignore)
    .accessibilityAddTraits(.isStaticText)
    .accessibilityLabel(ogL10n.ProductDetail.Review.Bar.Accessibility(count: String(count), rating: String(rating)))
  }

  private var bar: some View {
    Rectangle()
      .fill(OGColors.backgroundBackground10.color)
      .cornerRadius(UILayoutConstants.ProductReview.cornerRadius)
      .frame(height: UILayoutConstants.ProductReview.ratingBarHeight)
      .background {
        GeometryReader { proxy in
          Color.clear
            .onChange(of: proxy.size) { size in
              barWidth = size.width
            }
            .onAppear {
              barWidth = proxy.size.width
            }
        }
      }
      .overlay(alignment: .leading) {
        Rectangle()
          .fill(OGColors.backgroundBackground100.color)
          .frame(width: barWidth * (percentage / 100))
          .cornerRadius(UILayoutConstants.ProductReview.cornerRadius)
      }
  }
}
