import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

struct ProductSummaryView: SwiftUI.View {
  let reviewInfo: ReviewsInformation

  var body: some SwiftUI.View {
    VStack(alignment: .leading, spacing: .zero) {
      VStack(alignment: .leading, spacing: .zero) {
        if let brandName = reviewInfo.brandName {
          Text(brandName)
            .font(for: .copyS)
            .foregroundColor(OGColors.textOnLight.color)
        }
        Text(reviewInfo.title)
          .font(for: .titleM)
          .foregroundColor(OGColors.textOnLight.color)
      }
      .accessibilityElement(children: .combine)

      if let rating = reviewInfo.rating {
        AdaptiveHStack {
          Text(String(format: "%.1f", locale: Locale.current, rating.averageRating))
            .font(for: .headlineXXL)
            .foregroundColor(OGColors.textOnLight.color)
          RatingView(rating: .init(
            averageRating: rating.averageRating,
            numberOfRatings: .zero
          ), size: .large)
            .accessibilityHidden(true)
          Text(ogL10n.ProductReview.Rating.ReviewCount(count: "\(rating.count)"))
            .font(for: .copyS)
            .foregroundColor(OGColors.backgroundBackground60.color)
        }
        .accessibilityElement(children: .ignore)
        .accessibilityLabel(ogL10n.ProductDetail.Review.Summery.Accessibility(rating: String(format: "%.1f", locale: Locale.current, rating.averageRating), count: String(rating.count)))
        .accessibilityAddTraits(.isStaticText)
        .padding(.top, UILayoutConstants.Default.padding2x)
      }

      Text(ogL10n.ProductReview.Rating.Disclaimer)
        .font(for: .copyS)
        .foregroundColor(OGColors.textOnLight.color)
        .padding(.top, UILayoutConstants.Default.padding2x)
    }
  }
}
