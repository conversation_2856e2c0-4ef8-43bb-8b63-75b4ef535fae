import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

// MARK: - ProductReviewHeaderView

struct ProductReviewHeaderView: SwiftUI.View {
  let review: Review

  var body: some SwiftUI.View {
    HStack(alignment: .top) {
      RatingView(simpleRating: review.rating, size: .medium)
        .accessibilityHidden(true)

      Spacer()

      Text(review.formattedDate)
        .font(for: .copyS)
        .foregroundColor(OGColors.textBlack60.color)
    }
    .accessibilityElement(children: .ignore)
    .accessibilityAddTraits(.isStaticText)
  }
}

extension Review {
  public var formattedDate: String {
    if let dateTime {
      let dateFormatter = DateFormatter()
      dateFormatter.dateStyle = .medium
      return dateFormatter.string(from: Date(timeIntervalSince1970: TimeInterval(dateTime.epochSeconds)))
    } else {
      return ""
    }
  }
}
