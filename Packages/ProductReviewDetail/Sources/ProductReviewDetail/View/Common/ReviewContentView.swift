import OGAppKitSDK
import OGL10n
import SwiftUI
import UICatalog

// MARK: - ReviewContentView

struct ReviewContentView: SwiftUI.View {
  let reviews: [Review]
  let expandedReviewIndex: Int?
  var body: some SwiftUI.View {
    VStack(spacing: UILayoutConstants.ProductReview.verticalSpacing) {
      ForEach(Array(reviews.enumerated()), id: \.offset) { index, review in
        ProductReviewCardView(
          review: review,
          isExpanded: expandedReviewIndex == index,
          minHeight: .zero
        )
        .id(index)
      }
    }
  }
}

// MARK: - UILayoutConstants.ReviewContentView

extension UILayoutConstants {
  enum ReviewContentView {
    static let minHeight: CGFloat = 168
  }
}
