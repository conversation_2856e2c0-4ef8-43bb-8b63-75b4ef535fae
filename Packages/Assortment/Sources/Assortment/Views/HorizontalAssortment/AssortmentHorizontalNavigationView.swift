import AppCore
import OGL10n
import SwiftUI
import UICatalog

struct AssortmentHorizontalNavigationView: View {
  @StateObject private var viewStore: AssortmentHorizontalNavigationView.Store

  @SwiftUI.State private var selectedTabTitle = ""
  @SwiftUI.State private var tabsBarSize = CGSize.zero
  @EnvironmentObject private var deviceState: DeviceState

  init(
    viewStore: Self.Store = AssortmentHorizontalNavigationView.make()
  ) {
    self._viewStore = StateObject(wrappedValue: viewStore)
  }

  var body: some View {
    ScrollView(showsIndicators: false) {
      VStack(spacing: 0) {
        header
        listContent()
      }
    }
    .padding(.top, deviceState.isPhoneInLandsape ? 0 : tabsBarSize.height)
    .if(viewStore.selectedTabEntry != nil) { view in
      view
        .accessibilityElement(children: .contain)
        .accessibilityLabel(ogL10n.General.List.Items.Accessibility(amount: String(viewStore.selectedTabEntry?.childEntries.count ?? 0)))
    }
  }

  @ViewBuilder private var header: some View {
    if !deviceState.isPhoneInLandsape {
      AssortmentHorizontalTeaserView()
    }
    AssortmentHorizontalTabsBarView(selectedTabTitle: $selectedTabTitle)
  }

  @ViewBuilder
  private func listContent() -> some View {
    if let selectedTabEntry = viewStore.selectedTabEntry {
      childEntries(for: selectedTabEntry)
        .id(selectedTabEntry.id)
    }
    secondaryLevelEntries()
  }

  @ViewBuilder
  private func childEntries(for selectedTab: AssortmentState.NavigationEntry) -> some View {
    VStack(spacing: .zero) {
      ForEach(selectedTab.childEntries.indices, id: \.self) { index in
        if let entry = selectedTab.childEntries[safe: index] {
          AssortmentItemView(
            index: index,
            count: selectedTab.childEntries.count,
            navigationEntry: entry
          )
          .background(OGColors.backgroundBackground0.color)
        }
      }
    }
    .accessibilityElement(children: .contain)
    .accessibilityLabel(ogL10n.General.List.Items.Accessibility(amount: String(selectedTab.childEntries.count)))
  }

  @ViewBuilder
  private func secondaryLevelEntries() -> some View {
    VStack(spacing: UILayoutConstants.VerticalAssortmentRootView.itemSpacing) {
      ForEach(viewStore.secondaryLevelEntries.indices, id: \.self) { index in
        if let entry = viewStore.secondaryLevelEntries[safe: index] {
          AssortmentItemView(
            index: index,
            count: viewStore.secondaryLevelEntries.count,
            navigationEntry: entry
          )
          .background(OGColors.backgroundBackground0.color)
        }
      }
    }
    .accessibilityElement(children: .contain)
    .accessibilityLabel(ogL10n.General.List.Items.Accessibility(amount: String(viewStore.secondaryLevelEntries.count)))
  }
}
