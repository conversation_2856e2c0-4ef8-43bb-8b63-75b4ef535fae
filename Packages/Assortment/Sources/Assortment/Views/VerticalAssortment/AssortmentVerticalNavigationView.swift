import OGL10n
import SwiftUI
import UICatalog

// MARK: - AssortmentVerticalNavigationView

struct AssortmentVerticalNavigationView: View {
  @StateObject private var viewStore: AssortmentVerticalNavigationView.Store

  init(
    viewStore: Self.Store = AssortmentVerticalNavigationView.make()
  ) {
    self._viewStore = StateObject(wrappedValue: viewStore)
  }

  var body: some View {
    ScrollView(showsIndicators: false) {
      content()
    }
  }

  @ViewBuilder
  func content() -> some View {
    VStack(spacing: UILayoutConstants.VerticalAssortmentRootView.itemSpacing) {
      ForEach(viewStore.navigationEntries.indices, id: \.self) { index in
        if let entry = viewStore.navigationEntries[safe: index] {
          AssortmentItemView(
            index: index,
            count: viewStore.navigationEntries.count,
            navigationEntry: entry
          )
        }
      }
      .accessibilityElement(children: .contain)
      .accessibilityLabel(ogL10n.General.List.Items.Accessibility(amount: "\(viewStore.navigationEntries.count)"))
    }
  }
}

extension Array {
  public subscript(safe index: Int) -> Element? {
    guard index >= 0, index < endIndex else {
      return nil
    }
    return self[index]
  }
}

// MARK: - UILayoutConstants.VerticalAssortmentRootView

extension UILayoutConstants {
  enum VerticalAssortmentRootView {
    static let itemSpacing = 2.0
  }
}
