import SwiftUI
import UICatalog

// MARK: - ScrollViewOffsetPreferenceKey

struct ScrollViewOffsetPreferenceKey: PreferenceKey {
  static var defaultValue = CGFloat.zero

  static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
    value += nextValue()
  }
}

// MARK: - ScrollViewHeightPreferenceKey

struct ScrollViewHeightPreferenceKey: PreferenceKey {
  static var defaultValue = CGFloat.zero

  static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {}
}

// MARK: - ScrollViewContentHeightPreferenceKey

struct ScrollViewContentHeightPreferenceKey: PreferenceKey {
  static var defaultValue = CGFloat.zero

  static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {}
}

// MARK: - ObservableScrollView

struct ObservableScrollView<Content>: View where Content: View {
  @Namespace var scrollSpace

  @Binding var scrollOffset: CGFloat
  let content: (ScrollViewProxy) -> Content
  let showsIndicators: Bool
  @State var shouldAddSpacer = false
  @State var viewHeight = 0.0
  @State var contentHeight = 0.0
  init(
    scrollOffset: Binding<CGFloat> = .constant(0),
    showsIndicators: Bool = true,
    @ViewBuilder content: @escaping (ScrollViewProxy) -> Content
  ) {
    _scrollOffset = scrollOffset
    self.showsIndicators = showsIndicators
    self.content = content
  }

  var body: some View {
    ScrollView(showsIndicators: showsIndicators) {
      ScrollViewReader { proxy in
        content(proxy)
          .background(
            GeometryReader { geometry in
              let offset = geometry.frame(in: .named(scrollSpace)).minY
              let height = geometry.frame(in: .named(scrollSpace)).height
              Color.clear
                .preference(key: ScrollViewOffsetPreferenceKey.self, value: offset)
                .preference(key: ScrollViewContentHeightPreferenceKey.self, value: height)
            })
        if shouldAddSpacer {
          Spacer(minLength: UILayoutConstants.ObservableScrollView.spacerHeight)
        }
      }
    }
    .coordinateSpace(name: scrollSpace)
    .background(
      GeometryReader { geometry in
        let height = geometry.frame(in: .local).height
        Color.clear
          .preference(key: ScrollViewHeightPreferenceKey.self, value: height)
      })
    .onPreferenceChange(ScrollViewOffsetPreferenceKey.self) { value in
      scrollOffset = value
    }
    .onPreferenceChange(ScrollViewHeightPreferenceKey.self) { value in
      if !shouldAddSpacer {
        shouldAddSpacer = contentHeight >= value
      }
      viewHeight = value
    }
    .onPreferenceChange(ScrollViewContentHeightPreferenceKey.self) { value in
      if !shouldAddSpacer {
        shouldAddSpacer = value >= viewHeight
      }
      contentHeight = value
    }
  }
}

// MARK: - UILayoutConstants.ObservableScrollView

extension UILayoutConstants {
  public enum ObservableScrollView {
    public static let spacerHeight: CGFloat = 100
  }
}
