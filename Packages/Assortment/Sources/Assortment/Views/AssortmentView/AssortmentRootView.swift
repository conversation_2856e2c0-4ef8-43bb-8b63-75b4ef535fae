import OGNavigationBar
import Search
import SwiftUI
import UICatalog

// MARK: - AssortmentRootView

struct AssortmentRootView: View {
  @StateObject private var viewStore: Self.Store

  private let title: String

  public init(
    title: String,
    viewStore: Self.Store = AssortmentRootView.make()
  ) {
    self.title = title
    self._viewStore = StateObject(wrappedValue: viewStore)
  }

  var body: some View {
    if viewStore.isEnabled {
      OGNavigationBar(
        titleView: titleView,
        hasSearchBar: OGObservedBoolean(true),
        content: {
          content()
        }
      )
      .onAppear {
        Task {
          await viewStore.dispatch(.didAppear)
        }
      }
    }
  }

  private var titleView: some View {
    Text(title)
  }

  @ViewBuilder
  private func content() -> some View {
    if viewStore.hasTabEntries {
      AssortmentHorizontalNavigationView()
    } else {
      AssortmentVerticalNavigationView()
    }
  }
}

// MARK: - AssortmentRootView_Previews

struct AssortmentRootView_Previews: PreviewProvider {
  static var previews: some View {
    AssortmentRootView(title: "Assortment", viewStore: AssortmentRootView.make())
  }
}
