# Generated with bin/generate-deployment.sh

opt_out_usage

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

# When missing exact version (e.g. ensure_xcode_version(version: "15.0.1")), the version value defaults to the value specified in the .xcode-version file
ensure_xcode_version(version: "16.4")
default_platform(:ios)

# Globals
$xcodeproj = "OGBlossom.xcodeproj"
$saucelabs_region = 'eu-central-1'
$git_url_certificates = "https://github.com/aacml/og-dx_aac-ios-match_certificates.git"
$slack_channel = "#cd-ios"

# Accessing an environment variable in a lane
should_upload_to_saucelabs = ENV['SHOULD_UPLOAD_TO_SAUCELABS'] == 'true'

platform :ios do

  before_all do |lane|
    lane_name = Actions.lane_context[SharedValues::LANE_NAME].delete_prefix("ios ")
    target = lane_name.split("_").first
    # TODO improve based on config
    ensure_env_vars(env_vars: [
      "SECRET_KEYCHAIN_PASSWORD",
      "SECRET_KEYSTORE_PASSWORD",
      "FIREBASE_CLI_TOKEN",
      "FASTLANE_USER",
      "FASTLANE_PASSWORD",
      "FASTLANE_APPLE_APPLICATION_SPECIFIC_PASSWORD"
    ])
    unless lane_name.start_with?("match_")
      ensure_git_status_clean(show_uncommitted_changes: true)
      unless lane_name.start_with?("Packages_")
				version_number = get_version_number(xcodeproj: $xcodeproj, target: target)
			end
      build_number = get_build_number()
    end
  end

{% for targetID in targets %}
{% set target targets[targetID] %} 
{% for configID in Configurations %}
  {% set config target.configs[configID] %}
  desc "[{{ targetID }} {{ configID|scheme }}] Build & deploy"
  lane :{{ targetID }}_{{ configID }} do |options|
    should_upload_to_saucelabs = options[:should_upload_to_saucelabs] || false
  	build_lane(
  		"{{ config["PRODUCT_BUNDLE_IDENTIFIER"] }}", # bundle identifier
      "{% if config["NOTIFICATION_SERVICE_BUNDLE_IDENTIFIER"] %}{{ config["NOTIFICATION_SERVICE_BUNDLE_IDENTIFIER"] }}{% else %}{{ config["PRODUCT_BUNDLE_IDENTIFIER"] }}.NotificationService{% endif %}", # notification bundle identifier
  		"{{ targetID }} {{ configID|scheme }}", # scheme
  		"{{ configID|method }}", # method
      {% if config["MATCH_BRANCH"] %}"{{ config["MATCH_BRANCH"]}}"{% else %}"enterprise"{% endif %}, # match branch
      {% if configID|isAppStoreVersion %}
      true, # is App Store version
      nil, # Google App ID
      nil, # Firebase QA group
      "{{ config["APP_ID"] }}", # Apple ID
      "{{ config["SHORT_NAME"] }}" # Short Name
      {% else %}
      false, # is App Store version
      "{{ config["GOOGLE_APP_ID"] }}", # Google App ID
      {% if config["FIREBASE_QA_ID"] %}"{{ config["FIREBASE_QA_ID"] }}, mobile_lab_qa"{% else %}"mobile_lab_qa"{% endif %}, # Firebase QA group
      nil,
      nil,
      should_upload_to_saucelabs
      {% endif %}
  	)
  end

  desc "[{{ targetID }} {{ configID|scheme }}] Get certificate & provisioning profile"
  lane :match_{{ targetID }}_{{ configID }} do 
    match(type: {% if configID|isAppStoreVersion %}"appstore"{% else %}"enterprise"{% endif %},
          team_id: "{{ config["DEVELOPMENT_TEAM"] }}",
          username: "<EMAIL>",
          force: true,
          git_branch: {% if config["MATCH_BRANCH"] %}"{{ config["MATCH_BRANCH"]}}"{% else %}"enterprise"{% endif %},
          app_identifier: "{{ config["PRODUCT_BUNDLE_IDENTIFIER"] }},{% if config["NOTIFICATION_SERVICE_BUNDLE_IDENTIFIER"] %}{{ config["NOTIFICATION_SERVICE_BUNDLE_IDENTIFIER"] }}{% else %}{{ config["PRODUCT_BUNDLE_IDENTIFIER"] }}.NotificationService{% endif %}",
          clone_branch_directly: true)
  end

{% endfor %}

  desc "[{{ targetID }} Alpha] tests"
  lane :{{ targetID }}_tests do
    run_tests(devices: ["iPhone 16"], scheme: "{{ targetID }} Alpha (Debug)")
  end

{% endfor %}

  desc "[Packages] tests"
  lane :Packages_tests do
    run_tests(devices: ["iPhone 16"], scheme: "PackagesTestHosts")
  end
  
  def build_lane(bundle_id, notification_bundle_id, scheme, method, match_branch, is_appstore_version, firebase_app_id, firebase_qa_group, apple_id, short_name, should_upload_to_saucelabs = false)
    lane_name = Actions.lane_context[SharedValues::LANE_NAME].delete_prefix("ios ")
    target = lane_name.split("_").first
    version_number = Actions.lane_context[SharedValues::VERSION_NUMBER]
    build_number = get_build_number()

    changelog = changelog_from_git_commits(tag_match_pattern: "#{lane_name}/#{version_number}/*",
    merge_commit_filtering: "exclude_merges")
    Actions.lane_context["OG_CHANGELOG"] = changelog
    unless is_appstore_version
      is_beta = scheme.end_with? "Beta"
     # add_badge(glob: "/Apps/#{target}/Resources/Assets.xcassets/launcher1024x1024Plain.appiconset/*.{png,PNG}",
     # alpha: is_beta ? "false" : true,
     # shield: "#{version_number}-#{build_number}-blue", 
     # shield_parameters: "style=flat-square",
     # shield_gravity: "North")
    end

     # Use should_upload_to_saucelabs instead of should_build_simulator
    if should_upload_to_saucelabs

      file_name = "#{target}_#{bundle_id}_#{version_number}-#{build_number}.app"

      user = sh("whoami").chomp
      out_path = "build/#{target}.app"

      xcodebuild(
        project: $xcodeproj,
        scheme: scheme,
        xcargs: "-destination 'generic/platform=iOS Simulator'", # Simulator devices not available on CI
      )

      # Can't use custom derived data path because lint project script fails the build.
      # Find the latest output file in derived data.
      puts "🔭 Searchig for #{target}.app in DerivedData"
      command = "cd /Users/<USER>/Library/Developer/Xcode/; " + # In the directory above DerivedData
      "find . -name \"#{target}.app\" -print0" + # Find the app build.
      "| xargs -0 ls . -1 -t " + # List the found files by most recent.
      "| grep \"#{target}.app:\" " + # Find the line containing the build.
      "| head -1" # Select the top one.
      path = sh(command)
      # ./DerivedData/Build/Products/Alpha-iphonesimulator/AMB.app: & CI path may vary.
      path = path.chomp.split('./').last.split(':').first
      
      app_derived_data_path = "/Users/<USER>/Library/Developer/Xcode/#{path}"
      puts "✅ Found \"#{target}.app\" path: \"#{app_derived_data_path}\""
      
      puts "💾 Moving \"#{target}.app\" to the build directory."
      sh("rm -Rf ../build") # sh is in "fastlane" dir.
      sh("mkdir -p ../build")
      sh("cp -R #{app_derived_data_path} ../build/")
      puts "✅ Moved the build file to \"build/#{target}.app\"."

      puts "🗜️ Zipping \"build/#{target}.app\" to \"build/#{file_name}.zip\""
      zip(path: out_path, output_path: "build/#{file_name}.zip")
      puts "✅ Finished archiving \"build/#{file_name}.zip\"."

      # https://github.com/cloudkats/fastlane-plugin-saucelabs 
      ensure_env_vars(env_vars: [
        "SAUCE_USERNAME",
        "SAUCE_ACCESS_KEY"
      ])
      saucelabs_upload(
        user_name: ENV['SAUCE_USERNAME'],
        api_key: ENV['SAUCE_ACCESS_KEY'],
        app_name: "#{file_name}.zip",
        app_description: "#{file_name}",
        file: "build/#{file_name}.zip",
        region: $saucelabs_region
      )
      sh("rm -Rf ../build")

      end 
      # Build Alpha, Beta or Release
      file_name = "#{bundle_id}_#{version_number}-#{build_number}.ipa"

      run_match(is_ci, bundle_id, notification_bundle_id, is_appstore_version, match_branch)

      gym(project: $xcodeproj, 
        scheme: scheme, 
        clean: false,
        export_method: method,
        export_xcargs: "-allowProvisioningUpdates",
        output_name: file_name)

      if ENV["SKIP_DEPLOY"]
        puts "💡 Skipping deploy (ENV[\"SKIP_DEPLOY\"] is set)."
        return
      end
      # NOTE: The following command does not work with Xcode 15 in GitHub Actions,
      # although it correctly finds the path locally.
      # Using a copy of upload-symbol for now.
      # uploadSymbolsPath=`xcodebuild -project ../OGBlossom.xcodeproj -showBuildSettings | grep -m 1 "BUILD_DIR" | grep -oEi "\/.*" | sed 's:Build/Products:SourcePackages/checkouts/firebase-ios-sdk/Crashlytics/upload-symbols:' | tr -d '\n'`
     
      unless firebase_app_id.nil?
        current_branch = git_branch
        release_notes = "BRANCH: #{current_branch}\n### CHANGELOG\n#{changelog}"[0..300]
        firebase_app_distribution(app: firebase_app_id,
                                  firebase_cli_token: ENV["FIREBASE_CLI_TOKEN"],
                                  release_notes: release_notes,
                                  groups: firebase_qa_group)
        is_beta = scheme.end_with? "Beta"
        if is_beta
          upload_symbols_to_crashlytics(
            gsp_path: "Apps/#{target}/Firebase/beta",
            binary_path: './bin/upload-symbols'
          )
        else
          upload_symbols_to_crashlytics(
            gsp_path: "Apps/#{target}/Firebase/alpha",
            binary_path: './bin/upload-symbols'
          )
        end
      end
      if is_appstore_version
        lane_context[SharedValues::FL_CHANGELOG] = nil
        upload_symbols_to_crashlytics(
          gsp_path: "Apps/#{target}/Firebase/release",
            binary_path: './bin/upload-symbols'
        )
        upload_to_testflight(username: ENV["FASTLANE_USER"],
                             apple_id: apple_id,
                             itc_provider: short_name,
                             app_identifier: bundle_id,
                             skip_submission: true,
                             skip_waiting_for_build_processing: true)
      end
   
    if is_ci
      sh("git config user.name 'Otto Group One.O Mobile Lab'")
      sh("git config user.email '<EMAIL>'")
    end

    tag = "#{lane_name}/#{version_number}/#{build_number}"
    unless git_tag_exists(tag: tag)
      add_git_tag(tag: tag)
      push_git_tags(tag: tag)
    end
  end

  desc "Report successful build"
  after_all do |lane|
    lane_name = Actions.lane_context[SharedValues::LANE_NAME].delete_prefix("ios ")
    target = lane_name.split("_").first
    config = lane_name.split("_")[1].capitalize
    version_number = Actions.lane_context[SharedValues::VERSION_NUMBER]
    build_number = get_build_number()
    if lane_name.start_with?("match_") || ENV["SKIP_DEPLOY"] || lane_name.end_with?("alpha") || lane_name.end_with?("tests")
      next
    end
    if ENV["SLACK_URL"] 
      slack(pretext: ":white_check_mark: #{target} #{config} deployment was successful!",
            success: true,
            channel: $slack_channel,
            username: "#{target} via Blossom",
            payload: {
              "Build" => "#{version_number}-#{build_number} (#{config})",
              "Branch" => git_branch,
              "Commit" => last_git_commit[:abbreviated_commit_hash],
              #"Download" => Actions.lane_context["download_link"],
              "Changelog" =>  Actions.lane_context["OG_CHANGELOG"]
            },
            default_payloads: [])
      clean_build_artifacts
      reset_git_repo(disregard_gitignore: false, skip_clean: true)
    end
  end

  desc "Report build error"
  error do |lane, exception|
    lane_name = Actions.lane_context[SharedValues::LANE_NAME].delete_prefix("ios ")
    target = lane_name.split("_").first
    config = lane_name.split("_")[1].capitalize
    version_number = Actions.lane_context[SharedValues::VERSION_NUMBER]
    build_number = get_build_number()
    if lane_name.start_with?("match_")
      next
    end
    if ENV["SLACK_URL"] 
      slack(pretext: ":no_entry_sign: #{target} #{config} deployment failed.",
            success: false,
            channel: $slack_channel,
            username: "#{target} via Blossom",
            payload: {
              "Build" => "#{version_number}-#{build_number} (#{config})",
              "Branch" => git_branch,
              "Commit" => last_git_commit[:abbreviated_commit_hash],
              "Error" => exception
              #"Changelog" =>  Actions.lane_context["OG_CHANGELOG"]
            },
            default_payloads: [])
    end
  end    

  def run_match(is_ci, bundle_id, notification_bundle_id, is_appstore_version, match_branch)
    if is_ci
        create_keychain(name: "temp_keychain", password: ENV["MATCH_KEYCHAIN_PASSWORD"], default_keychain: true, unlock: true, timeout: 3600, lock_when_sleeps: false)
        match(type: is_appstore_version ? "appstore" : "enterprise", 
          git_url: $git_url_certificates,
          git_branch: match_branch, 
          app_identifier: "#{bundle_id},#{notification_bundle_id}",
          force_for_new_devices: false,
          readonly: true, 
          keychain_name: "temp_keychain", 
          keychain_password: ENV["MATCH_KEYCHAIN_PASSWORD"])
      else 
        match(type: is_appstore_version ? "appstore" : "enterprise", 
          git_branch: match_branch, 
          app_identifier: "#{bundle_id},#{notification_bundle_id}",
          force_for_new_devices: false,
          readonly: true)
    end
  end

  def get_build_number
      sh("../bin/get-build-number.sh").chomp
  end

  def last_ios_version
    json = JSON.parse(`xcrun simctl list -j`)
    json['runtimes'].last['version']
  end

  def ios_device_name(ios_version)
    json = JSON.parse(`xcrun simctl list -j`)
    device = json['devices']["com.apple.CoreSimulator.SimRuntime.iOS-#{ios_version.gsub('.', '-')}"]
    {# device = device.filter do |x|
      x['isAvailable']
    end #}
    device = device.first['name']
    device
  end

end
